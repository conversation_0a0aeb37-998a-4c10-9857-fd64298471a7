import 'package:flutter/foundation.dart';
import '../models/child_profile.dart';
import '../../../features/story/services/story_progress_service.dart';

/// Provider to manage the currently active child profile
class ActiveChildProvider extends ChangeNotifier {
  /// The currently active child profile
  ChildProfile? _activeProfile;

  /// List of all child profiles
  final List<ChildProfile> _profiles = [];

  /// Get the active profile (may be null if none selected)
  ChildProfile? get activeProfile => _activeProfile;

  /// Get all profiles
  List<ChildProfile> get profiles => List.unmodifiable(_profiles);

  /// Constructor with optional default profile
  ActiveChildProvider({ChildProfile? defaultProfile}) {
    if (defaultProfile != null) {
      _profiles.add(defaultProfile);
      _activeProfile = defaultProfile;

      // Save the active profile ID to shared preferences
      StoryProgressService.saveActiveChildId(defaultProfile.id);
    }

    // Load profiles and active profile from shared preferences
    _loadFromSharedPreferences();
  }

  /// Load profiles and active profile from shared preferences
  Future<void> _loadFromSharedPreferences() async {
    try {
      // Load the active profile ID
      final activeChildId = await StoryProgressService.loadActiveChildId();

      if (activeChildId != null && _activeProfile?.id != activeChildId) {
        // Find the profile with the active ID
        final activeProfile = _profiles.firstWhere(
          (profile) => profile.id == activeChildId,
          orElse: () => throw Exception('Active profile not found'),
        );

        _activeProfile = activeProfile;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading from shared preferences: $e');
    }
  }

  /// Set the active profile by ID
  Future<void> setActiveProfile(String profileId) async {
    final profile = _profiles.firstWhere(
      (profile) => profile.id == profileId,
      orElse: () => throw Exception('Profile not found'),
    );

    _activeProfile = profile;

    // Save the active profile ID to shared preferences
    await StoryProgressService.saveActiveChildId(profileId);

    notifyListeners();
  }

  /// Add a new profile
  void addProfile(ChildProfile profile) {
    if (_profiles.any((p) => p.id == profile.id)) {
      throw Exception('Profile with this ID already exists');
    }

    _profiles.add(profile);

    // If this is the first profile, make it active
    if (_profiles.length == 1) {
      _activeProfile = profile;
    }

    notifyListeners();
  }

  /// Update an existing profile
  void updateProfile(ChildProfile updatedProfile) {
    final index = _profiles.indexWhere((p) => p.id == updatedProfile.id);

    if (index == -1) {
      throw Exception('Profile not found');
    }

    _profiles[index] = updatedProfile;

    // If this was the active profile, update it
    if (_activeProfile?.id == updatedProfile.id) {
      _activeProfile = updatedProfile;
    }

    notifyListeners();
  }

  /// Remove a profile
  void removeProfile(String profileId) {
    final index = _profiles.indexWhere((p) => p.id == profileId);

    if (index == -1) {
      throw Exception('Profile not found');
    }

    final removedProfile = _profiles.removeAt(index);

    // If this was the active profile, set active to null or the first available
    if (_activeProfile?.id == removedProfile.id) {
      _activeProfile = _profiles.isNotEmpty ? _profiles.first : null;
    }

    notifyListeners();
  }

  /// Update the last story and scene for the active profile
  Future<void> updateLastStory(String storyId, String sceneId, {List<String>? choiceHistory}) async {
    if (_activeProfile == null) {
      return;
    }

    // Update story progress in the profile
    final updatedProfile = _activeProfile!.updateStoryProgress(storyId, sceneId);
    updateProfile(updatedProfile);

    // Save progress to shared preferences (for backward compatibility)
    await StoryProgressService.saveProgress(
      childId: _activeProfile!.id,
      storyId: storyId,
      sceneId: sceneId,
      choiceHistory: choiceHistory,
    );

    // Sync to cloud if available
    if (StoryProgressService.isCloudSyncAvailable) {
      await StoryProgressService.saveProgressToCloud(updatedProfile);
    }
  }

  /// Check if the active profile has a story to resume
  Future<bool> hasStoryToResume() async {
    if (_activeProfile == null) {
      return false;
    }

    final progress = await StoryProgressService.loadProgress(
      childId: _activeProfile!.id,
    );

    return progress != null;
  }

  /// Get the last story progress for the active profile
  Future<Map<String, dynamic>?> getLastStoryProgress() async {
    if (_activeProfile == null) {
      return null;
    }

    return StoryProgressService.loadProgress(
      childId: _activeProfile!.id,
    );
  }

  /// Clear the last story progress for the active profile
  Future<void> clearLastStoryProgress({String? storyId}) async {
    if (_activeProfile == null) {
      return;
    }

    await StoryProgressService.clearProgress(
      childId: _activeProfile!.id,
      storyId: storyId,
    );

    // If clearing a specific story, remove it from story progress
    if (storyId != null && _activeProfile!.storyProgress.containsKey(storyId)) {
      final updatedProgress = Map<String, StoryProgress>.from(_activeProfile!.storyProgress);
      updatedProgress.remove(storyId);

      final updatedProfile = _activeProfile!.copyWith(
        storyProgress: updatedProgress,
        lastActive: DateTime.now(),
      );

      updateProfile(updatedProfile);
    }

    await StoryProgressService.clearProgress(
      childId: _activeProfile!.id,
      storyId: storyId,
    );
  }

  /// Sync profile data with cloud
  Future<bool> syncWithCloud() async {
    if (_activeProfile == null || !StoryProgressService.isCloudSyncAvailable) {
      return false;
    }

    try {
      // Try to sync offline progress to cloud
      final success = await StoryProgressService.syncOfflineProgressToCloud(_activeProfile!);

      if (success) {
        // Update last sync timestamp
        await StoryProgressService.setLastSyncTimestamp(_activeProfile!.id);
      }

      return success;
    } catch (e) {
      debugPrint('Error syncing with cloud: $e');
      return false;
    }
  }

  /// Load profile from cloud
  Future<bool> loadFromCloud(String childId) async {
    if (!StoryProgressService.isCloudSyncAvailable) {
      return false;
    }

    try {
      final cloudProfile = await StoryProgressService.loadProgressFromCloud(childId);

      if (cloudProfile != null) {
        updateProfile(cloudProfile);

        // Also sync to local storage
        await StoryProgressService.syncCloudProgressToLocal(childId);

        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Error loading from cloud: $e');
      return false;
    }
  }

  /// Check if cloud sync is available
  bool get isCloudSyncAvailable => StoryProgressService.isCloudSyncAvailable;

  /// Get last sync timestamp
  Future<DateTime?> getLastSyncTimestamp() async {
    if (_activeProfile == null) return null;
    return await StoryProgressService.getLastSyncTimestamp(_activeProfile!.id);
  }

  /// Clear the active profile
  void clearProfile() {
    _activeProfile = null;
    notifyListeners();
  }

  /// Check if there's an active profile
  bool get hasActiveProfile => _activeProfile != null;

  /// Save story progress for the active profile
  Future<bool> saveStoryProgress(StoryProgress progress) async {
    if (_activeProfile == null) return false;

    try {
      // Update the profile's story progress
      final updatedProgress = Map<String, StoryProgress>.from(_activeProfile!.storyProgress);
      updatedProgress[progress.storyId] = progress;

      final updatedProfile = _activeProfile!.copyWith(
        storyProgress: updatedProgress,
        lastActive: DateTime.now(),
      );

      updateProfile(updatedProfile);

      // Save to cloud if available
      if (isCloudSyncAvailable) {
        await StoryProgressService.saveProgressToCloud(updatedProfile);
      }

      return true;
    } catch (e) {
      debugPrint('Error saving story progress: $e');
      return false;
    }
  }

  /// Get story progress for a specific story
  StoryProgress? getStoryProgress(String storyId) {
    if (_activeProfile == null) return null;
    return _activeProfile!.storyProgress[storyId];
  }

  /// Update preferences for the active profile
  Future<bool> updatePreferences(Map<String, dynamic> preferences) async {
    if (_activeProfile == null) return false;

    try {
      final updatedProfile = _activeProfile!.copyWith(
        preferences: {..._activeProfile!.preferences, ...preferences},
        lastActive: DateTime.now(),
      );

      updateProfile(updatedProfile);

      // Save to cloud if available
      if (isCloudSyncAvailable) {
        await StoryProgressService.saveProgressToCloud(updatedProfile);
      }

      return true;
    } catch (e) {
      debugPrint('Error updating preferences: $e');
      return false;
    }
  }

  /// Get completed stories count
  int getCompletedStoriesCount() {
    if (_activeProfile == null) return 0;
    return _activeProfile!.storyProgress.values
        .where((progress) => progress.isCompleted)
        .length;
  }

  /// Get total reading time in minutes
  int getTotalReadingTimeMinutes() {
    if (_activeProfile == null) return 0;
    return _activeProfile!.storyProgress.values
        .fold(0, (total, progress) => total + progress.totalTimeMinutes);
  }

  /// Get stories in progress (not completed)
  List<StoryProgress> getStoriesInProgress() {
    if (_activeProfile == null) return [];
    return _activeProfile!.storyProgress.values
        .where((progress) => !progress.isCompleted)
        .toList();
  }

  /// Load profile from local storage
  Future<bool> loadFromLocalStorage(String childId) async {
    try {
      final progress = await StoryProgressService.loadProgress(childId: childId);
      if (progress != null) {
        // Convert legacy progress to profile format if needed
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error loading from local storage: $e');
      return false;
    }
  }

  /// Save profile to local storage
  Future<bool> saveToLocalStorage() async {
    if (_activeProfile == null) return false;

    try {
      // Save each story progress individually for backward compatibility
      for (final progress in _activeProfile!.storyProgress.values) {
        await StoryProgressService.saveProgress(
          childId: _activeProfile!.id,
          storyId: progress.storyId,
          sceneId: progress.currentSceneId,
          choiceHistory: progress.choicesMade.values.toList(),
        );
      }
      return true;
    } catch (e) {
      debugPrint('Error saving to local storage: $e');
      return false;
    }
  }

  /// Clear local storage
  Future<bool> clearLocalStorage() async {
    try {
      if (_activeProfile != null) {
        await StoryProgressService.clearProgress(childId: _activeProfile!.id);
      }
      return true;
    } catch (e) {
      debugPrint('Error clearing local storage: $e');
      return false;
    }
  }
}
