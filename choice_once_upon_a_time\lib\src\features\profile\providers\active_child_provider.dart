import 'package:flutter/foundation.dart';
import '../models/child_profile.dart';
import '../../../features/story/services/story_progress_service.dart';

/// Provider to manage the currently active child profile
class ActiveChildProvider extends ChangeNotifier {
  /// The currently active child profile
  ChildProfile? _activeProfile;

  /// List of all child profiles
  final List<ChildProfile> _profiles = [];

  /// Get the active profile (may be null if none selected)
  ChildProfile? get activeProfile => _activeProfile;

  /// Get all profiles
  List<ChildProfile> get profiles => List.unmodifiable(_profiles);

  /// Constructor with optional default profile
  ActiveChildProvider({ChildProfile? defaultProfile}) {
    if (defaultProfile != null) {
      _profiles.add(defaultProfile);
      _activeProfile = defaultProfile;

      // Save the active profile ID to shared preferences
      StoryProgressService.saveActiveChildId(defaultProfile.id);
    }

    // Load profiles and active profile from shared preferences
    _loadFromSharedPreferences();
  }

  /// Load profiles and active profile from shared preferences
  Future<void> _loadFromSharedPreferences() async {
    try {
      // Load the active profile ID
      final activeChildId = await StoryProgressService.loadActiveChildId();

      if (activeChildId != null && _activeProfile?.id != activeChildId) {
        // Find the profile with the active ID
        final activeProfile = _profiles.firstWhere(
          (profile) => profile.id == activeChildId,
          orElse: () => throw Exception('Active profile not found'),
        );

        _activeProfile = activeProfile;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading from shared preferences: $e');
    }
  }

  /// Set the active profile by ID
  Future<void> setActiveProfile(String profileId) async {
    final profile = _profiles.firstWhere(
      (profile) => profile.id == profileId,
      orElse: () => throw Exception('Profile not found'),
    );

    _activeProfile = profile;

    // Save the active profile ID to shared preferences
    await StoryProgressService.saveActiveChildId(profileId);

    notifyListeners();
  }

  /// Add a new profile
  void addProfile(ChildProfile profile) {
    if (_profiles.any((p) => p.id == profile.id)) {
      throw Exception('Profile with this ID already exists');
    }

    _profiles.add(profile);

    // If this is the first profile, make it active
    if (_profiles.length == 1) {
      _activeProfile = profile;
    }

    notifyListeners();
  }

  /// Update an existing profile
  void updateProfile(ChildProfile updatedProfile) {
    final index = _profiles.indexWhere((p) => p.id == updatedProfile.id);

    if (index == -1) {
      throw Exception('Profile not found');
    }

    _profiles[index] = updatedProfile;

    // If this was the active profile, update it
    if (_activeProfile?.id == updatedProfile.id) {
      _activeProfile = updatedProfile;
    }

    notifyListeners();
  }

  /// Remove a profile
  void removeProfile(String profileId) {
    final index = _profiles.indexWhere((p) => p.id == profileId);

    if (index == -1) {
      throw Exception('Profile not found');
    }

    final removedProfile = _profiles.removeAt(index);

    // If this was the active profile, set active to null or the first available
    if (_activeProfile?.id == removedProfile.id) {
      _activeProfile = _profiles.isNotEmpty ? _profiles.first : null;
    }

    notifyListeners();
  }

  /// Update the last story and scene for the active profile
  Future<void> updateLastStory(String storyId, String sceneId, {List<String>? choiceHistory}) async {
    if (_activeProfile == null) {
      return;
    }

    final updatedProfile = _activeProfile!.copyWith(
      lastStoryId: storyId,
      lastSceneId: sceneId,
    );

    updateProfile(updatedProfile);

    // Save progress to shared preferences
    await StoryProgressService.saveProgress(
      childId: _activeProfile!.id,
      storyId: storyId,
      sceneId: sceneId,
      choiceHistory: choiceHistory,
    );
  }

  /// Check if the active profile has a story to resume
  Future<bool> hasStoryToResume() async {
    if (_activeProfile == null) {
      return false;
    }

    final progress = await StoryProgressService.loadProgress(
      childId: _activeProfile!.id,
    );

    return progress != null;
  }

  /// Get the last story progress for the active profile
  Future<Map<String, dynamic>?> getLastStoryProgress() async {
    if (_activeProfile == null) {
      return null;
    }

    return StoryProgressService.loadProgress(
      childId: _activeProfile!.id,
    );
  }

  /// Clear the last story progress for the active profile
  Future<void> clearLastStoryProgress({String? storyId}) async {
    if (_activeProfile == null) {
      return;
    }

    await StoryProgressService.clearProgress(
      childId: _activeProfile!.id,
      storyId: storyId,
    );

    // If clearing a specific story and it's the current last story,
    // update the profile to remove the reference
    if (storyId != null && _activeProfile!.lastStoryId == storyId) {
      final updatedProfile = _activeProfile!.copyWith(
        lastStoryId: null,
        lastSceneId: null,
      );

      updateProfile(updatedProfile);
    }
  }
}
