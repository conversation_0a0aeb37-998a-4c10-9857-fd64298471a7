{"storyId": "pips-garden-of-giving", "storyTitle": "<PERSON><PERSON>'s Garden of Giving", "targetAge": "4-6 years old", "moralTheme": "Kindness", "characterList": [{"characterName": "<PERSON><PERSON>", "characterDescriptionForVisual": "<PERSON><PERSON> is a tiny field mouse with soft, light brown fur, a small pink nose, large expressive round ears, and bright, curious dark eyes. He consistently wears a little green leaf vest. He should look friendly, gentle, and expressive (happy, thoughtful, sad, etc., depending on the scene)."}, {"characterName": "<PERSON>", "characterDescriptionForVisual": "<PERSON> is a young, gentle rabbit with fluffy white fur, long floppy ears (one ear has a tiny, characteristic notch at the tip), and kind, warm brown eyes. She often looks hopeful or concerned. She might carry a small, empty woven basket initially."}, {"characterName": "Grumbles", "characterDescriptionForVisual": "<PERSON><PERSON><PERSON> is a stout, older badger with classic grey and black striped fur on his face and body. He has a long snout and small, beady eyes that can look grumpy but can also show surprise or gratitude. He walks with a bit of a stoop when carrying things."}, {"characterName": "<PERSON>", "characterDescriptionForVisual": "An elderly, sweet-faced rabbit, with fluffy white fur, possibly slightly greyer than <PERSON>'s. She wears a knitted colorful shawl and glasses. She looks frail but her eyes light up with joy. Often seen sitting in a comfy chair."}], "storyNodes": [{"sceneId": "pip_intro_berries", "narrationText": "<PERSON><PERSON> the little mouse smiled. His very own berry bush was full of big, red, juicy berries! 'These will be so yummy!' he thought, puffing out his chest with pride.", "imageAssetPath": "assets/images/pips-garden-of-giving/pip_intro_berries", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "rosie_sad_choice1", "narrationText": "Just then, his friend <PERSON> the rabbit hopped by. She looked very sad. 'Oh <PERSON><PERSON>,' she sighed, 'my berry bush didn't grow any berries this year, and I wanted to make a pie for <PERSON> Rabbit.'", "imageAssetPath": "assets/images/pips-garden-of-giving/rosie_sad_choice1", "isChoicePoint": true, "choicePrompt": "What should <PERSON><PERSON> do with his berries?", "choices": [{"choiceText": "Share berries with <PERSON>", "nextSceneId": "pip_shares_berries_A"}, {"choiceText": "Keep berries for himself", "nextSceneId": "pip_keeps_berries_B"}], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "pip_shares_berries_A", "narrationText": "<PERSON><PERSON> looked at his shiny berries, then at <PERSON>'s sad face. He knew what to do! '<PERSON>,' he chirped, 'you can have some of my berries! We can pick them together!'", "imageAssetPath": "assets/images/pips-garden-of-giving/pip_shares_berries_a", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "rosie_grateful_invite_A", "narrationText": "<PERSON>'s ears perked up! 'Oh, <PERSON><PERSON>! Would you really? That's so kind of you!' she exclaimed. 'We can make the pie together and take it to <PERSON>!'", "imageAssetPath": "assets/images/pips-garden-of-giving/rosie_grateful_invite_a", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "meet_grumbles_choice_A2", "narrationText": "With a basket full of berries, <PERSON><PERSON> and <PERSON> headed towards Grandma's house. On the path, they saw <PERSON>rumbles the badger. He was huffing and puffing, trying to carry a big pile of sticks.", "imageAssetPath": "assets/images/pips-garden-of-giving/meet_grumbles_choice_a2", "isChoicePoint": true, "choicePrompt": "What should <PERSON><PERSON> and <PERSON> do?", "choices": [{"choiceText": "Help Grumbles with the sticks", "nextSceneId": "help_grumbles_A2a"}, {"choiceText": "Walk past and continue", "nextSceneId": "continue_to_grandma_<PERSON>b"}], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "help_grumbles_A2a", "narrationText": "'He looks like he needs help,' whispered <PERSON><PERSON>. They went up to <PERSON><PERSON><PERSON>. 'Excuse me, Mr. <PERSON>,' said <PERSON> politely, 'can we help you carry your sticks?'", "imageAssetPath": "assets/images/pips-garden-of-giving/help_grumbles_a2a", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "grumbles_grateful_shortcut_A2a", "narrationText": "<PERSON><PERSON><PERSON> blinked his beady eyes. 'Well, I'll be!' he rumbled. 'That's mighty kind of you little ones.' With their help, the sticks felt much lighter. 'As thanks,' said <PERSON><PERSON><PERSON>, 'I know a shortcut to <PERSON>'s!'", "imageAssetPath": "assets/images/pips-garden-of-giving/grumbles_grateful_shortcut_a2a", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "grandma_pie_everyone_happy_ending_A2a", "narrationText": "The shortcut was quick! Soon, a yummy berry pie was baking. <PERSON> was overjoyed with the pie and the company. <PERSON><PERSON>, <PERSON>, and even <PERSON><PERSON><PERSON> (who came by later!) shared smiles. <PERSON><PERSON> felt a warm glow; kindness really did make everyone happy!", "imageAssetPath": "assets/images/pips-garden-of-giving/grandma_pie_everyone_happy_ending_a2a", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": true, "moralLessonReinforced": "Kindness is contagious! Helping others brings unexpected joy and makes everyone, including you, feel wonderful."}, {"sceneId": "continue_to_grandma_<PERSON>b", "narrationText": "<PERSON><PERSON> and <PERSON> decided they should hurry to <PERSON>'s. They quietly walked past <PERSON><PERSON><PERSON>, hoping he would be okay. They soon arrived at <PERSON>'s cozy cottage.", "imageAssetPath": "assets/images/pips-garden-of-giving/continue_to_grandma_a2b", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "grandma_pie_good_feeling_ending_A2b", "narrationText": "<PERSON><PERSON> and <PERSON> made a delicious berry pie. <PERSON> loved it! 'Thank you, <PERSON>, and thank you, kind <PERSON><PERSON>, for sharing your berries,' she said. <PERSON><PERSON> felt good knowing his kindness had made <PERSON> and her Grandma happy.", "imageAssetPath": "assets/images/pips-garden-of-giving/grandma_pie_good_feeling_ending_a2b", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": true, "moralLessonReinforced": "Being kind to a friend in need is a wonderful thing and brings happiness to everyone involved."}, {"sceneId": "pip_keeps_berries_B", "narrationText": "<PERSON><PERSON> looked at his beautiful berries. 'I worked hard to grow these,' he thought. 'I want to eat them all myself.' So he told <PERSON>, 'Sorry, <PERSON>, I need these berries.' <PERSON>'s ears drooped, and she hopped away sadly.", "imageAssetPath": "assets/images/pips-garden-of-giving/pip_keeps_berries_b", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "pip_eats_sees_rosie_again_choice_B2", "narrationText": "<PERSON><PERSON> started eating his berries. They were sweet, but somehow, they didn't taste as good as he expected. He saw <PERSON> in the distance, still looking for berries, and felt a funny feeling in his tummy.", "imageAssetPath": "assets/images/pips-garden-of-giving/pip_eats_sees_rosie_again_choice_b2", "isChoicePoint": true, "choicePrompt": "<PERSON><PERSON> still has berries left. What should he do?", "choices": [{"choiceText": "Find Rosie and share", "nextSceneId": "pip_offers_late_B2a"}, {"choiceText": "Keep eating his berries", "nextSceneId": "pip_ignores_plays_empty_B2b"}], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "pip_offers_late_B2a", "narrationText": "<PERSON><PERSON> didn't like this funny feeling. He grabbed his leftover berries and hurried to <PERSON>. '<PERSON>, I'm sorry,' he said. 'I was being selfish. Would you still like some berries for <PERSON>'s pie?'", "imageAssetPath": "assets/images/pips-garden-of-giving/pip_offers_late_b2a", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "rosie_accepts_pie_relief_ending_B2a", "narrationText": "A big smile spread across <PERSON>'s face. 'Oh, <PERSON><PERSON>! Yes, please! Thank you!' They made a smaller pie, but it was filled with kindness. <PERSON> loved it, and <PERSON><PERSON> felt so much better inside.", "imageAssetPath": "assets/images/pips-garden-of-giving/rosie_accepts_pie_relief_ending_b2a", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": true, "moralLessonReinforced": "It's never too late to be kind! Saying sorry and sharing can make everyone, especially you, feel much happier."}, {"sceneId": "pip_ignores_plays_empty_B2b", "narrationText": "<PERSON><PERSON> decided to forget about <PERSON> and just enjoy his berries. He tried to play, but it wasn't as fun as usual. He kept thinking about <PERSON> and her Grandma.", "imageAssetPath": "assets/images/pips-garden-of-giving/pip_ignores_plays_empty_b2b", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "pip_regrets_missed_kindness_ending_B2b", "narrationText": "Later, <PERSON><PERSON> heard that <PERSON> Rabbit didn't get a berry pie and was a little sad. <PERSON><PERSON> looked at his few remaining berries and felt a pang of regret. He wished he had shared his joy.", "imageAssetPath": "assets/images/pips-garden-of-giving/pip_regrets_missed_kindness_ending_b2b", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": true, "moralLessonReinforced": "Not being kind can make others sad and leave you feeling empty. Sharing happiness makes it grow."}]}