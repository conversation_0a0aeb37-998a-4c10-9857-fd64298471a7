Screen Design Details for "Choice: Once Upon A Time"
This document outlines the UI/UX specifications for various screens in the application.

1. Main Menu Screen
Overall Layout: Captivating background. Responsive split:
Phone (Portrait): Vertical split (Top: Character Card, Bottom: Action Buttons 2x2 or list).
TV/Tablet (Landscape): Left (1/3) Character Card, Right (2/3) Action Buttons.
Left Part (Character Card):
Large, friendly illustrated character (placeholder initially).
"Active Child Profile" name below (e.g., "Leo's Adventures!").
Right Part (Action Buttons): Card style (rounded corners, bg color, icon, text).
Start New Adventure: Icon (open book/wand), Text ("New Story"/"Explore Stories"). Action: To Story Library.
Resume Previous Story: Icon (play button/bookmark), Text ("Continue Story"). Action: To last point in unfinished story (disabled if none).
Surprise Me: Icon (question mark/gift), Text ("Surprise Me!"). Action: Random unlocked story.
Interactive AI: Icon (robot/speech bubble), Text ("Chat with [Character]"/"AI Storyteller"). Action: To "Feature Coming Soon" screen.
Top Right Corner (Settings Cogwheel):
Icon: Cogwheel. Action: Opens Settings overlay/screen.
Options: "Parent Dashboard," "Change Theme," "Active Child Profile," "Sound On/Off."
2. Story Library Screen
Layout: Scrollable grid of story cards. Adaptive columns (Phone: 2, Tablet/TV: 3-4). Visible scrollbar. Header ("Choose Your Adventure!"). Back button.
Story Card Design:
Image: Captivating illustration (first scene/custom cover).
Title: Below image or overlay. Playful, readable font.
Lock Icon: For locked stories (dims card).
"Free" Badge: For free stories.
Interaction: Tap unlocked/free -> Story Interaction. Tap locked -> IAP screen.
TV Navigation: Clear focus indication. Smooth D-pad navigation.
3. Story Interaction Screen
Layout:
Top Area (50-60%): Large static illustration for current scene. Smooth transitions.
Bottom Area (40-50%):
Narration Text Area: Large, legible text. Highlight words with TTS (optional).
Choice Buttons (at choice points): 2-3 card-like buttons with choice text. Appear after narration.
Controls & Indicators:
TTS Control (MVP): Auto-play per scene. "Replay Scene" button (icon: circular arrow).
Back/Exit Button: To Story Library (prompt to save/warn).
Implicit "Next" or tap screen after narration (if no choice).
4. "Resume Previous Story" Information
Not a full screen. Logic to store storyId, currentSceneId for active child profile.
"Resume" button on Main Menu directly loads StoryInteractionScreen at the saved point.
5. "Feature Coming Soon" Screen
Layout: Friendly background.
Central Message: Large text ("Coming Soon!"), smaller description, optional animated character.
Back Button: To Main Menu.
6. Settings Screen/Panel
Layout: Full screen or modal. Title ("Settings"). List of options (icon & text).
Options:
Parent Dashboard: Icon (parent/shield), Text ("Parent Zone"). Action: To Parent Auth.
Active Child Profile: Icon (user/animal face), Text ("Switch Adventurer"). Action: To Child Profile Management. Displays active profile.
Change Theme: Icon (palette), Text ("App Theme"). Action: To Theme Selection.
Sound Settings: Icon (speaker), Text ("Music & Sounds"). Toggles for "Background Music," "UI Sound Effects."
About/Help (Optional MVP): Icon (info), Text ("About Us"). Shows app info.
Close/Back Button: To Main Menu.
7. Parent Authentication Screens (Login/Sign Up)
Accessed via "Parent Dashboard" if not logged in. Themed.
Layout: App logo. Headings ("Parent Sign In"/"Create Parent Account"). Email/Password fields. Buttons ("Sign In," "Create Account"). "Forgot Password?" link. Back button (out of parent section).
Consider a simple "Are you a grown-up?" check.
8. Parent Dashboard Screen (Post-Login)
Layout: Welcome message. Sections/list for controls.
Functionality:
Child Profile Management: Button/Link to Child Profile Management Screen. List existing profiles, show active.
Unlock Full Version / Purchase Status: CTA to unlock (if not purchased) or status message. "Restore Purchases" button.
Account Settings (Optional MVP): Change Password, Logout.
Analytics Opt-in/out (If applicable).
Back Button.
9. Child Profile Management Screen
Layout: Title ("Manage Adventurers").
List of Existing Profiles: Card/list item with name. "Active" indicator. Options: "Select"/"Make Active," "Edit," "Delete" (with confirmation).
"Add New Profile" Button: Prominent. Action: Dialog/screen to enter child's name (MVP: name only).
Back Button: To Parent Dashboard.
10. Theme Selection Screen/Dialog
Layout: Title ("Choose Your Theme!"). Grid/list of theme options.
Each option: Preview image/color swatches, Theme name.
Indication of current theme.
Action: Tap applies theme (or preview with "Apply").
Back/Confirm Button.
11. In-App Purchase (Unlock Full Version) Screen
Often modal.
Title: "Unlock All Stories!"
Value Proposition: Bullet points/icons of benefits. Small montage of locked story illustrations.
Clear Price Display.
CTA Button: "Unlock Now for [Price]."
"Restore Purchases" Button.
Close Button (X).
Small legal text/links.