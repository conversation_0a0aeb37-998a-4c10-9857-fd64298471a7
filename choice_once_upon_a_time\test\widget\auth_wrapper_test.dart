import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../../lib/src/features/auth/widgets/auth_wrapper.dart';
import '../../lib/src/features/auth/screens/parent_auth_screen.dart';
import '../../lib/src/features/main_menu/screens/main_menu_screen.dart';
import '../helpers/test_helpers.dart';
import '../mocks/mock_services.dart';

void main() {
  group('AuthWrapper Widget Tests', () {
    late MockParentAuthProvider mockAuthProvider;
    late MockActiveChildProvider mockActiveChildProvider;

    setUp(() {
      mockAuthProvider = MockParentAuthProvider();
      mockActiveChildProvider = MockActiveChildProvider();
      
      // Set up default mock behaviors
      TestHelpers.setupMockProviders(
        authProvider: mockAuthProvider,
        activeChildProvider: mockActiveChildProvider,
      );
    });

    group('Authentication States', () {
      testWidgets('should show loading screen when auth is loading', (tester) async {
        // Arrange
        when(mockAuthProvider.isLoading).thenReturn(true);
        when(mockAuthProvider.isAuthenticated).thenReturn(false);
        when(mockAuthProvider.errorMessage).thenReturn(null);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const AuthWrapper(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('Loading...'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show error screen when auth has error', (tester) async {
        // Arrange
        when(mockAuthProvider.isLoading).thenReturn(false);
        when(mockAuthProvider.isAuthenticated).thenReturn(false);
        when(mockAuthProvider.errorMessage).thenReturn('Authentication failed');

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const AuthWrapper(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.text('Authentication Error'), findsOneWidget);
        expect(find.text('Authentication failed'), findsOneWidget);
        expect(find.text('Try Again'), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show auth screen when not authenticated', (tester) async {
        // Arrange
        when(mockAuthProvider.isLoading).thenReturn(false);
        when(mockAuthProvider.isAuthenticated).thenReturn(false);
        when(mockAuthProvider.errorMessage).thenReturn(null);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const AuthWrapper(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.byType(ParentAuthScreen), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should clear error when Try Again is tapped', (tester) async {
        // Arrange
        when(mockAuthProvider.isLoading).thenReturn(false);
        when(mockAuthProvider.isAuthenticated).thenReturn(false);
        when(mockAuthProvider.errorMessage).thenReturn('Test error');

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const AuthWrapper(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Act
        await tester.tap(find.text('Try Again'));
        await tester.pump();

        // Assert
        verify(mockAuthProvider.clearError()).called(1);
      });
    });

    group('Authenticated States', () {
      setUp(() {
        when(mockAuthProvider.isLoading).thenReturn(false);
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockAuthProvider.errorMessage).thenReturn(null);
      });

      testWidgets('should show no children screen when no children exist', (tester) async {
        // Arrange
        when(mockAuthProvider.hasChildren).thenReturn(false);
        when(mockAuthProvider.childProfiles).thenReturn([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const AuthWrapper(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.text('Create Your First Child Profile'), findsOneWidget);
        expect(find.text('Create Child Profile'), findsOneWidget);
        expect(find.byIcon(Icons.child_care), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show child selection when no active child', (tester) async {
        // Arrange
        final mockChildren = [
          TestHelpers.createMockChildProfile(id: 'child1', name: 'Child 1'),
          TestHelpers.createMockChildProfile(id: 'child2', name: 'Child 2'),
        ];

        when(mockAuthProvider.hasChildren).thenReturn(true);
        when(mockAuthProvider.childProfiles).thenReturn(mockChildren);
        when(mockActiveChildProvider.activeProfile).thenReturn(null);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const AuthWrapper(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.text('Select Child'), findsOneWidget);
        expect(find.text('Child 1'), findsOneWidget);
        expect(find.text('Child 2'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show main menu when authenticated with active child', (tester) async {
        // Arrange
        final mockChild = TestHelpers.createMockChildProfile(name: 'Test Child');
        final mockChildren = [mockChild];

        when(mockAuthProvider.hasChildren).thenReturn(true);
        when(mockAuthProvider.childProfiles).thenReturn(mockChildren);
        when(mockActiveChildProvider.activeProfile).thenReturn(mockChild);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const AuthWrapper(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.byType(MainMenuScreen), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should handle child selection', (tester) async {
        // Arrange
        final mockChildren = [
          TestHelpers.createMockChildProfile(id: 'child1', name: 'Child 1'),
          TestHelpers.createMockChildProfile(id: 'child2', name: 'Child 2'),
        ];

        when(mockAuthProvider.hasChildren).thenReturn(true);
        when(mockAuthProvider.childProfiles).thenReturn(mockChildren);
        when(mockActiveChildProvider.activeProfile).thenReturn(null);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const AuthWrapper(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Act
        await tester.tap(find.text('Child 1'));
        await tester.pump();

        // Assert
        verify(mockActiveChildProvider.updateProfile(any)).called(1);
      });
    });

    group('Responsive Design', () {
      testWidgets('should handle different screen sizes', (tester) async {
        // Test on multiple screen sizes
        await TestHelpers.testOnAllScreenSizes(
          tester,
          (screenSize) => TestHelpers.createResponsiveTestWidget(
            child: const AuthWrapper(),
            screenSize: screenSize,
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
          (tester, screenSize) async {
            // Arrange
            when(mockAuthProvider.isLoading).thenReturn(false);
            when(mockAuthProvider.isAuthenticated).thenReturn(false);
            when(mockAuthProvider.errorMessage).thenReturn(null);

            await tester.pump();

            // Assert - should show auth screen without overflow
            expect(find.byType(ParentAuthScreen), findsOneWidget);
          },
        );
      });

      testWidgets('should handle loading state on all screen sizes', (tester) async {
        await TestHelpers.testOnAllScreenSizes(
          tester,
          (screenSize) {
            when(mockAuthProvider.isLoading).thenReturn(true);
            return TestHelpers.createResponsiveTestWidget(
              child: const AuthWrapper(),
              screenSize: screenSize,
              authProvider: mockAuthProvider,
              activeChildProvider: mockActiveChildProvider,
            );
          },
          (tester, screenSize) async {
            await tester.pump();
            expect(find.byType(CircularProgressIndicator), findsOneWidget);
          },
        );
      });

      testWidgets('should handle error state on all screen sizes', (tester) async {
        await TestHelpers.testOnAllScreenSizes(
          tester,
          (screenSize) {
            when(mockAuthProvider.isLoading).thenReturn(false);
            when(mockAuthProvider.isAuthenticated).thenReturn(false);
            when(mockAuthProvider.errorMessage).thenReturn('Test error');
            return TestHelpers.createResponsiveTestWidget(
              child: const AuthWrapper(),
              screenSize: screenSize,
              authProvider: mockAuthProvider,
              activeChildProvider: mockActiveChildProvider,
            );
          },
          (tester, screenSize) async {
            await tester.pump();
            expect(find.text('Authentication Error'), findsOneWidget);
            expect(find.text('Test error'), findsOneWidget);
          },
        );
      });
    });

    group('Sign Out Functionality', () {
      testWidgets('should handle sign out from no children screen', (tester) async {
        // Arrange
        when(mockAuthProvider.isLoading).thenReturn(false);
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockAuthProvider.hasChildren).thenReturn(false);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const AuthWrapper(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Act
        await tester.tap(find.byIcon(Icons.logout));
        await tester.pump();

        // Assert
        verify(mockAuthProvider.signOut()).called(1);
      });

      testWidgets('should handle sign out from child selection screen', (tester) async {
        // Arrange
        final mockChildren = [TestHelpers.createMockChildProfile()];
        when(mockAuthProvider.isLoading).thenReturn(false);
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockAuthProvider.hasChildren).thenReturn(true);
        when(mockAuthProvider.childProfiles).thenReturn(mockChildren);
        when(mockActiveChildProvider.activeProfile).thenReturn(null);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const AuthWrapper(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Act
        await tester.tap(find.byIcon(Icons.logout));
        await tester.pump();

        // Assert
        verify(mockAuthProvider.signOut()).called(1);
      });
    });

    group('Create Child Dialog', () {
      testWidgets('should show create child dialog', (tester) async {
        // Arrange
        when(mockAuthProvider.isLoading).thenReturn(false);
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockAuthProvider.hasChildren).thenReturn(false);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const AuthWrapper(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Act
        await tester.tap(find.text('Create Child Profile'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Create Child Profile'), findsWidgets); // Button + Dialog title
        expect(find.text('Child\'s Name'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
        expect(find.text('Create'), findsOneWidget);
      });

      testWidgets('should handle child creation', (tester) async {
        // Arrange
        when(mockAuthProvider.isLoading).thenReturn(false);
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockAuthProvider.hasChildren).thenReturn(false);
        when(mockAuthProvider.createChildProfile(name: anyNamed('name')))
            .thenAnswer((_) async => TestHelpers.createMockChildProfile(name: 'New Child'));

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const AuthWrapper(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        await tester.tap(find.text('Create Child Profile'));
        await tester.pumpAndSettle();

        // Act
        await tester.enterText(find.byType(TextField), 'New Child');
        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockAuthProvider.createChildProfile(name: 'New Child')).called(1);
        verify(mockActiveChildProvider.updateProfile(any)).called(1);
      });

      testWidgets('should handle dialog cancellation', (tester) async {
        // Arrange
        when(mockAuthProvider.isLoading).thenReturn(false);
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockAuthProvider.hasChildren).thenReturn(false);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const AuthWrapper(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        await tester.tap(find.text('Create Child Profile'));
        await tester.pumpAndSettle();

        // Act
        await tester.tap(find.text('Cancel'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Create Child Profile'), findsOneWidget); // Only the button, dialog closed
        verifyNever(mockAuthProvider.createChildProfile(name: anyNamed('name')));
      });
    });

    group('Accessibility', () {
      testWidgets('should meet accessibility guidelines', (tester) async {
        // Arrange
        when(mockAuthProvider.isLoading).thenReturn(false);
        when(mockAuthProvider.isAuthenticated).thenReturn(false);
        when(mockAuthProvider.errorMessage).thenReturn(null);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const AuthWrapper(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        await TestHelpers.verifyAccessibility(tester);
      });
    });
  });
}
