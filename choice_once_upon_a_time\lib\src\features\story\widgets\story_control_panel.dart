import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/story_settings_provider.dart';
import '../services/tts_service.dart';
import '../services/text_segmenter.dart';
import '../widgets/highlighted_text.dart';
import 'sentence_progress_indicator.dart';

/// Widget for the bottom control panel in the story interaction screen
class StoryControlPanel extends StatefulWidget {
  /// The text to display
  final String text;

  /// The TTS service
  final TTSService ttsService;

  /// Whether the previous button is enabled
  final bool canNavigatePrevious;

  /// Whether the next button is enabled
  final bool canNavigateNext;

  /// Callback when the previous button is pressed
  final VoidCallback onPrevious;

  /// Callback when the next button is pressed
  final VoidCallback onNext;

  /// Constructor
  const StoryControlPanel({
    super.key,
    required this.text,
    required this.ttsService,
    required this.canNavigatePrevious,
    required this.canNavigateNext,
    required this.onPrevious,
    required this.onNext,
  });

  @override
  State<StoryControlPanel> createState() => _StoryControlPanelState();
}

class _StoryControlPanelState extends State<StoryControlPanel> {
  /// Whether the narration is paused
  bool _isPaused = false;

  /// The current narration progress (0.0 to 1.0)
  double _narrationProgress = 0.0;

  @override
  void initState() {
    super.initState();
    _startNarration();
    _setupProgressListener();
  }

  @override
  void didUpdateWidget(StoryControlPanel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text) {
      _startNarration();
    }
  }

  /// Start narrating the text
  void _startNarration() {
    setState(() {
      _isPaused = false;
      _narrationProgress = 0.0;
    });

    widget.ttsService.speak(widget.text);
  }

  /// Set up a listener for narration progress
  void _setupProgressListener() {
    // This is a simplified implementation
    // In a real app, we would use the TTS progress events
    // to update the progress bar
    const duration = Duration(milliseconds: 100);
    Future.delayed(duration, () {
      if (mounted && widget.ttsService.isSpeaking && !_isPaused) {
        setState(() {
          // Increment progress by a small amount
          _narrationProgress = (_narrationProgress + 0.01).clamp(0.0, 1.0);
        });

        // If not complete, schedule another update
        if (_narrationProgress < 1.0) {
          _setupProgressListener();
        }
      }
    });
  }

  /// Toggle pause/play
  void _togglePause() {
    if (_isPaused) {
      widget.ttsService.resume();
      setState(() {
        _isPaused = false;
      });
      _setupProgressListener();
    } else {
      widget.ttsService.pause();
      setState(() {
        _isPaused = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions for responsive sizing
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    // Use the smaller dimension to ensure proper sizing on all devices
    final double minDimension = math.min(screenWidth, screenHeight);

    // Calculate responsive sizes
    final double iconSize = minDimension * 0.05; // 5% of min dimension
    final double buttonPadding = minDimension * 0.01; // 1% of min dimension
    final double horizontalPadding = minDimension * 0.02; // 2% of min dimension
    final double verticalPadding = minDimension * 0.015; // 1.5% of min dimension

    // Calculate sentences for the progress indicator
    final List<String> sentences = TextSegmenter.splitIntoSentences(widget.text);
    final int currentSentenceIndex = widget.ttsService.currentWordIndex > 0
        ? _calculateCurrentSentenceIndex(widget.text, widget.ttsService.currentWord)
        : 0;

    return Consumer<StorySettingsProvider>(
      builder: (context, settingsProvider, child) {
        // Calculate responsive font size
        final double fontSize = settingsProvider.fontSize * (minDimension / 800);

        return Container(
          decoration: BoxDecoration(
            color: Colors.black.withAlpha((settingsProvider.panelOpacity * 255).round()),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(minDimension * 0.02), // 2% of min dimension
              topRight: Radius.circular(minDimension * 0.02),
            ),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding,
            vertical: verticalPadding,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Main controls and subtitle row
              Row(
                children: [
                  // Previous button
                  IconButton(
                    icon: Icon(
                      Icons.skip_previous_rounded,
                      color: Colors.white,
                      size: iconSize,
                    ),
                    onPressed: widget.canNavigatePrevious ? widget.onPrevious : null,
                    tooltip: 'Previous',
                    padding: EdgeInsets.all(buttonPadding),
                  ),

                  // Play/Pause button
                  IconButton(
                    icon: Icon(
                      _isPaused
                          ? Icons.play_circle_filled_rounded
                          : Icons.pause_circle_filled_rounded,
                      color: Colors.white,
                      size: iconSize,
                    ),
                    onPressed: _togglePause,
                    tooltip: _isPaused ? 'Play' : 'Pause',
                    padding: EdgeInsets.all(buttonPadding),
                  ),

                  // Subtitle text
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
                      child: HighlightedText(
                        text: widget.text,
                        ttsService: widget.ttsService,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: fontSize,
                          fontFamily: settingsProvider.fontFamily,
                        ),
                        highlightColor: Colors.yellow.withAlpha(128), // 0.5 * 255 = 127.5 ≈ 128
                      ),
                    ),
                  ),

                  // Next button
                  IconButton(
                    icon: Icon(
                      Icons.skip_next_rounded,
                      color: Colors.white,
                      size: iconSize,
                    ),
                    onPressed: widget.canNavigateNext ? widget.onNext : null,
                    tooltip: 'Next',
                    padding: EdgeInsets.all(buttonPadding),
                  ),
                ],
              ),

              // Sentence progress indicator
              Padding(
                padding: EdgeInsets.only(top: verticalPadding),
                child: SentenceProgressIndicator(
                  sentences: sentences,
                  currentSentenceIndex: currentSentenceIndex,
                  dotSize: screenWidth * 0.008, // 0.8% of screen width
                  spacing: screenWidth * 0.005, // 0.5% of screen width
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Calculate the current sentence index based on the current word
  int _calculateCurrentSentenceIndex(String text, String currentWord) {
    if (currentWord.isEmpty) return 0;

    final List<String> sentences = TextSegmenter.splitIntoSentences(text);
    final int currentWordIndex = widget.ttsService.currentWordIndex;

    // Count words up to the current sentence
    int wordCount = 0;
    for (int i = 0; i < sentences.length; i++) {
      final sentenceWordCount = sentences[i].split(' ').length;
      if (wordCount + sentenceWordCount > currentWordIndex) {
        return i;
      }
      wordCount += sentenceWordCount;
    }

    return sentences.length - 1;
  }
}
