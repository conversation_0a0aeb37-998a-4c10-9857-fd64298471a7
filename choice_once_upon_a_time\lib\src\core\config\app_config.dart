/// Application configuration settings
class AppConfig {
  /// Whether to use local testing mode instead of Firebase
  /// Set to true for local development/testing without Firebase setup
  /// Set to false for production with proper Firebase configuration
  static const bool useLocalTestingMode = false;

  /// Whether to enable debug logging
  static const bool enableDebugLogging = true;

  /// Whether to show debug information in the UI
  static const bool showDebugInfo = true;

  /// Default story loading timeout in seconds
  static const int storyLoadTimeoutSeconds = 30;

  /// Default TTS settings
  static const double defaultNarrationSpeed = 1.0;
  static const double defaultVolume = 0.8;
  static const bool defaultAutoAdvance = true;

  /// Default UI settings
  static const double defaultPanelOpacity = 0.7;
  static const double defaultFontSize = 20.0;
  static const String defaultFontFamily = 'Nunito';

  /// Premium features configuration
  static const bool enablePremiumFeatures = true;
  static const List<String> freeStoryIds = [
    'corals-lost-colors',
    'the-brave-little-turtle',
  ];

  /// Local testing configuration
  static const String localTestingParentEmail = '<EMAIL>';
  static const String localTestingParentName = 'Test Parent';

  /// Firebase configuration (when not using local testing)
  static const String firebaseProjectId = 'choice-once-upon-a-time';
  static const String firebaseStorageBucket = 'choice-once-upon-a-time.appspot.com';

  /// App metadata
  static const String appName = 'Choice: Once Upon A Time';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';

  /// Feature flags
  static const bool enableCloudSync = !useLocalTestingMode;
  static const bool enableAnalytics = !useLocalTestingMode;
  static const bool enableCrashReporting = !useLocalTestingMode;
  static const bool enableInAppPurchases = !useLocalTestingMode;

  /// Development helpers
  static const bool skipOnboarding = useLocalTestingMode;
  static const bool autoSelectFirstChild = useLocalTestingMode;
  static const bool enableTestStories = useLocalTestingMode;

  /// Logging configuration
  static void logInfo(String message) {
    if (enableDebugLogging) {
      print('[INFO] $message');
    }
  }

  static void logError(String message, [Object? error, StackTrace? stackTrace]) {
    if (enableDebugLogging) {
      print('[ERROR] $message');
      if (error != null) print('Error: $error');
      if (stackTrace != null) print('Stack trace: $stackTrace');
    }
  }

  static void logDebug(String message) {
    if (enableDebugLogging) {
      print('[DEBUG] $message');
    }
  }

  /// Get configuration summary for debugging
  static Map<String, dynamic> getConfigSummary() {
    return {
      'useLocalTestingMode': useLocalTestingMode,
      'enableDebugLogging': enableDebugLogging,
      'showDebugInfo': showDebugInfo,
      'enableCloudSync': enableCloudSync,
      'enableAnalytics': enableAnalytics,
      'enableCrashReporting': enableCrashReporting,
      'enableInAppPurchases': enableInAppPurchases,
      'skipOnboarding': skipOnboarding,
      'autoSelectFirstChild': autoSelectFirstChild,
      'enableTestStories': enableTestStories,
      'appVersion': appVersion,
      'appBuildNumber': appBuildNumber,
    };
  }
}
