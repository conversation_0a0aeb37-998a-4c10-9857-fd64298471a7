import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/src/features/story/widgets/sentence_progress_indicator.dart';

void main() {
  group('SentenceProgressIndicator', () {
    testWidgets('should render dots for each sentence', (WidgetTester tester) async {
      final sentences = ['First sentence.', 'Second sentence.', 'Third sentence.'];
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: SentenceProgressIndicator(
                sentences: sentences,
                currentSentenceIndex: 1, // Second sentence is active
                dotSize: 8.0,
                spacing: 4.0,
              ),
            ),
          ),
        ),
      );
      
      // Verify the indicator is rendered
      expect(find.byType(SentenceProgressIndicator), findsOneWidget);
      
      // Verify the correct number of dots are rendered
      final dotContainers = find.byType(AnimatedContainer);
      expect(dotContainers, findsNWidgets(3));
      
      // Verify the active dot is larger
      final dotContainerWidgets = tester.widgetList<AnimatedContainer>(dotContainers);
      final List<double> dotWidths = dotContainerWidgets.map((container) => container.width as double).toList();
      
      // The second dot (index 1) should be larger than the others
      expect(dotWidths[1], greaterThan(dotWidths[0]));
      expect(dotWidths[1], greaterThan(dotWidths[2]));
    });
    
    testWidgets('should render a linear progress indicator for many sentences', (WidgetTester tester) async {
      // Create a list of 25 sentences
      final sentences = List.generate(25, (index) => 'Sentence ${index + 1}.');
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: SentenceProgressIndicator(
                sentences: sentences,
                currentSentenceIndex: 12, // Middle sentence is active
                dotSize: 8.0,
                spacing: 4.0,
              ),
            ),
          ),
        ),
      );
      
      // Verify the indicator is rendered
      expect(find.byType(SentenceProgressIndicator), findsOneWidget);
      
      // Verify a linear progress indicator is used instead of dots
      expect(find.byType(LinearProgressIndicator), findsOneWidget);
      
      // Verify the progress value is approximately halfway
      final progressIndicator = tester.widget<LinearProgressIndicator>(find.byType(LinearProgressIndicator));
      expect(progressIndicator.value, closeTo(0.5, 0.1)); // Allow some flexibility
    });
    
    testWidgets('should render nothing for empty sentences list', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: SentenceProgressIndicator(
                sentences: [],
                currentSentenceIndex: 0,
                dotSize: 8.0,
                spacing: 4.0,
              ),
            ),
          ),
        ),
      );
      
      // Verify the indicator is rendered but contains no visible elements
      expect(find.byType(SentenceProgressIndicator), findsOneWidget);
      expect(find.byType(SizedBox), findsOneWidget);
      expect(find.byType(AnimatedContainer), findsNothing);
      expect(find.byType(LinearProgressIndicator), findsNothing);
    });
    
    testWidgets('should use custom colors if provided', (WidgetTester tester) async {
      final sentences = ['First sentence.', 'Second sentence.', 'Third sentence.'];
      const activeColor = Colors.red;
      const inactiveColor = Colors.blue;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: SentenceProgressIndicator(
                sentences: sentences,
                currentSentenceIndex: 1,
                dotSize: 8.0,
                spacing: 4.0,
                activeDotColor: activeColor,
                inactiveDotColor: inactiveColor,
              ),
            ),
          ),
        ),
      );
      
      // Verify the indicator is rendered
      expect(find.byType(SentenceProgressIndicator), findsOneWidget);
      
      // Verify the dots have the correct colors
      final dotContainers = tester.widgetList<AnimatedContainer>(find.byType(AnimatedContainer));
      
      // The active dot (index 1) should have the active color
      expect(dotContainers.elementAt(1).decoration, isA<BoxDecoration>());
      final activeDecoration = dotContainers.elementAt(1).decoration as BoxDecoration;
      expect(activeDecoration.color, equals(activeColor));
      
      // The inactive dots should have the inactive color
      expect(dotContainers.elementAt(0).decoration, isA<BoxDecoration>());
      final inactiveDecoration = dotContainers.elementAt(0).decoration as BoxDecoration;
      expect(inactiveDecoration.color, equals(inactiveColor));
    });
  });
}
