import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../features/profile/providers/active_child_provider.dart';
import '../../../features/story/models/story_model.dart';
import '../../../features/story/providers/story_provider.dart';
import '../../../features/story/services/tts_service.dart';
import '../../../features/story/widgets/choice_popup.dart';
import '../../../features/story/widgets/story_control_panel.dart';
import '../../../features/story/widgets/story_end_screen.dart';
import '../../../features/story/widgets/story_settings_dialog.dart';

/// Screen that displays the interactive story experience
class StoryInteractionScreen extends StatefulWidget {
  /// The ID of the story
  final String storyId;

  /// The ID of the scene to start with (optional)
  final String? initialSceneId;

  /// Constructor
  const StoryInteractionScreen({
    super.key,
    required this.storyId,
    this.initialSceneId,
  });

  /// Route name for navigation
  static const routeName = '/story-interaction';

  @override
  State<StoryInteractionScreen> createState() => _StoryInteractionScreenState();
}

class _StoryInteractionScreenState extends State<StoryInteractionScreen> {
  /// The TTS service
  final TTSService _ttsService = TTSService();

  /// Whether the popup is visible
  bool _isPopupVisible = false;

  @override
  void initState() {
    super.initState();
    _loadStory();
  }

  @override
  void dispose() {
    _ttsService.dispose();
    super.dispose();
  }

  /// Load the story and initial scene
  Future<void> _loadStory() async {
    try {
      // Get the story provider
      final storyProvider = Provider.of<StoryProvider>(context, listen: false);

      // Set the active story and load the initial scene
      await storyProvider.setActiveStory(
        widget.storyId,
        initialSceneId: widget.initialSceneId,
      );

      // Start narration after a short delay
      Future.delayed(const Duration(milliseconds: 500), () {
        _narrateCurrentScene();
      });

      // Update the active child's last story and scene
      if (mounted) {
        final activeChildProvider = Provider.of<ActiveChildProvider>(context, listen: false);
        final currentScene = storyProvider.currentScene;
        if (activeChildProvider.activeProfile != null && currentScene != null) {
          activeChildProvider.updateLastStory(widget.storyId, currentScene.id);
        }
      }
    } catch (e) {
      debugPrint('Error loading story: $e');

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading story: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Narrate the current scene
  Future<void> _narrateCurrentScene() async {
    final storyProvider = Provider.of<StoryProvider>(context, listen: false);
    final currentScene = storyProvider.currentScene;

    if (currentScene == null) {
      return;
    }

    // Narrate the current text segment
    await _ttsService.speak(storyProvider.currentTextSegment);

    // Mark narration as complete
    storyProvider.completeNarration();

    // If this is a choice point and we're at the last segment,
    // show the choice popup after a short delay
    if (currentScene.isChoicePoint &&
        storyProvider.currentSegmentIndex == storyProvider.currentTextSegments.length - 1) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          setState(() {
            _isPopupVisible = true;
          });
        }
      });
    }
  }

  /// Handle a choice selection
  void _handleChoice(StoryChoice choice) {
    // Hide the popup
    setState(() {
      _isPopupVisible = false;
    });

    // Get the story provider
    final storyProvider = Provider.of<StoryProvider>(context, listen: false);

    // Make the choice
    storyProvider.makeChoice(choice.text, choice.nextSceneId);

    // Start narration after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      _narrateCurrentScene();
    });
  }



  /// Handle the "Try Last Choice Again" option at the end of the story
  void _handleTryLastChoiceAgain() {
    final storyProvider = Provider.of<StoryProvider>(context, listen: false);
    storyProvider.goBackToPreviousChoice();

    // Start narration after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      _narrateCurrentScene();
    });
  }

  /// Handle the "Read Story Again" option at the end of the story
  void _handleReadStoryAgain() {
    final storyProvider = Provider.of<StoryProvider>(context, listen: false);
    storyProvider.restartStory();

    // Start narration after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      _narrateCurrentScene();
    });
  }

  /// Handle the "Find New Story" option at the end of the story
  void _handleFindNewStory() {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<StoryProvider>(
      builder: (context, storyProvider, child) {
        final currentScene = storyProvider.currentScene;
        final isLoading = storyProvider.isLoading;
        final errorMessage = storyProvider.errorMessage;
        final currentTextSegment = storyProvider.currentTextSegment;

        return Scaffold(
          body: Stack(
            children: [
              // Main content
              if (isLoading)
                const Center(child: CircularProgressIndicator())
              else if (errorMessage != null)
                _buildErrorMessage(errorMessage)
              else if (currentScene == null)
                const Center(child: Text('No scene found'))
              else
                _buildStoryLayout(storyProvider, currentScene, currentTextSegment),

              // Choice popup (if visible)
              if (_isPopupVisible &&
                  currentScene != null &&
                  currentScene.isChoicePoint &&
                  currentScene.choices.isNotEmpty)
                _buildChoicePopup(currentScene),

              // End of story screen (if at the end)
              if (currentScene != null && currentScene.isEndingScene)
                _buildEndOfStoryScreen(currentScene),

              // Settings button (top-right)
              if (currentScene != null && !currentScene.isEndingScene && !_isPopupVisible)
                Positioned(
                  top: 16,
                  right: 16,
                  child: FloatingActionButton(
                    mini: true,
                    onPressed: _showSettingsDialog,
                    tooltip: 'Settings',
                    child: const Icon(Icons.settings_rounded),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  /// Show the settings dialog
  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => StorySettingsDialog(
        ttsService: _ttsService,
      ),
    );
  }

  /// Build the error message
  Widget _buildErrorMessage(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(fontSize: 18),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadStory,
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the story layout (full-screen background with bottom control panel)
  Widget _buildStoryLayout(StoryProvider storyProvider, StoryScene scene, String currentTextSegment) {
    return Stack(
      children: [
        // Full-height background image
        Positioned.fill(
          child: _buildSceneImage(scene.imagePath, scene.id),
        ),

        // Bottom control panel
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: StoryControlPanel(
            text: currentTextSegment,
            ttsService: _ttsService,
            canNavigatePrevious: storyProvider.currentSegmentIndex > 0 || storyProvider.sceneHistory.length > 1,
            canNavigateNext: storyProvider.currentSegmentIndex < storyProvider.currentTextSegments.length - 1 ||
                            (scene.defaultNextSceneId != null && !scene.isChoicePoint && !scene.isEndingScene),
            onPrevious: () {
              _ttsService.stop();
              storyProvider.navigateToPreviousSegment();
              Future.delayed(const Duration(milliseconds: 300), () {
                _ttsService.speak(storyProvider.currentTextSegment);
              });
            },
            onNext: () {
              _ttsService.stop();

              // If this is the last segment and there are choices, show the choice popup
              if (storyProvider.currentSegmentIndex == storyProvider.currentTextSegments.length - 1 &&
                  scene.isChoicePoint && scene.choices.isNotEmpty) {
                setState(() {
                  _isPopupVisible = true;
                });
                return;
              }

              // Otherwise, navigate to the next segment or scene
              storyProvider.navigateToNextSegment();
              Future.delayed(const Duration(milliseconds: 300), () {
                _ttsService.speak(storyProvider.currentTextSegment);
              });
            },
          ),
        ),
      ],
    );
  }

  /// Build the scene image with optimized loading
  Widget _buildSceneImage(String imagePath, String sceneId) {
    return Consumer<StoryProvider>(
      builder: (context, storyProvider, child) {
        // Check if image is already cached - if so, use it directly
        if (storyProvider.isImageCached(sceneId)) {
          final validatedImage = storyProvider.getValidatedImageInfo(sceneId);
          final actualPath = validatedImage?.validPath ?? imagePath;

          // Return a stable widget that won't rebuild
          return _buildCachedImage(actualPath, sceneId);
        }

        // Check if we have a validated image for this scene
        final validatedImage = storyProvider.getValidatedImageInfo(sceneId);

        if (validatedImage != null) {
          // Image is validated but not cached, load it and mark as cached
          return _loadAndCacheImage(validatedImage.validPath, sceneId, storyProvider);
        } else {
          // No validated image, fall back to the original loading method
          return _tryLoadImage(imagePath, sceneId);
        }
      },
    );
  }

  /// Build a cached image widget that doesn't rebuild
  Widget _buildCachedImage(String imagePath, String sceneId) {
    return Image.asset(
      imagePath,
      key: ValueKey('cached_image_$sceneId'), // Stable key prevents rebuilds
      fit: BoxFit.cover,
      height: double.infinity,
      width: double.infinity,
      alignment: Alignment.center,
      errorBuilder: (context, error, stackTrace) {
        debugPrint('Error loading cached image: $error');
        return _buildImageErrorWidget(imagePath);
      },
    );
  }

  /// Load and cache an image
  Widget _loadAndCacheImage(String imagePath, String sceneId, StoryProvider storyProvider) {
    // Check if this image is already being loaded
    if (storyProvider.isImageLoading(imagePath)) {
      return const Center(child: CircularProgressIndicator());
    }

    // Mark as loading
    storyProvider.markImageAsLoading(imagePath);

    return Image.asset(
      imagePath,
      fit: BoxFit.cover,
      height: double.infinity,
      width: double.infinity,
      alignment: Alignment.center,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (frame != null) {
          // Image loaded successfully, mark as cached
          WidgetsBinding.instance.addPostFrameCallback((_) {
            storyProvider.markImageAsLoaded(imagePath, sceneId);
          });
        }
        return child;
      },
      errorBuilder: (context, error, stackTrace) {
        // Mark as finished loading even on error
        WidgetsBinding.instance.addPostFrameCallback((_) {
          storyProvider.markImageAsLoaded(imagePath, sceneId);
        });
        debugPrint('Error loading image: $error');
        return _buildImageErrorWidget(imagePath);
      },
    );
  }

  /// Try to load an image with different extensions (fallback method)
  Widget _tryLoadImage(String basePath, String sceneId) {
    // List of extensions to try in order of preference
    final extensions = ['', '.png', '.jpg', '.jpeg', '.webp'];

    return FutureBuilder<String?>(
      future: _findValidImagePath(basePath, extensions),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // Show a loading indicator while checking paths
          return const Center(child: CircularProgressIndicator());
        }

        final validPath = snapshot.data;

        if (validPath != null) {
          // Valid path found, load the image and cache it
          return Consumer<StoryProvider>(
            builder: (context, storyProvider, child) {
              return Image.asset(
                validPath,
                fit: BoxFit.cover,
                height: double.infinity,
                width: double.infinity,
                alignment: Alignment.center,
                frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
                  if (frame != null) {
                    // Image loaded successfully, mark as cached
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      storyProvider.markImageAsLoaded(validPath, sceneId);
                    });
                  }
                  return child;
                },
                errorBuilder: (context, error, stackTrace) {
                  debugPrint('Error loading image: $error');
                  debugPrint('Attempted path: $validPath');
                  return _buildImageErrorWidget(validPath);
                },
              );
            },
          );
        } else {
          // No valid path found
          debugPrint('No valid image path found for: $basePath');
          return _buildImageErrorWidget(basePath);
        }
      },
    );
  }

  /// Find a valid image path by trying different extensions
  Future<String?> _findValidImagePath(String basePath, List<String> extensions) async {
    // Log the base path for debugging
    debugPrint('Attempting to find valid image path for: $basePath');

    // First, check if the path already has a valid extension
    final String basePathLower = basePath.toLowerCase();
    if (basePathLower.endsWith('.png') ||
        basePathLower.endsWith('.jpg') ||
        basePathLower.endsWith('.jpeg') ||
        basePathLower.endsWith('.webp')) {
      try {
        await rootBundle.load(basePath);
        debugPrint('Found image with existing extension: $basePath');
        return basePath;
      } catch (e) {
        debugPrint('Image with existing extension not found: $basePath');
        // Continue to try other extensions
      }
    }

    // Try each extension
    for (final extension in extensions) {
      final path = extension.isEmpty ? basePath : '$basePath$extension';
      try {
        // Check if the asset exists
        await rootBundle.load(path);
        debugPrint('Found valid image path: $path');
        return path;
      } catch (e) {
        // Asset not found with this extension, try the next one
        debugPrint('Image not found with path: $path');
        continue;
      }
    }

    // No valid path found
    return null;
  }

  /// Build a widget to show when image loading fails
  Widget _buildImageErrorWidget(String attemptedPath) {
    return Container(
      color: Colors.grey[800],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_not_supported,
              size: 100,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Image not found',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 18,
              ),
            ),
            if (kDebugMode) // Only show path in debug mode
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  'Path: $attemptedPath',
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Build the choice popup
  Widget _buildChoicePopup(StoryScene scene) {
    // Create a semi-transparent overlay
    return Container(
      color: const Color.fromRGBO(0, 0, 0, 0.7),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: ChoicePopup(
          prompt: scene.choicePrompt ?? 'What will you do?',
          choices: scene.choices,
          ttsService: _ttsService,
          onChoiceSelected: _handleChoice,
        ),
      ),
    );
  }

  /// Build the end of story screen
  Widget _buildEndOfStoryScreen(StoryScene scene) {
    return Container(
      color: const Color.fromRGBO(0, 0, 0, 0.7),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: StoryEndScreen(
          moralLesson: scene.moralLesson,
          ttsService: _ttsService,
          onTryLastChoiceAgain: _handleTryLastChoiceAgain,
          onReadStoryAgain: _handleReadStoryAgain,
          onFindNewStory: _handleFindNewStory,
        ),
      ),
    );
  }
}
