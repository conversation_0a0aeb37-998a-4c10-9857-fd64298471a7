import 'dart:async';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';

// Adjust these import paths based on your actual project structure
import '../../../features/profile/providers/active_child_provider.dart';
import '../../../features/story/models/story_model.dart';
import '../../../features/story/models/scene.dart' as story_models;
import '../../../features/story/providers/story_provider.dart';
import '../../../features/story/providers/story_settings_provider.dart';
import '../../../features/story/services/tts_service.dart';
import '../../../features/story/widgets/choice_popup.dart';
import '../../../features/story/widgets/story_control_panel.dart';
import '../../../features/story/widgets/story_end_screen.dart';
import '../../../features/story/widgets/story_settings_dialog.dart';

/// Screen that displays the interactive story experience
class StoryInteractionScreen extends StatefulWidget {
  /// The ID of the story
  final String storyId;

  /// The ID of the scene to start with (optional)
  final String? initialSceneId;

  /// Constructor
  const StoryInteractionScreen({
    super.key,
    required this.storyId,
    this.initialSceneId,
  });

  /// Route name for navigation
  static const routeName = '/story-interaction';

  @override
  State<StoryInteractionScreen> createState() => _StoryInteractionScreenState();
}

class _StoryInteractionScreenState extends State<StoryInteractionScreen> {
  /// The TTS service
  final TTSService _ttsService = TTSService();
  bool _isMounted = false;

  @override
  void initState() {
    super.initState();
    _isMounted = true;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_isMounted) {
        _loadStory();
      }
    });
  }

  @override
  void dispose() {
    _isMounted = false;
    _ttsService.stop(); // Ensure TTS is stopped
    _ttsService.dispose();
    super.dispose();
  }

  /// Update TTS settings from StorySettingsProvider
  void _updateTTSSettings() {
    if (!_isMounted) return;

    final settingsProvider = Provider.of<StorySettingsProvider>(context, listen: false);

    // Update narration speed
    _ttsService.setSpeechRate(settingsProvider.narrationSpeed);

    // Update natural pause settings
    _ttsService.setNaturalPauseSettings(
      useNaturalPauses: settingsProvider.useNaturalPauses,
      sentencePauseDuration: settingsProvider.sentencePauseDuration,
      segmentPauseDuration: settingsProvider.segmentPauseDuration,
    );

    debugPrint('Updated TTS settings: speed=${settingsProvider.narrationSpeed}, pauses=${settingsProvider.useNaturalPauses}');
  }

  /// Load the story and initial scene
  Future<void> _loadStory() async {
    if (!_isMounted) return;
    try {
      // Update TTS settings from provider
      _updateTTSSettings();

      final storyProvider = Provider.of<StoryProvider>(context, listen: false);
      // Ensure any previous choice popup state is cleared when loading a new story/scene
      storyProvider.setShowChoices(false); // Explicitly tell provider to hide choices

      await storyProvider.setActiveStory(
        widget.storyId,
        initialSceneId: widget.initialSceneId,
      );

      if (_isMounted && storyProvider.activeStory != null && storyProvider.currentScene != null) {
        final activeChildProvider = Provider.of<ActiveChildProvider>(context, listen: false);
        if (activeChildProvider.activeProfile != null) {
          activeChildProvider.updateLastStory(widget.storyId, storyProvider.currentScene!.id);
        }
      }

      if (_isMounted && storyProvider.currentTextSegment.isNotEmpty) {
        Future.delayed(const Duration(milliseconds: 500), () { // UX delay
          if (_isMounted) _narrateCurrentSegment();
        });
      } else if (_isMounted && storyProvider.currentScene != null && storyProvider.currentTextSegment.isEmpty) {
        // If the first scene has no text, provider handles its completion.
        // This call is crucial for the provider to then decide if choices should be shown.
        storyProvider.completeNarration();
      }

    } catch (e) {
      debugPrint('Error loading story: $e');
      if (_isMounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading story: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Narrate the current text segment.
  /// CRITICAL: `_ttsService.speak()` MUST return a Future that completes ONLY when speech playback has finished.
  Future<void> _narrateCurrentSegment() async {
    if (!_isMounted) return;
    final storyProvider = Provider.of<StoryProvider>(context, listen: false);
    final segmentToNarrate = storyProvider.currentTextSegment;

    if (segmentToNarrate.isEmpty && storyProvider.currentScene != null) {
      debugPrint("Current segment is empty. Completing narration for provider to handle next step.");
      // Provider's completeNarration will check if it's a choice point and all segments are done.
      storyProvider.completeNarration();
      return;
    }

    if (segmentToNarrate.isNotEmpty) {
      try {
        debugPrint("Narrating: $segmentToNarrate");
        await _ttsService.speak(segmentToNarrate); // NOW PROPERLY AWAITS ACTUAL SPEECH COMPLETION
        debugPrint("Narration actually finished for: $segmentToNarrate");
      } catch (e) {
        debugPrint('TTS Error during speak: $e');
      } finally {
        if (_isMounted) {
          // Notify provider that this segment's narration is actually complete.
          // Provider will then decide to:
          // 1. Advance to the next segment (and we'll be notified via Consumer to narrate it).
          // 2. If last segment & choice point: set its internal `showChoices = true` & notifyListeners.
          // 3. If last segment & auto-advance: navigate to next scene.
          storyProvider.completeNarration();
        }
      }
    }
  }

  /// Handle a choice selection from the ChoicePopup
  void _handleChoice(StoryChoice choice) {
    if (!_isMounted) return;
    _ttsService.stop(); // Stop any ongoing narration

    final storyProvider = Provider.of<StoryProvider>(context, listen: false);
    // `makeChoice` in StoryProvider should:
    // 1. Set its internal `showChoices = false`.
    // 2. Navigate to the `choice.nextSceneId`.
    // 3. Notify listeners.
    storyProvider.makeChoice(choice.text, choice.nextSceneId).then((_) {
      if (_isMounted) {
        Future.delayed(const Duration(milliseconds: 300), () { // UX delay
          if (_isMounted) _narrateCurrentSegment();
        });
      }
    }).catchError((e) {
      debugPrint("Error making choice or narrating next segment: $e");
      // Handle error appropriately
    });
  }

  /// Generic handler for control panel navigation (Next/Previous)
  void _onControlPanelNavigate(Future<void> Function() providerNavigationAction) {
    if (!_isMounted) return;
    _ttsService.stop();
    providerNavigationAction().then((_) {
      if (_isMounted) {
        Future.delayed(const Duration(milliseconds: 300), () { // UX delay
          if (_isMounted) _narrateCurrentSegment();
        });
      }
    }).catchError((e) {
      debugPrint("Error during control panel navigation or subsequent narration: $e");
      // Handle error appropriately
    });
  }

  void _handleTryLastChoiceAgain() {
    if (!_isMounted) return;
    _ttsService.stop();
    final storyProvider = Provider.of<StoryProvider>(context, listen: false);
    storyProvider.goBackToPreviousChoice().then((_) {
      if (_isMounted) {
        Future.delayed(const Duration(milliseconds: 300), () {
          if (_isMounted) _narrateCurrentSegment();
        });
      }
    });
  }

  void _handleReadStoryAgain() {
    if (!_isMounted) return;
    _ttsService.stop();
    final storyProvider = Provider.of<StoryProvider>(context, listen: false);
    storyProvider.restartStory().then((_) {
      if (_isMounted) {
        Future.delayed(const Duration(milliseconds: 300), () {
          if (_isMounted) _narrateCurrentSegment();
        });
      }
    });
  }

  void _handleFindNewStory() {
    if (!_isMounted) return;
    _ttsService.stop();
    if (Navigator.canPop(context)) {
      Navigator.of(context).pop();
    } else {
      debugPrint("Cannot pop, already at the root or single page in navigation stack.");
      // Optionally navigate to a default screen like:
      // Navigator.of(context).pushReplacementNamed('/story-library');
    }
  }

  /// Check if we can navigate to previous segment
  bool _canNavigatePrevious(StoryProvider storyProvider) {
    return storyProvider.currentSegmentIndex > 0;
  }

  /// Check if we can navigate to next segment
  bool _canNavigateNext(StoryProvider storyProvider) {
    return storyProvider.currentSegmentIndex < storyProvider.currentTextSegments.length - 1;
  }

  /// Navigate to previous segment
  Future<void> _navigateToPreviousSegment(StoryProvider storyProvider) async {
    if (_canNavigatePrevious(storyProvider)) {
      await _ttsService.stop();
      storyProvider.navigateToPreviousSegment();
    }
  }

  /// Navigate to next segment
  Future<void> _navigateToNextSegment(StoryProvider storyProvider) async {
    if (_canNavigateNext(storyProvider)) {
      await _ttsService.stop();
      storyProvider.navigateToNextSegment();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<StoryProvider>(
      builder: (context, storyProvider, child) {
        final currentScene = storyProvider.currentScene;
        final isLoading = storyProvider.isLoading;
        final errorMessage = storyProvider.errorMessage;
        final currentTextSegment = storyProvider.currentTextSegment;
        final bool shouldShowChoices = storyProvider.showChoices; // Directly use provider state

        // Auto-start narration when segment changes and narration is not complete
        // Only start narration if scene narration is not blocked
        if (_isMounted &&
            currentTextSegment.isNotEmpty &&
            !storyProvider.isNarrationComplete &&
            !shouldShowChoices &&
            !storyProvider.isNavigationBlocked &&
            currentScene != null &&
            !currentScene.isEndingScene) {
          // Use a post-frame callback to avoid calling setState during build
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (_isMounted) {
              _updateTTSSettings(); // Update settings before narrating
              _narrateCurrentSegment();
            }
          });
        }

        return Scaffold(
          body: Stack(
            children: [
              if (isLoading && currentScene == null)
                const Center(child: CircularProgressIndicator())
              else if (errorMessage != null)
                _buildErrorMessage(errorMessage, storyProvider)
              else if (currentScene == null)
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('No scene available. Story might be empty or failed to load correctly.'),
                      const SizedBox(height: 10),
                      ElevatedButton(onPressed: _loadStory, child: const Text("Reload Story"))
                    ],
                  )
                )
              else
                _buildStoryLayout(storyProvider, currentScene, currentTextSegment),

              // Choice popup visibility is now directly controlled by storyProvider.showChoices
              if (shouldShowChoices &&
                  currentScene != null &&
                  currentScene.isChoicePoint &&
                  currentScene.choices.isNotEmpty)
                _buildChoicePopup(currentScene),

              if (currentScene != null && currentScene.isEndingScene)
                _buildEndOfStoryScreen(currentScene),

              if (currentScene != null && !currentScene.isEndingScene && !shouldShowChoices)
                Positioned(
                  top: 16 + MediaQuery.of(context).padding.top,
                  right: 16,
                  child: FloatingActionButton(
                    mini: true,
                    backgroundColor: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.8),
                    onPressed: _showSettingsDialog,
                    tooltip: 'Settings',
                    child: const Icon(Icons.settings_rounded),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  void _showSettingsDialog() {
    _ttsService.stop();
    showDialog(
      context: context,
      builder: (context) => StorySettingsDialog(
        ttsService: _ttsService,
      ),
    );
  }

  Widget _buildErrorMessage(String message, StoryProvider storyProvider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(message, style: const TextStyle(fontSize: 18), textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                storyProvider.clearError();
                _loadStory();
              },
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoryLayout(StoryProvider storyProvider, story_models.Scene scene, String currentTextSegment) {
    return Stack(
      children: [
        Positioned.fill(
          child: _buildSceneImage(scene.imagePath ?? '', scene.id),
        ),
        // Hide control panel if choice popup is visible
        if (!storyProvider.showChoices)
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: StoryControlPanel(
              text: currentTextSegment,
              ttsService: _ttsService,
              canNavigatePrevious: _canNavigatePrevious(storyProvider),
              canNavigateNext: _canNavigateNext(storyProvider),
              onPrevious: () => _onControlPanelNavigate(() async => _navigateToPreviousSegment(storyProvider)),
              onNext: () => _onControlPanelNavigate(() async => _navigateToNextSegment(storyProvider)),
            ),
          ).animate().fadeIn(duration: 300.ms),
      ],
    );
  }

  Widget _buildSceneImage(String imagePathFromSceneModel, String sceneId) {
    return Consumer<StoryProvider>(
      builder: (context, storyProvider, child) {
        final validatedImageInfo = storyProvider.getValidatedImageInfo(sceneId);
        String? pathToLoad = validatedImageInfo?.validPath;

        if (pathToLoad == null || pathToLoad.isEmpty) {
          debugPrint("StoryInteractionScreen: Validated path for scene $sceneId not found in provider. Using raw path: $imagePathFromSceneModel.");
          pathToLoad = imagePathFromSceneModel; // Fallback to raw path
        }

        if (pathToLoad == null || pathToLoad.isEmpty) {
             debugPrint("StoryInteractionScreen: No image path available for scene $sceneId.");
            return _buildImageErrorWidget("No path for scene $sceneId");
        }

        return Image.asset(
          pathToLoad,
          key: ValueKey('scene_image_$sceneId'),
          fit: BoxFit.cover,
          height: double.infinity,
          width: double.infinity,
          alignment: Alignment.center,
          errorBuilder: (context, error, stackTrace) {
            debugPrint('Error loading image "$pathToLoad" for scene $sceneId: $error');
            // Optionally, you could try the _tryLoadImage fallback here if needed,
            // but it's better if provider handles all validation.
            // return _tryLoadImage(imagePathFromSceneModel, sceneId);
            return _buildImageErrorWidget(pathToLoad!);
          },
        ).animate().fadeIn(duration: 500.ms);
      },
    );
  }

  /// Screen-level fallback to find an image path.
  /// Consider if this is still needed if StoryProvider's validation is comprehensive.
  Future<String?> _findValidImagePathOnScreen(String basePath) async {
    final extensions = ['', '.png', '.jpg', '.jpeg', '.webp'];
    final String basePathLower = basePath.toLowerCase();
    if (extensions.sublist(1).any((ext) => basePathLower.endsWith(ext))) {
      try {
        await rootBundle.load(basePath);
        return basePath;
      } catch (_) {}
    }
    String nameToTry = basePath;
    int dotIndex = basePath.lastIndexOf('.');
    int lastSlashIndex = basePath.lastIndexOf('/');
    if (dotIndex > lastSlashIndex) {
      nameToTry = basePath.substring(0, dotIndex);
    }
    for (final extension in extensions) {
      final path = extension.isEmpty ? nameToTry : '$nameToTry$extension';
      try {
        await rootBundle.load(path);
        debugPrint('Screen Fallback: Found valid image path: $path');
        return path;
      } catch (e) {}
    }
    return null;
  }

  Widget _buildImageErrorWidget(String attemptedPath) {
    return Container(
      color: Colors.grey[800],
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.image_not_supported_outlined, size: 80, color: Colors.grey[500]),
              const SizedBox(height: 16),
              Text('Image not found', style: TextStyle(color: Colors.grey[400], fontSize: 18)),
              if (kDebugMode)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text('Attempted: $attemptedPath', style: TextStyle(color: Colors.grey[500], fontSize: 12), textAlign: TextAlign.center, overflow: TextOverflow.ellipsis, maxLines: 2),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChoicePopup(story_models.Scene scene) {
    return Positioned.fill(
      child: Container(
        color: Colors.black.withValues(alpha: 0.75),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
          child: Center(
            child: ChoicePopup(
              prompt: scene.choicePrompt ?? 'What will you do?',
              choices: scene.choices,
              ttsService: _ttsService,
              onChoiceSelected: _handleChoice,
            ),
          ),
        ),
      ).animate().fadeIn(duration: 200.ms),
    );
  }

  Widget _buildEndOfStoryScreen(StoryScene scene) {
    return Positioned.fill(
      child: Container(
        color: Colors.black.withValues(alpha: 0.8),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Center(
            child: StoryEndScreen(
              moralLesson: scene.moralLesson,
              ttsService: _ttsService,
              onTryLastChoiceAgain: _handleTryLastChoiceAgain,
              onReadStoryAgain: _handleReadStoryAgain,
              onFindNewStory: _handleFindNewStory,
            ),
          ),
        ),
      ).animate().fadeIn(duration: 400.ms),
    );
  }
}
