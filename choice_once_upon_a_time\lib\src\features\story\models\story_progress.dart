import 'package:equatable/equatable.dart';

/// Represents the progress of a user through a story
class StoryProgress extends Equatable {
  /// ID of the story this progress belongs to
  final String storyId;

  /// Current scene ID where the user is
  final String currentSceneId;

  /// Map of choices made by the user (sceneId -> choiceId)
  final Map<String, String> choicesMade;

  /// List of completed scene IDs
  final List<String> completedScenes;

  /// Whether the story has been completed
  final bool isCompleted;

  /// Total time spent reading this story (in minutes)
  final int totalTimeMinutes;

  /// When this progress was last updated
  final DateTime lastUpdated;

  /// When this progress was first created
  final DateTime createdAt;

  /// Optional metadata for the progress
  final Map<String, dynamic>? metadata;

  /// Creates a new StoryProgress
  const StoryProgress({
    required this.storyId,
    required this.currentSceneId,
    this.choicesMade = const {},
    this.completedScenes = const [],
    this.isCompleted = false,
    this.totalTimeMinutes = 0,
    required this.lastUpdated,
    required this.createdAt,
    this.metadata,
  });

  /// Creates a StoryProgress from JSON
  factory StoryProgress.fromJson(Map<String, dynamic> json) {
    return StoryProgress(
      storyId: json['storyId'] as String,
      currentSceneId: json['currentSceneId'] as String,
      choicesMade: Map<String, String>.from(json['choicesMade'] as Map? ?? {}),
      completedScenes: List<String>.from(json['completedScenes'] as List? ?? []),
      isCompleted: json['isCompleted'] as bool? ?? false,
      totalTimeMinutes: json['totalTimeMinutes'] as int? ?? 0,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Converts the StoryProgress to JSON
  Map<String, dynamic> toJson() {
    return {
      'storyId': storyId,
      'currentSceneId': currentSceneId,
      'choicesMade': choicesMade,
      'completedScenes': completedScenes,
      'isCompleted': isCompleted,
      'totalTimeMinutes': totalTimeMinutes,
      'lastUpdated': lastUpdated.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// Creates a copy of this StoryProgress with the given fields replaced
  StoryProgress copyWith({
    String? storyId,
    String? currentSceneId,
    Map<String, String>? choicesMade,
    List<String>? completedScenes,
    bool? isCompleted,
    int? totalTimeMinutes,
    DateTime? lastUpdated,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
  }) {
    return StoryProgress(
      storyId: storyId ?? this.storyId,
      currentSceneId: currentSceneId ?? this.currentSceneId,
      choicesMade: choicesMade ?? this.choicesMade,
      completedScenes: completedScenes ?? this.completedScenes,
      isCompleted: isCompleted ?? this.isCompleted,
      totalTimeMinutes: totalTimeMinutes ?? this.totalTimeMinutes,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Creates a new StoryProgress for a story
  factory StoryProgress.newStory({
    required String storyId,
    required String initialSceneId,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    return StoryProgress(
      storyId: storyId,
      currentSceneId: initialSceneId,
      choicesMade: {},
      completedScenes: [],
      isCompleted: false,
      totalTimeMinutes: 0,
      lastUpdated: now,
      createdAt: now,
      metadata: metadata,
    );
  }

  /// Add a choice made by the user
  StoryProgress addChoice({
    required String sceneId,
    required String choiceId,
    required String nextSceneId,
  }) {
    final updatedChoices = Map<String, String>.from(choicesMade);
    updatedChoices[sceneId] = choiceId;

    final updatedCompleted = List<String>.from(completedScenes);
    if (!updatedCompleted.contains(sceneId)) {
      updatedCompleted.add(sceneId);
    }

    return copyWith(
      choicesMade: updatedChoices,
      completedScenes: updatedCompleted,
      currentSceneId: nextSceneId,
      lastUpdated: DateTime.now(),
    );
  }

  /// Mark the current scene as completed
  StoryProgress completeScene(String sceneId) {
    final updatedCompleted = List<String>.from(completedScenes);
    if (!updatedCompleted.contains(sceneId)) {
      updatedCompleted.add(sceneId);
    }

    return copyWith(
      completedScenes: updatedCompleted,
      lastUpdated: DateTime.now(),
    );
  }

  /// Navigate to a specific scene
  StoryProgress navigateToScene(String sceneId) {
    return copyWith(
      currentSceneId: sceneId,
      lastUpdated: DateTime.now(),
    );
  }

  /// Mark the story as completed
  StoryProgress complete() {
    return copyWith(
      isCompleted: true,
      lastUpdated: DateTime.now(),
    );
  }

  /// Add reading time
  StoryProgress addReadingTime(int minutes) {
    return copyWith(
      totalTimeMinutes: totalTimeMinutes + minutes,
      lastUpdated: DateTime.now(),
    );
  }

  /// Get progress percentage (0.0 to 1.0)
  double getProgressPercentage(int totalScenes) {
    if (totalScenes == 0) return 0.0;
    if (isCompleted) return 1.0;
    return completedScenes.length / totalScenes;
  }

  /// Check if a scene has been completed
  bool hasCompletedScene(String sceneId) {
    return completedScenes.contains(sceneId);
  }

  /// Get the choice made for a specific scene
  String? getChoiceForScene(String sceneId) {
    return choicesMade[sceneId];
  }

  /// Get the number of choices made
  int get choiceCount => choicesMade.length;

  /// Get the number of completed scenes
  int get completedSceneCount => completedScenes.length;

  /// Check if this is a new story (no progress made)
  bool get isNewStory => completedScenes.isEmpty && choicesMade.isEmpty;

  /// Get reading time in a human-readable format
  String get readingTimeFormatted {
    if (totalTimeMinutes < 60) {
      return '${totalTimeMinutes}m';
    } else {
      final hours = totalTimeMinutes ~/ 60;
      final minutes = totalTimeMinutes % 60;
      return '${hours}h ${minutes}m';
    }
  }

  /// Get last updated time in a human-readable format
  String get lastUpdatedFormatted {
    final now = DateTime.now();
    final difference = now.difference(lastUpdated);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }

  @override
  List<Object?> get props => [
        storyId,
        currentSceneId,
        choicesMade,
        completedScenes,
        isCompleted,
        totalTimeMinutes,
        lastUpdated,
        createdAt,
        metadata,
      ];

  @override
  String toString() {
    return 'StoryProgress(storyId: $storyId, currentSceneId: $currentSceneId, '
        'completed: $isCompleted, progress: ${completedScenes.length} scenes)';
  }
}
