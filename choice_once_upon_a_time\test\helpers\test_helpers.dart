import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import '../../lib/src/features/auth/providers/parent_auth_provider.dart';
import '../../lib/src/features/monetization/providers/purchase_provider.dart';
import '../../lib/src/features/profile/providers/active_child_provider.dart';
import '../../lib/src/features/story/providers/story_provider.dart';
import '../../lib/src/features/story/providers/story_settings_provider.dart';
import '../../lib/src/features/profile/models/child_profile.dart';
import '../../lib/src/features/story/models/story.dart';
import '../../lib/src/features/story/models/scene.dart';
import '../../lib/src/features/story/models/story_progress.dart';

// Generate mocks for all providers and services
@GenerateMocks([
  ParentAuthProvider,
  PurchaseProvider,
  ActiveChildProvider,
  StoryProvider,
  StorySettingsProvider,
])
import 'test_helpers.mocks.dart';

/// Test helper utilities for the Choice: Once Upon A Time app
class TestHelpers {
  /// Create a test widget with all necessary providers
  static Widget createTestWidget({
    required Widget child,
    ParentAuthProvider? authProvider,
    PurchaseProvider? purchaseProvider,
    ActiveChildProvider? activeChildProvider,
    StoryProvider? storyProvider,
    StorySettingsProvider? storySettingsProvider,
  }) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<ParentAuthProvider>(
          create: (_) => authProvider ?? MockParentAuthProvider(),
        ),
        ChangeNotifierProvider<PurchaseProvider>(
          create: (_) => purchaseProvider ?? MockPurchaseProvider(),
        ),
        ChangeNotifierProvider<ActiveChildProvider>(
          create: (_) => activeChildProvider ?? MockActiveChildProvider(),
        ),
        ChangeNotifierProvider<StoryProvider>(
          create: (_) => storyProvider ?? MockStoryProvider(),
        ),
        ChangeNotifierProvider<StorySettingsProvider>(
          create: (_) => storySettingsProvider ?? MockStorySettingsProvider(),
        ),
      ],
      child: MaterialApp(
        home: child,
      ),
    );
  }

  /// Create a test widget with responsive testing capabilities
  static Widget createResponsiveTestWidget({
    required Widget child,
    Size? screenSize,
    ParentAuthProvider? authProvider,
    PurchaseProvider? purchaseProvider,
    ActiveChildProvider? activeChildProvider,
    StoryProvider? storyProvider,
    StorySettingsProvider? storySettingsProvider,
  }) {
    Widget testWidget = createTestWidget(
      child: child,
      authProvider: authProvider,
      purchaseProvider: purchaseProvider,
      activeChildProvider: activeChildProvider,
      storyProvider: storyProvider,
      storySettingsProvider: storySettingsProvider,
    );

    if (screenSize != null) {
      testWidget = MediaQuery(
        data: MediaQueryData(size: screenSize),
        child: testWidget,
      );
    }

    return testWidget;
  }

  /// Common screen sizes for testing
  static const Size phonePortrait = Size(360, 640);
  static const Size phoneLandscape = Size(640, 360);
  static const Size phonePortraitLarge = Size(414, 896);
  static const Size phoneLandscapeLarge = Size(896, 414);
  static const Size tabletPortrait = Size(768, 1024);
  static const Size tabletLandscape = Size(1024, 768);
  static const Size tabletPortraitLarge = Size(834, 1194);
  static const Size tabletLandscapeLarge = Size(1194, 834);
  static const Size desktopSmall = Size(1200, 800);
  static const Size desktopLarge = Size(1920, 1080);

  /// Get all test screen sizes
  static List<Size> get allScreenSizes => [
        phonePortrait,
        phoneLandscape,
        phonePortraitLarge,
        phoneLandscapeLarge,
        tabletPortrait,
        tabletLandscape,
        tabletPortraitLarge,
        tabletLandscapeLarge,
        desktopSmall,
        desktopLarge,
      ];

  /// Create a mock child profile for testing
  static ChildProfile createMockChildProfile({
    String id = 'test-child-1',
    String name = 'Test Child',
    int age = 8,
    String avatarId = 'avatar1',
    Map<String, StoryProgress>? storyProgress,
    Map<String, dynamic>? preferences,
  }) {
    return ChildProfile(
      id: id,
      name: name,
      age: age,
      avatarId: avatarId,
      storyProgress: storyProgress ?? {},
      preferences: preferences ?? {},
      lastActive: DateTime.now(),
    );
  }

  /// Create a mock story for testing
  static Story createMockStory({
    String id = 'test-story-1',
    String title = 'Test Story',
    String description = 'A test story for unit testing',
    String? coverImagePath,
    bool isLocked = false,
    List<Scene>? scenes,
  }) {
    return Story(
      id: id,
      title: title,
      description: description,
      coverImagePath: coverImagePath,
      isLocked: isLocked,
      scenes: scenes ?? [createMockScene()],
    );
  }

  /// Create a mock scene for testing
  static Scene createMockScene({
    String id = 'test-scene-1',
    String text = 'This is a test scene.',
    String? backgroundImagePath,
    List<Choice>? choices,
    String? defaultNextSceneId,
  }) {
    return Scene(
      id: id,
      text: text,
      backgroundImagePath: backgroundImagePath,
      choices: choices ?? [],
      defaultNextSceneId: defaultNextSceneId,
    );
  }

  /// Create a mock story progress for testing
  static StoryProgress createMockStoryProgress({
    String storyId = 'test-story-1',
    String currentSceneId = 'test-scene-1',
    List<String>? completedScenes,
    Map<String, String>? choicesMade,
    bool isCompleted = false,
    int totalTimeMinutes = 0,
  }) {
    return StoryProgress.create(
      storyId: storyId,
      currentSceneId: currentSceneId,
      completedScenes: completedScenes ?? [],
      choicesMade: choicesMade ?? {},
      isCompleted: isCompleted,
      totalTimeMinutes: totalTimeMinutes,
    );
  }

  /// Setup common mock behaviors for providers
  static void setupMockProviders({
    MockParentAuthProvider? authProvider,
    MockPurchaseProvider? purchaseProvider,
    MockActiveChildProvider? activeChildProvider,
    MockStoryProvider? storyProvider,
    MockStorySettingsProvider? storySettingsProvider,
  }) {
    // Setup auth provider defaults
    if (authProvider != null) {
      when(authProvider.isAuthenticated).thenReturn(false);
      when(authProvider.isLoading).thenReturn(false);
      when(authProvider.errorMessage).thenReturn(null);
      when(authProvider.hasChildren).thenReturn(false);
      when(authProvider.childProfiles).thenReturn([]);
    }

    // Setup purchase provider defaults
    if (purchaseProvider != null) {
      when(purchaseProvider.isPremiumUnlocked).thenReturn(false);
      when(purchaseProvider.isLoading).thenReturn(false);
      when(purchaseProvider.errorMessage).thenReturn(null);
      when(purchaseProvider.isStoryUnlocked(any)).thenReturn(true); // Free stories unlocked
    }

    // Setup active child provider defaults
    if (activeChildProvider != null) {
      when(activeChildProvider.activeProfile).thenReturn(null);
      when(activeChildProvider.isCloudSyncAvailable).thenReturn(false);
      when(activeChildProvider.hasStoryToResume()).thenAnswer((_) async => false);
    }

    // Setup story provider defaults
    if (storyProvider != null) {
      when(storyProvider.stories).thenReturn([]);
      when(storyProvider.isLoading).thenReturn(false);
      when(storyProvider.activeStory).thenReturn(null);
      when(storyProvider.currentScene).thenReturn(null);
    }

    // Setup story settings provider defaults
    if (storySettingsProvider != null) {
      when(storySettingsProvider.speechRate).thenReturn(1.0);
      when(storySettingsProvider.autoAdvance).thenReturn(true);
      when(storySettingsProvider.showSubtitles).thenReturn(true);
    }
  }

  /// Verify that no RenderFlex overflow errors occur
  static void verifyNoOverflow(WidgetTester tester) {
    expect(tester.takeException(), isNull, reason: 'No RenderFlex overflow should occur');
  }

  /// Test widget on multiple screen sizes
  static Future<void> testOnAllScreenSizes(
    WidgetTester tester,
    Widget Function(Size screenSize) widgetBuilder,
    Future<void> Function(WidgetTester tester, Size screenSize) testFunction,
  ) async {
    for (final screenSize in allScreenSizes) {
      await tester.pumpWidget(widgetBuilder(screenSize));
      await testFunction(tester, screenSize);
      verifyNoOverflow(tester);
    }
  }

  /// Pump and settle with timeout
  static Future<void> pumpAndSettleWithTimeout(
    WidgetTester tester, {
    Duration timeout = const Duration(seconds: 10),
  }) async {
    await tester.pumpAndSettle(timeout);
  }

  /// Find widget by type and verify it exists
  static T findWidgetByType<T extends Widget>(WidgetTester tester) {
    final finder = find.byType(T);
    expect(finder, findsOneWidget, reason: 'Should find exactly one ${T.toString()}');
    return tester.widget<T>(finder);
  }

  /// Find multiple widgets by type
  static List<T> findWidgetsByType<T extends Widget>(WidgetTester tester) {
    final finder = find.byType(T);
    return tester.widgetList<T>(finder).toList();
  }

  /// Verify accessibility for screen readers
  static Future<void> verifyAccessibility(WidgetTester tester) async {
    final SemanticsHandle handle = tester.ensureSemantics();
    await expectLater(tester, meetsGuideline(androidTapTargetGuideline));
    await expectLater(tester, meetsGuideline(iOSTapTargetGuideline));
    await expectLater(tester, meetsGuideline(labeledTapTargetGuideline));
    await expectLater(tester, meetsGuideline(textContrastGuideline));
    handle.dispose();
  }

  /// Create a test environment with error handling
  static Future<void> runTestWithErrorHandling(
    String description,
    Future<void> Function() testFunction,
  ) async {
    try {
      await testFunction();
    } catch (e, stackTrace) {
      fail('Test "$description" failed with error: $e\nStack trace: $stackTrace');
    }
  }
}
