import 'package:equatable/equatable.dart';
import 'story_model.dart';

/// Represents a choice that the user can make in a scene
class Choice extends Equatable {
  /// Unique identifier for the choice
  final String id;

  /// Text displayed for the choice
  final String text;

  /// ID of the scene to navigate to when this choice is selected
  final String nextSceneId;

  /// Optional condition that must be met for this choice to be available
  final String? condition;

  /// Optional metadata for the choice
  final Map<String, dynamic>? metadata;

  /// Creates a new Choice
  const Choice({
    required this.id,
    required this.text,
    required this.nextSceneId,
    this.condition,
    this.metadata,
  });

  /// Creates a Choice from JSON
  factory Choice.fromJson(Map<String, dynamic> json) {
    return Choice(
      id: json['id'] as String,
      text: json['text'] as String,
      nextSceneId: json['nextSceneId'] as String,
      condition: json['condition'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Converts the Choice to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'nextSceneId': nextSceneId,
      if (condition != null) 'condition': condition,
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// Creates a copy of this Choice with the given fields replaced
  Choice copyWith({
    String? id,
    String? text,
    String? nextSceneId,
    String? condition,
    Map<String, dynamic>? metadata,
  }) {
    return Choice(
      id: id ?? this.id,
      text: text ?? this.text,
      nextSceneId: nextSceneId ?? this.nextSceneId,
      condition: condition ?? this.condition,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [id, text, nextSceneId, condition, metadata];

  @override
  String toString() {
    return 'Choice(id: $id, text: $text, nextSceneId: $nextSceneId)';
  }
}

/// Represents a scene in a story
class Scene extends Equatable {
  /// Unique identifier for the scene
  final String id;

  /// Text content of the scene
  final String text;

  /// Path to the background image for this scene
  final String? backgroundImagePath;

  /// List of choices available in this scene
  final List<Choice> choices;

  /// Prompt text for choices (if this is a choice point)
  final String? choicePrompt;

  /// Path to audio file for narration
  final String? audioPath;

  /// Whether this scene is an ending scene
  final bool isEnding;

  /// Moral lesson for ending scenes
  final String? moralLesson;

  /// Optional metadata for the scene
  final Map<String, dynamic>? metadata;

  /// Default next scene ID if no choices are made (for auto-advance)
  final String? defaultNextSceneId;

  /// Duration to wait before auto-advancing (if no choices)
  final Duration? autoAdvanceDelay;

  /// Creates a new Scene
  const Scene({
    required this.id,
    required this.text,
    this.backgroundImagePath,
    this.choices = const [],
    this.choicePrompt,
    this.audioPath,
    this.isEnding = false,
    this.moralLesson,
    this.metadata,
    this.defaultNextSceneId,
    this.autoAdvanceDelay,
  });

  /// Creates a Scene from JSON
  factory Scene.fromJson(Map<String, dynamic> json) {
    return Scene(
      id: json['id'] as String,
      text: json['text'] as String,
      backgroundImagePath: json['backgroundImagePath'] as String?,
      choices: (json['choices'] as List<dynamic>?)
              ?.map((choice) => Choice.fromJson(choice as Map<String, dynamic>))
              .toList() ??
          [],
      choicePrompt: json['choicePrompt'] as String?,
      audioPath: json['audioPath'] as String?,
      isEnding: json['isEnding'] as bool? ?? false,
      moralLesson: json['moralLesson'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      defaultNextSceneId: json['defaultNextSceneId'] as String?,
      autoAdvanceDelay: json['autoAdvanceDelay'] != null
          ? Duration(milliseconds: json['autoAdvanceDelay'] as int)
          : null,
    );
  }

  /// Converts the Scene to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      if (backgroundImagePath != null) 'backgroundImagePath': backgroundImagePath,
      'choices': choices.map((choice) => choice.toJson()).toList(),
      if (choicePrompt != null) 'choicePrompt': choicePrompt,
      if (audioPath != null) 'audioPath': audioPath,
      'isEnding': isEnding,
      if (moralLesson != null) 'moralLesson': moralLesson,
      if (metadata != null) 'metadata': metadata,
      if (defaultNextSceneId != null) 'defaultNextSceneId': defaultNextSceneId,
      if (autoAdvanceDelay != null) 'autoAdvanceDelay': autoAdvanceDelay!.inMilliseconds,
    };
  }

  /// Creates a copy of this Scene with the given fields replaced
  Scene copyWith({
    String? id,
    String? text,
    String? backgroundImagePath,
    List<Choice>? choices,
    String? choicePrompt,
    String? audioPath,
    bool? isEnding,
    String? moralLesson,
    Map<String, dynamic>? metadata,
    String? defaultNextSceneId,
    Duration? autoAdvanceDelay,
  }) {
    return Scene(
      id: id ?? this.id,
      text: text ?? this.text,
      backgroundImagePath: backgroundImagePath ?? this.backgroundImagePath,
      choices: choices ?? this.choices,
      choicePrompt: choicePrompt ?? this.choicePrompt,
      audioPath: audioPath ?? this.audioPath,
      isEnding: isEnding ?? this.isEnding,
      moralLesson: moralLesson ?? this.moralLesson,
      metadata: metadata ?? this.metadata,
      defaultNextSceneId: defaultNextSceneId ?? this.defaultNextSceneId,
      autoAdvanceDelay: autoAdvanceDelay ?? this.autoAdvanceDelay,
    );
  }

  /// Whether this scene has choices available
  bool get hasChoices => choices.isNotEmpty;

  /// Whether this scene should auto-advance
  bool get shouldAutoAdvance => !hasChoices && defaultNextSceneId != null;

  /// Whether this scene is a choice point (has choices)
  bool get isChoicePoint => choices.isNotEmpty;

  /// Whether this scene is an ending scene
  bool get isEndingScene => isEnding;

  /// Get the narration text (alias for text)
  String get narrationText => text;

  /// Get the image path (alias for backgroundImagePath)
  String? get imagePath => backgroundImagePath;

  /// Convert choices to StoryChoice format for compatibility
  List<StoryChoice> get storyChoices {
    return choices.map((choice) => StoryChoice(
      text: choice.text,
      nextSceneId: choice.nextSceneId,
    )).toList();
  }

  /// Get choice by ID
  Choice? getChoiceById(String choiceId) {
    try {
      return choices.firstWhere((choice) => choice.id == choiceId);
    } catch (e) {
      return null;
    }
  }

  /// Get available choices (considering conditions)
  List<Choice> getAvailableChoices({Map<String, dynamic>? context}) {
    if (context == null) return choices;

    return choices.where((choice) {
      if (choice.condition == null) return true;
      // TODO: Implement condition evaluation logic
      return true;
    }).toList();
  }

  @override
  List<Object?> get props => [
        id,
        text,
        backgroundImagePath,
        choices,
        choicePrompt,
        audioPath,
        isEnding,
        moralLesson,
        metadata,
        defaultNextSceneId,
        autoAdvanceDelay,
      ];

  @override
  String toString() {
    return 'Scene(id: $id, text: ${text.length > 50 ? '${text.substring(0, 50)}...' : text}, choices: ${choices.length})';
  }
}
