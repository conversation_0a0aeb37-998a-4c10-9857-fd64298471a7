2025-05-25 14:12:37.481: [INFO][Sync] Reset engine, reason: 8
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Bookmarks
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Preferences
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Passwords
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Autofill Profiles
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Autofill
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Extensions
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Sessions
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Extension settings
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: History Delete Directives
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Device Info
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: User Consents
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Send Tab To Self
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Web Apps
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: History
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Saved Tab Group
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Collection
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Edge E Drop
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Edge Hub App Usage
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Edge Tab Group
2025-05-25 14:12:37.481: [INFO][Sync] Stopped: Edge Wallet
2025-05-25 14:12:37.481: [INFO][Sync] SyncState after authenticated was: NotSignedIn
2025-05-25 14:12:37.709: [INFO][Sync] Reset engine, reason: 8
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Bookmarks
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Preferences
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Passwords
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Autofill Profiles
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Autofill
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Extensions
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Sessions
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Extension settings
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: History Delete Directives
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Device Info
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: User Consents
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Send Tab To Self
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Web Apps
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: History
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Saved Tab Group
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Collection
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Edge E Drop
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Edge Hub App Usage
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Edge Tab Group
2025-05-25 14:12:37.709: [INFO][Sync] Stopped: Edge Wallet
2025-05-25 14:12:38.977: [INFO][Sync] Try to start sync engine
2025-05-25 14:12:38.980: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Encryption Keys
2025-05-25 14:12:38.980: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-05-25 14:12:38.980: [INFO][SyncEngineBackend::DoInitialize] Control Types added: Encryption Keys
2025-05-25 14:12:38.980: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Encryption Keys with reason: 3
2025-05-25 14:12:38.980: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Configure job was blocked
2025-05-25 14:12:39.042: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-25 14:12:39.042: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-25 14:12:39.042: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-05-25 14:12:39.757: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-25 14:12:39.757: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-25 14:12:39.757: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-05-25 14:12:39.757: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-05-25 14:12:39.757: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-05-25 14:12:39.757: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Encryption Keys
2025-05-25 14:12:48.157: [INFO][Sync] Started DataTypeManager configuration, reason: 1
2025-05-25 14:12:48.157: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 3, current state: 0
2025-05-25 14:12:48.157: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-05-25 14:12:48.159: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys; Allow fail: 
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Bookmarks
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Preferences
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Passwords
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Autofill Profiles
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Autofill
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Extensions
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Sessions
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Extension settings
2025-05-25 14:12:48.159: [INFO][Sync] Loading: History Delete Directives
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Device Info
2025-05-25 14:12:48.159: [INFO][Sync] Loading: User Consents
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Send Tab To Self
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Web Apps
2025-05-25 14:12:48.159: [INFO][Sync] Loading: History
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Saved Tab Group
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Collection
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Edge E Drop
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Edge Hub App Usage
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Edge Tab Group
2025-05-25 14:12:48.159: [INFO][Sync] Loading: Edge Wallet
2025-05-25 14:12:48.170: [INFO][Sync] All data types are ready for configure.
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Bookmarks
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Preferences
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Passwords
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Autofill Profiles
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Autofill
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Extensions
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Sessions
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Extension settings
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for History Delete Directives
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Device Info
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Send Tab To Self
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Web Apps
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for History
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Saved Tab Group
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Collection
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge E Drop
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge Hub App Usage
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge Tab Group
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge Wallet
2025-05-25 14:12:48.170: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 3
2025-05-25 14:12:48.170: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 14:12:48.171: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-05-25 14:12:48.171: [INFO][Sync] Prepare to configure types: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-05-25 14:12:48.171: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys with reason: 3
2025-05-25 14:12:48.171: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-05-25 14:12:48.530: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-05-25 14:12:48.539: [INFO][Sync] ConfigurationDone, failed: , succeeded: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys, remaining count: 3
2025-05-25 14:12:48.539: [INFO][Sync] Prepare to configure types: Bookmarks, Encryption Keys
2025-05-25 14:12:48.539: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Bookmarks, Encryption Keys with reason: 3
2025-05-25 14:12:48.539: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Bookmarks, Encryption Keys
2025-05-25 14:12:48.662: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Bookmarks, Encryption Keys
2025-05-25 14:12:48.668: [INFO][Sync] ConfigurationDone, failed: , succeeded: Bookmarks, Encryption Keys, remaining count: 2
2025-05-25 14:12:48.668: [INFO][Sync] Prepare to configure types: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys
2025-05-25 14:12:48.668: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys with reason: 3
2025-05-25 14:12:48.668: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys
2025-05-25 14:12:48.908: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys
2025-05-25 14:12:48.909: [INFO][Sync] ConfigurationDone, failed: , succeeded: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, remaining count: 1
2025-05-25 14:12:48.911: [INFO][Sync] Prepare to configure types: History, Encryption Keys
2025-05-25 14:12:48.911: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: History, Encryption Keys with reason: 3
2025-05-25 14:12:48.911: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: History, Encryption Keys
2025-05-25 14:12:49.106: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: History, Encryption Keys
2025-05-25 14:12:49.107: [INFO][Sync] ConfigurationDone, failed: , succeeded: History, Encryption Keys, remaining count: 0
2025-05-25 14:12:49.108: [INFO][Sync]     Configuration completed, state: 3
2025-05-25 14:12:49.108: [INFO][Sync] Configured DataTypeManager: Ok
2025-05-25 14:12:49.110: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-05-25 14:13:19.439: [INFO][Sync] Reset engine, reason: 0
2025-05-25 14:13:19.439: [INFO][Sync] Reset engine with reason: 0
