import 'package:flutter/material.dart';

/// Provider to manage story-specific settings
class StorySettingsProvider extends ChangeNotifier {
  /// The transparency level of the control panel (0.0 to 1.0)
  double _panelOpacity = 0.7;

  /// The font size for the subtitle text
  double _fontSize = 20.0;

  /// The narration speed (0.5 to 1.5)
  double _narrationSpeed = 0.5;

  /// The selected font family
  String _fontFamily = 'Nunito';

  /// Whether natural pauses are enabled
  bool _useNaturalPauses = true;

  /// Sentence pause duration in milliseconds
  int _sentencePauseDuration = 650;

  /// Segment pause duration in milliseconds
  int _segmentPauseDuration = 1000;

  /// Available font families
  final List<String> _availableFontFamilies = [
    'Nunito',
    'Comic Sans MS',
    'Roboto',
    'Open Sans',
  ];

  /// Getters
  double get panelOpacity => _panelOpacity;
  double get fontSize => _fontSize;
  double get narrationSpeed => _narrationSpeed;
  String get fontFamily => _fontFamily;
  bool get useNaturalPauses => _useNaturalPauses;
  int get sentencePauseDuration => _sentencePauseDuration;
  int get segmentPauseDuration => _segmentPauseDuration;
  List<String> get availableFontFamilies => _availableFontFamilies;

  /// Set the panel opacity
  void setPanelOpacity(double opacity) {
    if (opacity >= 0.0 && opacity <= 1.0) {
      _panelOpacity = opacity;
      notifyListeners();
    }
  }

  /// Set the font size
  void setFontSize(double size) {
    if (size >= 16.0 && size <= 32.0) {
      _fontSize = size;
      notifyListeners();
    }
  }

  /// Set the narration speed
  void setNarrationSpeed(double speed) {
    if (speed >= 0.3 && speed <= 1.5) {
      _narrationSpeed = speed;
      notifyListeners();
    }
  }

  /// Set the font family
  void setFontFamily(String family) {
    if (_availableFontFamilies.contains(family)) {
      _fontFamily = family;
      notifyListeners();
    }
  }

  /// Set whether natural pauses are enabled
  void setUseNaturalPauses(bool enabled) {
    _useNaturalPauses = enabled;
    notifyListeners();
  }

  /// Set the sentence pause duration
  void setSentencePauseDuration(int duration) {
    if (duration >= 200 && duration <= 2000) {
      _sentencePauseDuration = duration;
      notifyListeners();
    }
  }

  /// Set the segment pause duration
  void setSegmentPauseDuration(int duration) {
    if (duration >= 500 && duration <= 3000) {
      _segmentPauseDuration = duration;
      notifyListeners();
    }
  }

  /// Reset all settings to default
  void resetToDefaults() {
    _panelOpacity = 0.7;
    _fontSize = 20.0;
    _narrationSpeed = 0.5;
    _fontFamily = 'Nunito';
    _useNaturalPauses = true;
    _sentencePauseDuration = 650;
    _segmentPauseDuration = 1000;
    notifyListeners();
  }
}
