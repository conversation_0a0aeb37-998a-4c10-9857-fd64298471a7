/// Service for segmenting text into manageable chunks for narration
class TextSegmenter {
  /// Split text into sentences based on punctuation
  static List<String> splitIntoSentences(String text) {
    if (text.isEmpty) return [];

    // Use regex to split while preserving punctuation
    final RegExp sentencePattern = RegExp(r'[^.!?]*[.!?]+');
    final matches = sentencePattern.allMatches(text);

    final sentences = matches
        .map((match) => match.group(0)?.trim() ?? '')
        .where((sentence) => sentence.isNotEmpty)
        .toList();

    // If no sentences were found (text without punctuation), add punctuation
    if (sentences.isEmpty && text.trim().isNotEmpty) {
      return ['${text.trim()}.'];
    }

    return sentences;
  }

  /// Split text into segments of approximately equal length
  static List<String> splitIntoSegments(String text, {int targetLength = 100}) {
    if (text.isEmpty) return [];

    // First try to split by sentences
    final sentences = splitIntoSentences(text);

    if (sentences.isEmpty) {
      // If no sentences found, return the original text
      return [text];
    }

    // If all sentences are short enough, return them as-is
    if (sentences.every((sentence) => sentence.length <= targetLength * 1.5)) {
      return sentences;
    }

    // Otherwise, group sentences into segments
    final segments = <String>[];
    String currentSegment = '';

    for (final sentence in sentences) {
      if (currentSegment.isEmpty) {
        currentSegment = sentence;
      } else if (currentSegment.length + sentence.length + 1 <= targetLength * 1.5) {
        currentSegment += ' $sentence';
      } else {
        segments.add(currentSegment);
        currentSegment = sentence;
      }
    }

    if (currentSegment.isNotEmpty) {
      segments.add(currentSegment);
    }

    return segments.isEmpty ? [text] : segments;
  }

  /// Split text into words for word-by-word highlighting
  static List<String> splitIntoWords(String text) {
    if (text.isEmpty) return [];

    return text
        .split(RegExp(r'\s+'))
        .where((word) => word.isNotEmpty)
        .toList();
  }
}
