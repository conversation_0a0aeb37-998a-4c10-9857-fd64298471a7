import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/src/features/story/providers/story_provider.dart';
import 'package:choice_once_upon_a_time/src/features/story/models/story_model.dart';

void main() {
  group('StoryProvider Optimizations', () {
    late StoryProvider storyProvider;

    setUp(() {
      storyProvider = StoryProvider();
    });

    tearDown(() {
      storyProvider.dispose();
    });

    test('should track validated image information', () {
      // Test that validated image info can be stored and retrieved
      const sceneId = 'test_scene';
      const validPath = 'assets/images/test/scene.png';

      // Initially no validated image
      expect(storyProvider.getValidatedImageInfo(sceneId), isNull);

      // After validation, should be available
      // Note: In real usage, this would be set during story loading
      // Here we're testing the getter functionality
      expect(storyProvider.getValidatedImageInfo(sceneId), isNull);
    });

    test('should track image loading state', () {
      const imagePath = 'assets/images/test/scene.png';

      // Initially not loading
      expect(storyProvider.isImageLoading(imagePath), isFalse);

      // Mark as loading
      storyProvider.markImageAsLoading(imagePath);
      expect(storyProvider.isImageLoading(imagePath), isTrue);

      // Mark as loaded
      storyProvider.markImageAsLoaded(imagePath, 'test_scene');
      expect(storyProvider.isImageLoading(imagePath), isFalse);
      expect(storyProvider.isImageCached('test_scene'), isTrue);
    });

    test('should prevent duplicate image loading', () {
      const imagePath = 'assets/images/test/scene.png';

      // Mark as loading
      storyProvider.markImageAsLoading(imagePath);

      // Should be marked as loading
      expect(storyProvider.isImageLoading(imagePath), isTrue);

      // Attempting to mark as loading again should not change state
      storyProvider.markImageAsLoading(imagePath);
      expect(storyProvider.isImageLoading(imagePath), isTrue);
    });

    test('should cache images per scene', () {
      const sceneId1 = 'scene_1';
      const sceneId2 = 'scene_2';
      const imagePath1 = 'assets/images/test/scene1.png';
      const imagePath2 = 'assets/images/test/scene2.png';

      // Initially not cached
      expect(storyProvider.isImageCached(sceneId1), isFalse);
      expect(storyProvider.isImageCached(sceneId2), isFalse);

      // Cache first image
      storyProvider.markImageAsLoaded(imagePath1, sceneId1);
      expect(storyProvider.isImageCached(sceneId1), isTrue);
      expect(storyProvider.isImageCached(sceneId2), isFalse);

      // Cache second image
      storyProvider.markImageAsLoaded(imagePath2, sceneId2);
      expect(storyProvider.isImageCached(sceneId1), isTrue);
      expect(storyProvider.isImageCached(sceneId2), isTrue);
    });

    test('should clear image state when story changes', () {
      const imagePath = 'assets/images/test/scene.png';
      const sceneId = 'test_scene';

      // Set up some image state
      storyProvider.markImageAsLoading(imagePath);
      storyProvider.markImageAsLoaded(imagePath, sceneId);

      expect(storyProvider.isImageLoading(imagePath), isFalse);
      expect(storyProvider.isImageCached(sceneId), isTrue);

      // Note: In real usage, _clearStoryState() is called when loading a new story
      // This is a private method, so we can't test it directly here
      // But the functionality is tested through integration tests
    });
  });

  group('Scene Navigation Logic', () {
    test('should determine next scene ID correctly', () {
      // Create a mock story with sequential scenes
      final scenes = [
        StoryScene(
          id: 'scene_1',
          narrationText: 'First scene',
          imagePath: 'assets/images/test/scene1.png',
          isChoicePoint: false,
          choices: [],
          isEndingScene: false,
        ),
        StoryScene(
          id: 'scene_2',
          narrationText: 'Second scene',
          imagePath: 'assets/images/test/scene2.png',
          isChoicePoint: false,
          choices: [],
          isEndingScene: false,
        ),
        StoryScene(
          id: 'scene_3',
          narrationText: 'Third scene',
          imagePath: 'assets/images/test/scene3.png',
          isChoicePoint: false,
          choices: [],
          isEndingScene: true,
        ),
      ];

      final story = Story(
        id: 'test_story',
        title: 'Test Story',
        scenes: scenes,
        characters: [],
      );

      // Test that scenes without explicit defaultNextSceneId
      // should auto-advance to the next scene in the array
      expect(scenes[0].defaultNextSceneId, isNull);
      expect(scenes[1].defaultNextSceneId, isNull);

      // The logic for determining next scene is implemented in the provider
      // and would be tested through integration tests with actual story loading
    });
  });
}
