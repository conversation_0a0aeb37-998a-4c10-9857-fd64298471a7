{"aadc_info": {"age_group": 0}, "accessibility": {"captions": {"live_caption_language": "en-US"}}, "account_info": [{"access_point": 17, "account_id": "0003BFFDA06E3E04", "accountcapabilities": {"accountcapabilities/g42tslldmfya": -1, "accountcapabilities/g44tilldmfya": -1, "accountcapabilities/ge2dinbnmnqxa": -1, "accountcapabilities/ge2tkmznmnqxa": -1, "accountcapabilities/ge2tknznmnqxa": -1, "accountcapabilities/ge2tkobnmnqxa": -1, "accountcapabilities/ge3dgmjnmnqxa": -1, "accountcapabilities/ge3dgobnmnqxa": -1, "accountcapabilities/geydgnznmnqxa": -1, "accountcapabilities/geytcnbnmnqxa": -1, "accountcapabilities/gezdcnbnmnqxa": -1, "accountcapabilities/gezdsmbnmnqxa": -1, "accountcapabilities/geztenjnmnqxa": -1, "accountcapabilities/gi2tklldmfya": -1, "accountcapabilities/gu2dqlldmfya": -1, "accountcapabilities/gu4dmlldmfya": -1, "accountcapabilities/guydolldmfya": -1, "accountcapabilities/guzdslldmfya": -1, "accountcapabilities/haytqlldmfya": -1, "accountcapabilities/he4tolldmfya": -1}, "edge_account_age_group": 3, "edge_account_cid": "2b1da38654006967", "edge_account_environment": 0, "edge_account_environment_string": "login.microsoftonline.com", "edge_account_first_name": "<PERSON><PERSON><PERSON><PERSON>", "edge_account_is_test_on_premises_profile": false, "edge_account_last_name": "<PERSON>", "edge_account_location": "IN", "edge_account_oid": "", "edge_account_phone_number": "", "edge_account_puid": "0003BFFDA06E3E04", "edge_account_sovereignty": 0, "edge_account_tenant_id": "9188040d-6c67-4c5b-b112-36a304b66dad", "edge_account_type": 1, "edge_tenant_supports_msa_linking": false, "edge_wam_aad_for_app_account_type": 0, "email": "<EMAIL>", "full_name": "", "gaia": "0003BFFDA06E3E04", "given_name": "", "hd": "", "is_supervised_child": -1, "is_under_advanced_protection": false, "last_downloaded_image_url_with_size": "", "locale": "", "picture_url": ""}], "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "arbitration_using_experiment_config": false, "autocomplete": {"retention_policy_last_version": 136}, "autofill": {"last_version_deduped": 136}, "browser": {"available_dark_theme_options": "All", "chat_v2": {"ip_eligibility_status": {"last_checked_time": "*****************"}}, "edge_sidebar_visibility": {"_game_assist_": {"order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": **********}}, "_gaming_assist_": {"order": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": **********, "523b5ef3-0b10-4154-8b62-10b2ebd00921": **********, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": -**********, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": -**********, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": *********, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": -*********, "96defd79-4015-4a32-bd09-794ff72183ef": **********}}, "add_app_to_bottom": true, "order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": 858993458, "d304175e-bbd4-4b66-8839-9627e56f391f": 429496729}}, "edge_sidebar_visibility_debug": {"order_list": ["Search"], "order_raw_data": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"name": "Search", "pos": "858993458"}, "d304175e-bbd4-4b66-8839-9627e56f391f": {"name": "unknown", "pos": "429496729"}}}, "editor_proofing_languages": {"en": {"Grammar": false, "Spelling": false}, "en-IN": {"Grammar": false, "Spelling": false}, "en-US": {"Grammar": true, "Spelling": true}}, "enable_text_prediction_v2": true, "has_seen_welcome_page": false, "hub_app_non_synced_preferences": {"apps": {"06be1ebe-f23a-4bea-ae45-3120ad86cfea": {"last_path": ""}, "0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": {"last_path": ""}, "168a2510-04d5-473e-b6a0-828815a7ca5f": {"last_path": ""}, "1ec8a5a9-971c-4c82-a104-5e1a259456b8": {"last_path": ""}, "2354565a-f412-4654-b89c-f92eaa9dbd20": {"last_path": ""}, "25fe2d1d-e934-482a-a62f-ea1705db905d": {"last_path": ""}, "2caf0cf4-ea42-4083-b928-29b39da1182b": {"last_path": ""}, "3458dfd2-bf1b-4d00-a6dd-a74a59d523c7": {"last_path": ""}, "35a43603-bb38-4b53-ba20-932cb9117794": {"last_path": ""}, "380c71d3-10bf-4a5d-9a06-c932e4b7d1d8": {"last_path": ""}, "439642fc-998d-4a64-8bb6-940ecaf6b60b": {"last_path": ""}, "523b5ef3-0b10-4154-8b62-10b2ebd00921": {"last_path": ""}, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": {"last_path": ""}, "698b01b4-557a-4a3b-9af7-a7e8138e8372": {"last_path": ""}, "76b926d6-3738-46bf-82d7-2ab896ddf70b": {"last_path": ""}, "7b52ae05-ae84-4165-b083-98ba2031bc22": {"last_path": ""}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"last_path": ""}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"last_path": ""}, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": {"last_path": ""}, "96defd79-4015-4a32-bd09-794ff72183ef": {"last_path": ""}, "9ce3c9c2-462f-4cc9-bbd7-57d656445be0": {"last_path": ""}, "a1a78183-6db3-4789-9e7c-84d157846d55": {"last_path": ""}, "bacc3c12-ebff-44b4-a0f8-ce8b69c9e047": {"last_path": ""}, "c814ae4d-fa0a-4280-a444-cb8bd264828b": {"last_path": ""}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"last_path": ""}, "d3ff4c56-a2b8-4673-ad13-35e7706cc9d1": {"last_path": ""}, "da15ec1d-543d-41c9-94b8-eb2bd060f2c7": {"last_path": ""}, "dadd1f1c-380c-4871-9e09-7971b6b15069": {"last_path": ""}, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": {"last_path": ""}}}, "hub_app_preferences": {"2cb2db96-3bd0-403e-abe2-9269b3761041": {"auto_show": {"enabled": true}}, "439642fc-998d-4a64-8bb6-940ecaf6b60b": {"auto_show": {"enabled": true}}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"all_scenarios": {"auto_open": {"enabled": false}}, "auto_show": {"enabled": false}}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"auto_show": {"enabled": true}}, "apps": {"b0197d6d-a306-4106-8536-17bb5ecfd446": {"last_path": ""}}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"auto_show": {"enabled": true}}, "d304175e-bbd4-4b66-8839-9627e56f391f": {"auto_show": {"enabled": true}}, "default_on_apps_cleanup_state": 1}, "hub_app_usage_preferences": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": 2, "2caf0cf4-ea42-4083-b928-29b39da1182b": 2, "523b5ef3-0b10-4154-8b62-10b2ebd00921": 1, "529de4f7-b6c4-4c76-b36d-c511c9328ebe": 138, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 1, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": 1, "96defd79-4015-4a32-bd09-794ff72183ef": 5, "CleanupCounts": 1, "OpenFirstTime": 1684247859, "cd4688a9-e888-48ea-ad81-76193d56b1be": 13}, "hub_cleanup_candidate_list_for_debug": [{"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}], "hub_cleanup_context": {"cleanup_last_time_v3": 1724126881.971102, "show_days": "00000110101111111111101111011111", "sidebar_autohide_by_cleanup": true, "sidebar_show_last_time": 3712084}, "hub_cleanup_context_v2": {"cleanup_debug_info_v2_adjusted_engaged_app_count": 0, "cleanup_debug_info_v2_app_count_threshold": 1, "cleanup_debug_info_v2_current_sidebar_visibility": 0, "cleanup_debug_info_v2_discover_icon_enabled": true, "cleanup_debug_info_v2_dwell_time_in_secs": 10, "cleanup_debug_info_v2_engaged_app_count": 0, "cleanup_debug_info_v2_expected_sidebar_visibility": 0, "cleanup_debug_info_v2_is_tower_off_by_user": false, "cleanup_debug_info_v2_skip_user_generated_apps_for_threshold": true, "cleanup_debug_info_v2_user_generated_app_count": 0, "hub_app_cleanup_v2_done": true}, "recent_theme_color_list": [4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0], "show_edge_split_window_toolbar_button": false, "show_hub_app_in_sidebar_buttons": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": 3, "168a2510-04d5-473e-b6a0-828815a7ca5f": 1, "1ec8a5a9-971c-4c82-a104-5e1a259456b8": 1, "2354565a-f412-4654-b89c-f92eaa9dbd20": 0, "2caf0cf4-ea42-4083-b928-29b39da1182b": 3, "523b5ef3-0b10-4154-8b62-10b2ebd00921": 3, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": 3, "76b926d6-3738-46bf-82d7-2ab896ddf70b": 3, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": 3, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": 3, "96defd79-4015-4a32-bd09-794ff72183ef": 3, "9ce3c9c2-462f-4cc9-bbd7-57d656445be0": 3, "cd4688a9-e888-48ea-ad81-76193d56b1be": 0, "d304175e-bbd4-4b66-8839-9627e56f391f": 0, "d3ff4c56-a2b8-4673-ad13-35e7706cc9d1": 1, "da15ec1d-543d-41c9-94b8-eb2bd060f2c7": 1, "dadd1f1c-380c-4871-9e09-7971b6b15069": 3, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": 1, "ec843dfc-b28b-4030-9fb9-edcf38942a5e": 1}, "show_hub_apps_tower_pinned": false, "show_toolbar_collections_button": false, "toolbar_browser_essentials_button_pinned": false, "underside_chat_bing_signed_in_status": false, "underside_chat_consent": 0, "user_level_features_context": {}, "window_placement": {"bottom": 808, "left": 8, "maximized": true, "right": 1048, "top": 8, "work_area_bottom": 816, "work_area_left": 0, "work_area_right": 1536, "work_area_top": 0}}, "browser_content_container_height": 742, "browser_content_container_width": 1536, "browser_content_container_x": 0, "browser_content_container_y": 74, "browser_essentials": {"show_hub_fre": false, "show_safety_fre": false}, "collections": {"prism_collection": {"enroll": {"rule_version": 1, "state": 2}}, "prism_collections": {"enabled": 0, "migration": {"accepted": true, "completed": 2, "item_count": 0}, "policy": {"cached": 2}, "wns": {"last_subscribe_time": "13392655970457076", "subscription_id": "1;10850822064287639336"}}}, "commerce_daily_metrics_last_update_time": "13392628757791452", "copilot_vision": {"user_access": true}, "countryid_at_install": 18766, "credentials_enable_breachdetection": true, "custom_links": {"list": []}, "default_search_provider": {"guid": ""}, "devtools": {"preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "dual_engine": {"consumer_site_list_with_ie_entries": false, "consumer_sitelist_location": "", "consumer_sitelist_version": "", "external_consumer_shared_cookie_data": {}, "shared_cookie_data": {}, "sitelist_has_consumer_data": false, "sitelist_has_enterprise_data": false, "sitelist_location": "", "sitelist_source": 0, "sitelist_version": "", "user_list_data_1": {}}, "edge": {"account_type": 1, "bookmarks": {"last_dup_info_record_time": "*****************"}, "msa_sso_info": {"allow_for_non_msa_profile": false}, "profile_matches_os_primary_account": false, "profile_sso_info": {"aad_sso_algo_state": 1, "is_first_profile": true, "is_msa_first_profile": true, "msa_sso_algo_state": 2, "msa_sso_state_reached_by": 3}, "profile_sso_option": 1, "services": {"last_gaia_id": "0003BFFDA06E3E04", "signin_scoped_device_id": "559ed093-8f02-4d90-9523-73f73347e69d"}, "workspaces": {"state": "{\"edgeWorkspacePrefsVersion\":2,\"enableFluid\":true,\"failedRestartSpaceId\":\"\",\"failedToConnectToFluid\":false,\"fluidStatus\":0,\"fre_shown\":false,\"fromCache\":false,\"isFluidPreferencesConnected\":false,\"isSpaceOpening\":false,\"openingSpaceId\":\"\",\"statusForScreenReaders\":\"\",\"workspacePopupMode\":0,\"workspacesForExternalLinks\":[]}"}}, "edge_cloud_messaging": {"cached_target_token": {"cv": "979024205469192448", "target_token": "3A4iZxV6Y05JqcDplAO2YA==$FIZYEDh3oB5p123NGdh1nYkRwvaCsOqt4CnOvDFzelAsNbbAW3Dufri2uHAwXUeOPfi3zm77cprG+ADXLb7R99N+zy9p929jHoF5Vc8CPyDxWFP7d9+ayfRkxSUM2EGKRixAz599c4G4kXqBnUZGrB2AeEtHdnvLLZVM1XlFxce9qfIYYUu3Wmu7uvI/p6TC", "time": "13392655959398214"}}, "edge_rewards": {"cache_data": "CAEQ4AMYAEoCaW4=", "hva_promotions": [], "promotions": [{"attributes": {"State": "<PERSON><PERSON><PERSON>", "activityprogress": "0", "animated_icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/newEdgeLogo.json", "bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_small.png", "complete": "False", "description": "Search here for 3 days and earn an extra 3,100 points.", "destination": "", "edgebar_description": "", "edgebar_disclaimer": "Offer valid for 1 person/account within 7 days of joining the challenge", "edgebar_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Microsoft_giftcard_grey.png", "edgebar_link_text": "Get started", "edgebar_title": "Welcome to search bar powered by Microsoft Edge! Get a free gift card when you search here for 3 days.", "edgebar_type": "eligible", "give_eligible": "False", "icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/newEdgeLogo.json", "image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/278x180/Star-magenta-278x180px.png", "link_text": "Get started", "max": "0", "offerid": "eligibility_EdgeBarMicrosoft_202211_ML293H", "progress": "0", "promotional": "0", "sc_bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_medium.png", "sc_bg_large_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_large.png", "small_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Mobile/newEdgeLogo_75x75.png", "title": "Earn bonus Microsoft Rewards points", "type": "url<PERSON><PERSON>"}, "name": "ENIN_eligibility_EdgeBarMicrosoft_202211_ML293H_info", "priority": -1, "tags": ["exclude_give_pcparent", "non_global_config"]}], "refresh_status_muted_until": "13393260757461233"}, "edge_ux_config": {"assignmentcontext": "o07UCH/Bcn51h5sT+HrCUOO5iIwnF4A26tIm/I1j/mo=", "dataversion": "254461117", "experimentvariables": {"2f717976": {"edgeServerUX.sync.historyDataTypeEnabled": true}, "shop-60c": {"edgeServerUX.shopping.aablockth": 60, "edgeServerUX.shopping.block99": false}, "shopphinsightsv2-t": {"edgeServerUX.shopping.enablePhInsightsV2": true}, "shopppdismisstreatment": {"edgeServerUX.shopping.msEdgeShoppingCashbackDismissTimeout2s": true}, "shoprevenuattributiont": {"edgeServerUX.shopping.disableCashbackOnCouponCopy": true}}, "flights": {"2f717976": "31213786", "shop-60c": "31271455", "shopphinsightsv2-t": "31303589", "shopppdismisstreatment": "31004791", "shoprevenuattributiont": "31235887"}, "latestcorrelationid": "Ref A: 606151DC417344D48B51975E7C8C4229 Ref B: DEL01EDGE0418 Ref C: 2025-05-25T14:12:38Z"}, "edge_wallet": {"passwords": {"password_lost_report_date": "13392544797449328"}, "trigger_funnel": {"records": []}}, "enterprise_profile_guid": "0d8107e4-ff53-4f4d-8871-3c4bf44a5338", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "136.0.7103.114", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": [], "ui": {"allow_chrome_webstore": true}}, "family_safety": {"activity_reporting_enabled": false, "web_filtering_enabled": false}, "fsd": {"retention_policy_last_version": 136}, "gaia_cookie": {"changed_time": **********.859933, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"consented_to_sync": false, "signin": {"LAST_SIGNIN_ACCESS_POINT": {"time": "2025-05-25T14:12:38.956Z", "value": "17"}}, "signin_scoped_device_id": "2b61edd5-e6da-4185-8928-7e5a9e23680e"}}, "history": {"thumbnail_visibility": true, "thumbnail_visibility_per_usage": true}, "history_clusters": {"all_cache": {"all_keywords": {}, "all_timestamp": "0"}, "short_cache": {"short_keywords": {}, "short_timestamp": "0"}}, "import_items_failure_state": {"reimport": {"ie_react": 62432}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "13392500383682654", "recent_session_start_times": ["13392647259795583", "13392628757706200", "13392541316352327", "13392500383682654"], "session_last_active_time": "13392662464042123", "session_start_time": "13392647259795583"}, "intl": {"accept_languages": "en-US,en,en-IN", "selected_languages": "en-US,en,en-IN"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "EoPCaI2Wslxr2Uih0bMt9ffBSaJYYNjPuB3cU59a1qzQQQHXdPbXcIBDxR2h1Mei8nbbwQ0Us4624Oqm+H8udQ=="}, "muid": {"last_sync": "13392655957706585", "values_seen": ["0C9E1DE2A9FE62EE1BB30815A8F86316"]}, "ntp": {"background_image_type": "imageAndVideo", "feed_engagement_time": "13392628432745632", "layout_mode": 2, "news_feed_display": "always", "next_site_suggestions_available": false, "num_personal_suggestions": 2, "record_user_choices": [{"setting": "tscollapsed", "source": "tscollapsed_to_off", "timestamp": 1695212032291.0, "value": 0}, {"setting": "seen_interest_fre_count", "source": "ntp", "timestamp": 1702443747810.0, "value": 3}, {"setting": "breaking_news_dismissed", "source": "ntp", "timestamp": 1748154831981.0, "value": {}}], "show_greeting": true}, "nurturing": {"time_of_last_sync_consent_view": "13392655958974965"}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "13392662388008419", "last_fetch_success": "13392662388305332"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "EWALLET_MERCHANT_ALLOWLIST": true, "FORMS_ANNOTATIONS": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": true}, "personalization_data_consent": {"how_set": 4, "personalization_in_context_consent_can_prompt": false, "personalization_in_context_count": 0, "personalization_in_context_has_prompted": false, "when_set": "*****************"}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:49688,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:53753,*": {"expiration": "13400423329795275", "last_modified": "13392647329795281", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:53864,*": {"expiration": "13400276490875037", "last_modified": "13392500490875042", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:54377,*": {"expiration": "13400317346164006", "last_modified": "13392541346164012", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:55762,*": {"expiration": "13400425325439105", "last_modified": "13392649325439112", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:57163,*": {"expiration": "13400317597503461", "last_modified": "13392541597503465", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:57400,*": {"expiration": "13400319088515396", "last_modified": "13392543088515401", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:58500,*": {"expiration": "13400319514877146", "last_modified": "13392543514877152", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:60595,*": {"expiration": "13400404793971821", "last_modified": "13392628793971827", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:61908,*": {"expiration": "13400427083305305", "last_modified": "13392651083305311", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:61911,*": {"expiration": "13400438475013491", "last_modified": "13392662475013500", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62056,*": {"expiration": "13400427122888508", "last_modified": "13392651122888513", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62296,*": {"expiration": "13400427534144226", "last_modified": "13392651534144232", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62459,*": {"expiration": "13400427897456309", "last_modified": "13392651897456317", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62627,*": {"expiration": "13400431870800993", "last_modified": "13392655870801006", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62781,*": {"expiration": "13400427941831169", "last_modified": "13392651941831174", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62982,*": {"expiration": "13400428070863284", "last_modified": "13392652070863291", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63071,*": {"expiration": "13400428199983972", "last_modified": "13392652199983979", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63106,*": {"expiration": "13400433135175312", "last_modified": "13392657135175324", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63355,*": {"expiration": "13400431666717057", "last_modified": "13392655666717067", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {"https://ukpsc.net.in:443,*": {"expiration": "0", "last_modified": "13288105279776783", "model": 0, "setting": 1}}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"http://localhost:49688,*": {"last_modified": "13392661417423230", "setting": {"lastEngagementTime": 1.3392661417423208e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 6.599999999999999, "rawScore": 6.599999999999999}}, "http://localhost:53753,*": {"last_modified": "13392647322791687", "setting": {"lastEngagementTime": 1.3392647322791672e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.3999999999999995, "rawScore": 5.3999999999999995}}, "http://localhost:53864,*": {"last_modified": "13392628757759534", "setting": {"lastEngagementTime": 1.3392569242485124e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}, "http://localhost:54377,*": {"last_modified": "13392628757759524", "setting": {"lastEngagementTime": 1.3392598042902496e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "http://localhost:55762,*": {"last_modified": "13392649247963521", "setting": {"lastEngagementTime": 1.3392649247963512e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:55928,*": {"last_modified": "13392649353688589", "setting": {"lastEngagementTime": 1.3392649353688578e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:57163,*": {"last_modified": "13392628757759515", "setting": {"lastEngagementTime": 1.3392598311748512e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.8}}, "http://localhost:57400,*": {"last_modified": "13392628757759503", "setting": {"lastEngagementTime": 1.3392599799078212e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 9.599999999999998}}, "http://localhost:58500,*": {"last_modified": "13392628757759447", "setting": {"lastEngagementTime": 1.3392599957759388e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.2}}, "http://localhost:60595,*": {"last_modified": "13392628768537843", "setting": {"lastEngagementTime": 1.3392628768537808e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:61794,*": {"last_modified": "13392650937991967", "setting": {"lastEngagementTime": 1.3392650937991952e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:61908,*": {"last_modified": "13392651020343919", "setting": {"lastEngagementTime": 1.3392651020343872e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:61911,*": {"last_modified": "13392662433263359", "setting": {"lastEngagementTime": 1.3392662433263328e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}, "http://localhost:62056,*": {"last_modified": "13392651109722182", "setting": {"lastEngagementTime": 1.3392651109722176e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:62296,*": {"last_modified": "13392651524436384", "setting": {"lastEngagementTime": 1.3392651524436368e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.2, "rawScore": 4.2}}, "http://localhost:62459,*": {"last_modified": "13392651575255784", "setting": {"lastEngagementTime": 1.3392651575255768e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:62627,*": {"last_modified": "13392655846124468", "setting": {"lastEngagementTime": 1.3392655846124432e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:62781,*": {"last_modified": "13392651938249492", "setting": {"lastEngagementTime": 1.3392651938249456e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:62898,*": {"last_modified": "13392655957952193", "setting": {"lastEngagementTime": 1.3392655957952172e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:62982,*": {"last_modified": "13392652056493513", "setting": {"lastEngagementTime": 1.33926520564935e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:63071,*": {"last_modified": "13392652196120287", "setting": {"lastEngagementTime": 1.3392652196120276e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}, "http://localhost:63106,*": {"last_modified": "13392656123080482", "setting": {"lastEngagementTime": 1.3392656123080456e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.3999999999999995, "rawScore": 5.3999999999999995}}, "http://localhost:63355,*": {"last_modified": "13392655654729434", "setting": {"lastEngagementTime": 1.3392655654729392e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:64560,*": {"last_modified": "13392659463785575", "setting": {"lastEngagementTime": 1.339265946378554e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "136.0.7103.114", "creation_time": "13392500383643688", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_profile_id": "b7b17a46-7289-48c4-a3d1-e5b468539950", "edge_user_with_non_zero_passwords": true, "exit_type": "Normal", "family_member_role": "not_in_family", "hard_yes_password_monitor_consent": true, "is_relative_to_aad": false, "last_engagement_time": "13392662433263329", "last_time_obsolete_http_credentials_removed": 1748026843.667839, "last_time_password_monitor_consent_shown": "13285067729068742", "last_time_password_store_metrics_reported": 1748155187.703079, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chrome", "network_pbs": {"c2faa7c1": {"last_updated": "13392544777709469", "pb": 6}}, "number_password_monitor_consent_shown": 1, "password_breach_last_scanned_error": 0, "password_breach_last_scanned_time": "13392655968537799", "password_breach_scan_trigger_last_reset_time": "13339771245395414", "password_breach_scan_triggered_count": 0, "password_breach_scan_triggered_password_count": 0, "password_breach_scanned": true, "password_hash_data_list": [], "were_old_google_logins_removed": true}, "reading_view": {"last_access_time": "13285333733832602"}, "reset_prepopulated_engines": false, "safebrowsing": {"advanced_protection_last_refresh": "13392655959427970", "event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13392759584515018", "hash_real_time_ohttp_key": "0QAgQZygJtLf6q/rDOcCngz5gv7t6Y52KvDoA00aTin0d0wABAABAAI=", "metrics_last_log_time": "13392628757", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQhMXr4oeN5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEMPF6+KHjeUXCuUCChFjcm9zc19kZXZpY2VfdXNlchLPAgrDAg0AAIA/EOO2lYDmkOUXGrACCqcCGqQCChkNAACAPxISTm9Dcm9zc0RldmljZVVzYWdlChgNAAAAQBIRQ3Jvc3NEZXZpY2VNb2JpbGUKGQ0AAEBAEhJDcm9zc0RldmljZURlc2t0b3AKGA0AAIBAEhFDcm9zc0RldmljZVRhYmxldAoiDQAAoEASG0Nyb3NzRGV2aWNlTW9iaWxlQW5kRGVza3RvcAohDQAAwEASGkNyb3NzRGV2aWNlTW9iaWxlQW5kVGFibGV0CiINAADgQBIbQ3Jvc3NEZXZpY2VEZXNrdG9wQW5kVGFibGV0CiANAAAAQRIZQ3Jvc3NEZXZpY2VBbGxEZXZpY2VUeXBlcwoXDQAAEEESEENyb3NzRGV2aWNlT3RoZXISEk5vQ3Jvc3NEZXZpY2VVc2FnZRIEEAcYBCACEPu2lYDmkOUX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13392518399000000", "uma_in_sql_start_time": "13392500383665536"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392651897449989", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392651927608337", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392651941826804", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392652056220908", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392652070858920", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392652097912933", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392652199979558", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392655625397739", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392655666714069", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392655787864919", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392655870792802", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392655957479290", "type": 0}, {"crashed": false, "time": "13392656058290564", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392657134282976", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392659449666682", "type": 0}, {"crashed": false, "time": "13392661259673448", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "1339*************", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "shopping": {"contextual_features_enabled": true, "dma_telemetry_expiration_time": "*****************", "pcb_supported": true}, "should_read_incoming_syncing_theme_prefs": false, "signin": {"accounts_metadata_dict": {}, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "smart_explore": {"auto_cleanup_date": "*****************"}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "surf_game": {"buoy_highscore": -1, "classic_highscore": 1599, "speed_highscore": -1}, "sync": {"apps": true, "autofill": true, "bookmarks": true, "collections": true, "collections_edge_re_evaluated": true, "collections_edge_supported": true, "data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "edge_account_type": 1, "edge_wallet": true, "edge_wallet_edge_supported": true, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": true, "extensions_edge_supported": true, "feature_status_for_sync_to_signin": 5, "has_been_enabled": true, "history_edge_supported": true, "keep_everything_synced": true, "keystore_encryption_key_state": "****************************************************************************************************************************************************************************************************************************************************************************************", "local_device_guids_with_timestamp": [{"cache_guid": "DuqudsEYOKgpz+wkmMKF0Q==", "timestamp": 155007}], "passwords": true, "passwords_per_account_pref_migration_done": true, "preferences": true, "tabs": true, "tabs_edge_supported": true, "transport_data_per_account": {}, "typed_urls": true}, "sync_consent_recorded": true, "sync_profile_info": {"edge_ci_consent_last_modified_date": "*****************", "edge_ci_consent_last_shown_date": "*****************", "edge_ci_is_option_explicitly_selectedby_user": false, "edge_san_consent_last_modified_date": "*****************", "edge_san_consent_last_shown_date": "*****************", "edge_san_is_option_explicitly_selectedby_user": false}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "tab_groups_migration_version": 3, "third_party_search": {"consented": true}, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "toolbar_declutter": {"new_user_cleanup_triggered": true, "undo": {"last_time": "*****************"}}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "tracking_prevention": {"strict_inprivate": false}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled": true, "personalization_data_consent_enabled_last_known_value": true}, "video_enhancement": {"mode": "Non-AI enhancement"}, "visual_search": {"dma_state": 1}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "136", "link_handling_info": {"enabled_for_installed_apps": true}}}