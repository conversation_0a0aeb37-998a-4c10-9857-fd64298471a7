import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/story_settings_provider.dart';
import '../providers/story_provider.dart';

/// Settings panel for story interaction customization
class SettingsPanel extends StatelessWidget {
  final VoidCallback onClose;

  const SettingsPanel({
    super.key,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(77),
            blurRadius: 10,
            offset: const Offset(-2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              border: Border(
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.settings, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Settings',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: onClose,
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),
          
          // Settings content
          Expanded(
            child: Consumer2<StorySettingsProvider, StoryProvider>(
              builder: (context, settingsProvider, storyProvider, child) {
                return ListView(
                  padding: const EdgeInsets.all(16),
                  children: [
                    _buildPanelOpacitySection(settingsProvider),
                    const SizedBox(height: 24),
                    _buildFontSizeSection(settingsProvider),
                    const SizedBox(height: 24),
                    _buildSpeechRateSection(settingsProvider, storyProvider),
                    const SizedBox(height: 24),
                    _buildVolumeSection(settingsProvider, storyProvider),
                    const SizedBox(height: 24),
                    _buildAutoAdvanceSection(settingsProvider),
                    const SizedBox(height: 24),
                    _buildResetSection(settingsProvider),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPanelOpacitySection(StorySettingsProvider settingsProvider) {
    return _buildSettingSection(
      title: 'Panel Transparency',
      child: Column(
        children: [
          Slider(
            value: settingsProvider.panelOpacity,
            min: 0.3,
            max: 1.0,
            divisions: 7,
            label: '${(settingsProvider.panelOpacity * 100).round()}%',
            onChanged: settingsProvider.setPanelOpacity,
          ),
          Text(
            'Opacity: ${(settingsProvider.panelOpacity * 100).round()}%',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildFontSizeSection(StorySettingsProvider settingsProvider) {
    return _buildSettingSection(
      title: 'Text Size',
      child: Column(
        children: [
          Slider(
            value: settingsProvider.fontSize,
            min: 12.0,
            max: 32.0,
            divisions: 10,
            label: '${settingsProvider.fontSize.round()}pt',
            onChanged: settingsProvider.setFontSize,
          ),
          Text(
            'Size: ${settingsProvider.fontSize.round()}pt',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildSpeechRateSection(
    StorySettingsProvider settingsProvider,
    StoryProvider storyProvider,
  ) {
    return _buildSettingSection(
      title: 'Speech Speed',
      child: Column(
        children: [
          Slider(
            value: settingsProvider.speechRate,
            min: 0.1,
            max: 1.0,
            divisions: 9,
            label: '${(settingsProvider.speechRate * 100).round()}%',
            onChanged: (value) {
              settingsProvider.setSpeechRate(value);
              storyProvider.updateTTSSettings(speechRate: value);
            },
          ),
          Text(
            'Speed: ${(settingsProvider.speechRate * 100).round()}%',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildVolumeSection(
    StorySettingsProvider settingsProvider,
    StoryProvider storyProvider,
  ) {
    return _buildSettingSection(
      title: 'Volume',
      child: Column(
        children: [
          Slider(
            value: settingsProvider.volume,
            min: 0.0,
            max: 1.0,
            divisions: 10,
            label: '${(settingsProvider.volume * 100).round()}%',
            onChanged: (value) {
              settingsProvider.setVolume(value);
              storyProvider.updateTTSSettings(volume: value);
            },
          ),
          Text(
            'Volume: ${(settingsProvider.volume * 100).round()}%',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildAutoAdvanceSection(StorySettingsProvider settingsProvider) {
    return _buildSettingSection(
      title: 'Auto-Advance',
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('Auto-advance to next scene'),
            subtitle: const Text('Automatically proceed when narration ends'),
            value: settingsProvider.autoAdvance,
            onChanged: settingsProvider.setAutoAdvance,
            contentPadding: EdgeInsets.zero,
          ),
          if (settingsProvider.autoAdvance) ...[
            const SizedBox(height: 8),
            Text(
              'Delay: ${settingsProvider.autoAdvanceDelay}ms',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            Slider(
              value: settingsProvider.autoAdvanceDelay.toDouble(),
              min: 500,
              max: 3000,
              divisions: 5,
              label: '${settingsProvider.autoAdvanceDelay}ms',
              onChanged: (value) => settingsProvider.setAutoAdvanceDelay(value.round()),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildResetSection(StorySettingsProvider settingsProvider) {
    return _buildSettingSection(
      title: 'Reset',
      child: ElevatedButton.icon(
        onPressed: settingsProvider.resetToDefaults,
        icon: const Icon(Icons.restore),
        label: const Text('Reset to Defaults'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.orange,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }

  Widget _buildSettingSection({
    required String title,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        child,
      ],
    );
  }
}
