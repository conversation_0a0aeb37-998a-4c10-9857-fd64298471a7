Guidelines for AugmentAI Coder
These guidelines are for the AI Coder (AugmentAI) to follow during the development of "Choice: Once Upon A Time."

I. Core Principles:
Follow Roadmap: All coding tasks directly correspond to tasks in the "Choice: Once Upon A Time" MVP Roadmap.
Clarity & Maintainability: Prioritize readable, well-documented, and easily maintainable code.
Flutter/Dart Best Practices: Adhere strictly to established best practices.
Performance: Generate efficient code.
II. Detailed Coding Guidelines:
Code Style & Formatting:
Formatting: Use dart format.
Naming Conventions: PascalCase for classes/widgets, camelCase for variables/functions, snake_case for files/directories.
Clarity: Use descriptive names.
Comments: Dart documentation comments (///) for public APIs; inline comments (//) for complex logic.
Flutter Development Best Practices:
Widget Granularity: Favor smaller, reusable widgets.
Stateless vs. StatefulWidget: Default to StatelessWidget; use StatefulWidget for local, ephemeral state.
const Constructors: Use const where possible.
Build Method Purity: Keep build methods free of side effects.
Widget Tree Depth: Avoid excessive nesting; refactor.
Context Awareness: Use BuildContext correctly.
Project Architecture (Modular Design):
Feature-First Directory Structure: Organize code by feature (e.g., lib/src/features/story_listing/). Each feature has subdirectories for widgets/, screens/, models/, services/, state/.
Separation of Concerns: Separate UI, business logic (state management), and data layer.
Shared Components: Place reusable elements in lib/src/shared_kernel/ or lib/src/common/.
Dependency Injection (DI): Use provider (or specified alternative).
State Management (Primary: Provider):
Consistently use Provider.
Use ChangeNotifierProvider, Consumer, Selector, context.watch, context.read appropriately.
Encapsulate state logic in ChangeNotifier classes.
Scope providers appropriately.
UI/UX Implementation (Responsive & Accessible):
Responsiveness: Use MediaQuery, LayoutBuilder, Expanded, Flexible, etc. Adapt for Android Phone (portrait) & TV/Tablet (landscape). Implement TV D-pad focus.
Accessibility: Min. touch target 44-48dp. Use Semantics where needed.
Visuals: Adhere to UI design specs, child-friendly aesthetics.
Assets: Correctly reference assets from pubspec.yaml. Use flutter_svg for SVGs.
Firebase Integration:
Use official Firebase plugins (firebase_core, firebase_auth, cloud_firestore, etc.).
Implement operations asynchronously (async/await). Handle errors.
Structure Firestore data per predefined models.
Abstract Firebase calls into service classes.
Content & Story Logic:
Implement logic to parse story data from specified JSON structure.
Develop robust choice-handling and story branching.
Integrate TTS (e.g., flutter_tts).
Monetization (In-App Purchase):
Use in_app_purchase plugin.
Generate code for fetching products, purchasing, verifying, unlocking.
Error Handling & Logging:
Comprehensive error handling (try-catch). User-friendly error messages.
Integrate FirebaseCrashlytics.
Use debugPrint() or logging package for development logs (conditional/removable).
Testing:
Generate testable code.
Write unit tests for business logic and widget tests for UI components if requested.
Asynchronous Operations:
Manage Futures/Streams correctly. Use FutureBuilder/StreamBuilder or integrate with state management for loading/error/data states.
Security:
Do not embed sensitive keys in client code unless part of standard config files.
Client code should interact correctly with Firebase security rules.
III. Communication & Iteration:
Context is Key: Expect task context from Primary Developer/AI Mentor.
Clarification: Ask if requirements are unclear.
Assumptions: State any assumptions made.
Iterative Development: Be prepared to refactor/modify code based on feedback. 
