rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidParentProfile() {
      return request.resource.data.keys().hasAll(['email', 'displayName', 'createdAt', 'updatedAt', 'childProfileIds', 'isPremium']) &&
             request.resource.data.email is string &&
             request.resource.data.displayName is string &&
             request.resource.data.createdAt is timestamp &&
             request.resource.data.updatedAt is timestamp &&
             request.resource.data.childProfileIds is list &&
             request.resource.data.isPremium is bool;
    }
    
    function isValidChildProfile() {
      return request.resource.data.keys().hasAll(['name', 'parentId', 'createdAt', 'lastActive', 'storyProgress', 'preferences']) &&
             request.resource.data.name is string &&
             request.resource.data.parentId is string &&
             request.resource.data.createdAt is timestamp &&
             request.resource.data.lastActive is timestamp &&
             request.resource.data.storyProgress is map &&
             request.resource.data.preferences is map;
    }
    
    function isParentOfChild(childDoc) {
      return isAuthenticated() && childDoc.data.parentId == request.auth.uid;
    }
    
    // Parent profiles - users can only access their own profile
    match /parents/{parentId} {
      allow read, write: if isOwner(parentId) && isValidParentProfile();
      allow create: if isOwner(parentId) && isValidParentProfile();
    }
    
    // Child profiles - parents can only access their own children's profiles
    match /children/{childId} {
      allow read, write: if isParentOfChild(resource) && isValidChildProfile();
      allow create: if isAuthenticated() && 
                       isValidChildProfile() && 
                       request.resource.data.parentId == request.auth.uid;
      allow delete: if isParentOfChild(resource);
    }
    
    // Story progress data (if stored separately)
    match /story_progress/{progressId} {
      allow read, write: if isAuthenticated() && 
                            resource.data.parentId == request.auth.uid;
      allow create: if isAuthenticated() && 
                       request.resource.data.parentId == request.auth.uid;
    }
    
    // Purchase records (if stored separately)
    match /purchases/{purchaseId} {
      allow read, write: if isAuthenticated() && 
                            resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && 
                       request.resource.data.userId == request.auth.uid;
    }
    
    // Analytics data (read-only for users, write-only for system)
    match /analytics/{document=**} {
      allow read: if false; // Users cannot read analytics
      allow write: if false; // Only server-side functions can write analytics
    }
    
    // App configuration (read-only for all authenticated users)
    match /config/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Only admins can write config
    }
    
    // Stories metadata (read-only for all authenticated users)
    match /stories/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Only admins can write stories
    }
    
    // Default deny rule for all other documents
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
