import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import '../../lib/src/features/story/models/story_model.dart';
import '../../lib/src/features/story/providers/story_provider.dart';
import '../../lib/src/features/story/providers/story_settings_provider.dart';
import '../../lib/src/features/story/services/tts_service.dart';

void main() {
  group('Comprehensive Narration Flow Control Tests', () {
    late StoryProvider storyProvider;
    late StorySettingsProvider settingsProvider;
    late TTSService ttsService;

    setUp(() {
      storyProvider = StoryProvider();
      settingsProvider = StorySettingsProvider();
      ttsService = TTSService();
    });

    tearDown(() {
      ttsService.dispose();
    });

    test('TTS Service should have natural pause settings', () {
      // Test that TTS service has the new natural pause functionality
      expect(ttsService.useNaturalPauses, isTrue);
      expect(ttsService.narrationSpeed, equals(0.5));
      expect(ttsService.sentencePauseDuration, equals(650));
      expect(ttsService.segmentPauseDuration, equals(1000));

      // Test setting natural pause settings
      ttsService.setNaturalPauseSettings(
        useNaturalPauses: false,
        sentencePauseDuration: 500,
        segmentPauseDuration: 800,
      );

      expect(ttsService.useNaturalPauses, isFalse);
      expect(ttsService.sentencePauseDuration, equals(500));
      expect(ttsService.segmentPauseDuration, equals(800));
    });

    testWidgets('StoryProvider should enforce complete scene narration', (WidgetTester tester) async {
      // Create a test story with multiple text segments
      final testStory = Story(
        id: 'test-story',
        title: 'Test Story',
        coverImagePath: 'assets/images/test/cover.png',
        characters: [],
        scenes: [
          StoryScene(
            id: 'scene1',
            narrationText: 'This is the first sentence. This is the second sentence. This is the third sentence.',
            imagePath: 'assets/images/test/scene1.png',
            isChoicePoint: true,
            isEndingScene: false,
            choices: [
              StoryChoice(text: 'Choice 1', nextSceneId: 'scene2'),
              StoryChoice(text: 'Choice 2', nextSceneId: 'scene3'),
            ],
          ),
          StoryScene(
            id: 'scene2',
            narrationText: 'You chose the first option.',
            imagePath: 'assets/images/test/scene2.png',
            isChoicePoint: false,
            isEndingScene: true,
            choices: [],
          ),
        ],
      );

      await storyProvider.setActiveStoryForTesting(testStory);

      // Initially, navigation should be blocked
      expect(storyProvider.isNavigationBlocked, isTrue);
      expect(storyProvider.isSceneNarrationComplete, isFalse);
      expect(storyProvider.showChoices, isFalse);

      // Simulate completing the first segment
      storyProvider.completeNarration();

      // Should advance to next segment, not show choices yet
      expect(storyProvider.isSceneNarrationComplete, isFalse);
      expect(storyProvider.showChoices, isFalse);
      expect(storyProvider.currentSegmentIndex, greaterThan(0));

      // Complete all segments by calling completeNarration multiple times
      while (!storyProvider.isSceneNarrationComplete) {
        storyProvider.completeNarration();
      }

      // Now scene narration should be complete and choices should show
      expect(storyProvider.isSceneNarrationComplete, isTrue);
      expect(storyProvider.isNavigationBlocked, isFalse);
      expect(storyProvider.showChoices, isTrue);
    });

    testWidgets('StorySettingsProvider should have narration controls', (WidgetTester tester) async {
      // Test default settings
      expect(settingsProvider.narrationSpeed, equals(0.5));
      expect(settingsProvider.useNaturalPauses, isTrue);
      expect(settingsProvider.sentencePauseDuration, equals(650));
      expect(settingsProvider.segmentPauseDuration, equals(1000));

      // Test setting narration speed
      settingsProvider.setNarrationSpeed(1.2);
      expect(settingsProvider.narrationSpeed, equals(1.2));

      // Test setting natural pause settings
      settingsProvider.setUseNaturalPauses(false);
      expect(settingsProvider.useNaturalPauses, isFalse);

      settingsProvider.setSentencePauseDuration(800);
      expect(settingsProvider.sentencePauseDuration, equals(800));

      settingsProvider.setSegmentPauseDuration(1200);
      expect(settingsProvider.segmentPauseDuration, equals(1200));

      // Test reset to defaults
      settingsProvider.resetToDefaults();
      expect(settingsProvider.narrationSpeed, equals(0.5));
      expect(settingsProvider.useNaturalPauses, isTrue);
      expect(settingsProvider.sentencePauseDuration, equals(650));
      expect(settingsProvider.segmentPauseDuration, equals(1000));
    });

    test('TTS Service should have sentence splitting capability', () {
      // Test that the service has the necessary properties for sentence handling
      expect(ttsService.sentences, isA<List<String>>());
      expect(ttsService.currentSentenceIndex, isA<int>());

      // Test that narration settings can be retrieved
      final settings = ttsService.getNarrationSettings();
      expect(settings, isA<Map<String, dynamic>>());
      expect(settings['narrationSpeed'], equals(0.5));
      expect(settings['useNaturalPauses'], isTrue);
    });

    test('Navigation should be blocked until scene narration completes', () {
      // Test that navigation methods respect the blocking
      final testStory = Story(
        id: 'test-story',
        title: 'Test Story',
        coverImagePath: 'assets/images/test/cover.png',
        characters: [],
        scenes: [
          StoryScene(
            id: 'scene1',
            narrationText: 'Test narration text.',
            imagePath: 'assets/images/test/scene1.png',
            isChoicePoint: false,
            isEndingScene: false,
            choices: [],
          ),
          StoryScene(
            id: 'scene2',
            narrationText: 'Second scene text.',
            imagePath: 'assets/images/test/scene2.png',
            isChoicePoint: false,
            isEndingScene: true,
            choices: [],
          ),
        ],
      );

      storyProvider.setActiveStoryForTesting(testStory);

      // Initially blocked
      expect(storyProvider.isNavigationBlocked, isTrue);
      expect(storyProvider.isSceneNarrationComplete, isFalse);

      // Try to navigate to next segment - should work for manual navigation
      final canNavigate = storyProvider.navigateToNextSegment();
      expect(canNavigate, isTrue); // Manual navigation is allowed

      // Complete narration to unblock
      storyProvider.completeNarration();
      expect(storyProvider.isNavigationBlocked, isFalse);
      expect(storyProvider.isSceneNarrationComplete, isTrue);
    });
  });
}
