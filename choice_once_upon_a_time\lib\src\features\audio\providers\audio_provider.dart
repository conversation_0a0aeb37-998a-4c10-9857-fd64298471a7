import 'package:flutter/material.dart';
import '../services/audio_service.dart';
import '../../../core/config/app_config.dart';

/// Provider for managing audio state and integration with settings
class AudioProvider extends ChangeNotifier {
  final AudioService _audioService = AudioService();

  /// Whether the audio service is initialized
  bool _isInitialized = false;

  /// Current background music track
  String? _currentMusicTrack;

  /// Whether background music is currently playing
  bool _isMusicPlaying = false;

  /// Last played sound effect
  String? _lastSoundEffect;

  /// Getters
  bool get isInitialized => _isInitialized;
  String? get currentMusicTrack => _currentMusicTrack;
  bool get isMusicPlaying => _isMusicPlaying;
  String? get lastSoundEffect => _lastSoundEffect;
  AudioService get audioService => _audioService;

  /// Initialize the audio provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _isInitialized = await _audioService.initialize();
      if (_isInitialized) {
        AppConfig.logInfo('Audio provider initialized successfully');
        notifyListeners();
      }
    } catch (e) {
      AppConfig.logError('Failed to initialize audio provider: $e');
    }
  }

  /// Play background music
  Future<void> playBackgroundMusic(String assetPath) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final success = await _audioService.playBackgroundMusic(assetPath);
      if (success) {
        _currentMusicTrack = assetPath;
        _isMusicPlaying = true;
        notifyListeners();
      }
    } catch (e) {
      AppConfig.logError('Failed to play background music: $e');
    }
  }

  /// Stop background music
  Future<void> stopBackgroundMusic() async {
    if (!_isInitialized) return;

    try {
      await _audioService.stopBackgroundMusic();
      _currentMusicTrack = null;
      _isMusicPlaying = false;
      notifyListeners();
    } catch (e) {
      AppConfig.logError('Failed to stop background music: $e');
    }
  }

  /// Pause background music
  Future<void> pauseBackgroundMusic() async {
    if (!_isInitialized || !_isMusicPlaying) return;

    try {
      await _audioService.pauseBackgroundMusic();
      _isMusicPlaying = false;
      notifyListeners();
    } catch (e) {
      AppConfig.logError('Failed to pause background music: $e');
    }
  }

  /// Resume background music
  Future<void> resumeBackgroundMusic() async {
    if (!_isInitialized || _isMusicPlaying) return;

    try {
      await _audioService.resumeBackgroundMusic();
      _isMusicPlaying = true;
      notifyListeners();
    } catch (e) {
      AppConfig.logError('Failed to resume background music: $e');
    }
  }

  /// Play sound effect
  Future<void> playSoundEffect(String assetPath) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final success = await _audioService.playSoundEffect(assetPath);
      if (success) {
        _lastSoundEffect = assetPath;
        notifyListeners();
      }
    } catch (e) {
      AppConfig.logError('Failed to play sound effect: $e');
    }
  }

  /// Update audio settings
  Future<void> updateSettings({
    required bool musicEnabled,
    required double musicVolume,
    required bool sfxEnabled,
    required double sfxVolume,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      await _audioService.updateFromSettings(
        musicEnabled: musicEnabled,
        musicVolume: musicVolume,
        sfxEnabled: sfxEnabled,
        sfxVolume: sfxVolume,
      );
      notifyListeners();
    } catch (e) {
      AppConfig.logError('Failed to update audio settings: $e');
    }
  }

  /// Play default background music for stories
  Future<void> playStoryMusic() async {
    await playBackgroundMusic('audio/music/gentle_adventure.mp3');
  }

  /// Play menu background music
  Future<void> playMenuMusic() async {
    await playBackgroundMusic('audio/music/magical_forest.mp3');
  }

  /// Play bedtime music
  Future<void> playBedtimeMusic() async {
    await playBackgroundMusic('audio/music/bedtime_lullaby.mp3');
  }

  /// Play page turn sound effect
  Future<void> playPageTurnSound() async {
    await playSoundEffect('audio/sfx/page_turn.mp3');
  }

  /// Play choice selection sound effect
  Future<void> playChoiceSelectSound() async {
    await playSoundEffect('audio/sfx/choice_select.mp3');
  }

  /// Play story completion sound effect
  Future<void> playStoryCompleteSound() async {
    await playSoundEffect('audio/sfx/story_complete.mp3');
  }

  /// Play button click sound effect
  Future<void> playButtonClickSound() async {
    await playSoundEffect('audio/sfx/button_click.mp3');
  }

  /// Play magic sparkle sound effect
  Future<void> playMagicSparkleSound() async {
    await playSoundEffect('audio/sfx/magic_sparkle.mp3');
  }

  /// Fade out current music
  Future<void> fadeOutMusic({Duration duration = const Duration(seconds: 2)}) async {
    if (!_isInitialized) return;

    try {
      await _audioService.fadeOutMusic(duration: duration);
      _currentMusicTrack = null;
      _isMusicPlaying = false;
      notifyListeners();
    } catch (e) {
      AppConfig.logError('Failed to fade out music: $e');
    }
  }

  /// Fade in music
  Future<void> fadeInMusic(String assetPath, {Duration duration = const Duration(seconds: 2)}) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      await _audioService.fadeInMusic(assetPath, duration: duration);
      _currentMusicTrack = assetPath;
      _isMusicPlaying = true;
      notifyListeners();
    } catch (e) {
      AppConfig.logError('Failed to fade in music: $e');
    }
  }

  /// Get available music tracks
  List<String> getAvailableMusicTracks() {
    return _audioService.getAvailableMusicTracks();
  }

  /// Get available sound effects
  List<String> getAvailableSoundEffects() {
    return _audioService.getAvailableSoundEffects();
  }

  /// Get friendly name for music track
  String getMusicTrackName(String assetPath) {
    switch (assetPath) {
      case 'audio/music/gentle_adventure.mp3':
        return 'Gentle Adventure';
      case 'audio/music/magical_forest.mp3':
        return 'Magical Forest';
      case 'audio/music/ocean_dreams.mp3':
        return 'Ocean Dreams';
      case 'audio/music/bedtime_lullaby.mp3':
        return 'Bedtime Lullaby';
      default:
        return 'Unknown Track';
    }
  }

  /// Get friendly name for sound effect
  String getSoundEffectName(String assetPath) {
    switch (assetPath) {
      case 'audio/sfx/page_turn.mp3':
        return 'Page Turn';
      case 'audio/sfx/choice_select.mp3':
        return 'Choice Select';
      case 'audio/sfx/story_complete.mp3':
        return 'Story Complete';
      case 'audio/sfx/button_click.mp3':
        return 'Button Click';
      case 'audio/sfx/magic_sparkle.mp3':
        return 'Magic Sparkle';
      default:
        return 'Unknown Effect';
    }
  }

  @override
  void dispose() {
    _audioService.dispose();
    super.dispose();
  }
}
