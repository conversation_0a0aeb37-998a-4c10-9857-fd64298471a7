import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../../lib/src/features/monetization/widgets/content_gate.dart';
import '../../lib/src/features/monetization/screens/premium_screen.dart';
import '../helpers/test_helpers.dart';
import '../mocks/mock_services.dart';

void main() {
  group('ContentGate Widget Tests', () {
    late MockPurchaseProvider mockPurchaseProvider;

    setUp(() {
      mockPurchaseProvider = MockPurchaseProvider();
      TestHelpers.setupMockProviders(purchaseProvider: mockPurchaseProvider);
    });

    group('ContentGate', () {
      testWidgets('should show child when content is unlocked', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isContentUnlocked('test-content')).thenReturn(true);

        const testChild = Text('Unlocked Content');

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const ContentGate(
              contentId: 'test-content',
              child: testChild,
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text('Unlocked Content'), findsOneWidget);
        expect(find.byIcon(Icons.lock), findsNothing);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show lock screen when content is locked', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isContentUnlocked('test-content')).thenReturn(false);
        when(mockPurchaseProvider.isLoading).thenReturn(false);
        when(mockPurchaseProvider.errorMessage).thenReturn(null);

        const testChild = Text('Locked Content');

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const ContentGate(
              contentId: 'test-content',
              child: testChild,
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text('Locked Content'), findsNothing);
        expect(find.byIcon(Icons.lock), findsOneWidget);
        expect(find.text('PREMIUM CONTENT'), findsOneWidget);
        expect(find.text('Unlock this content with Premium'), findsOneWidget);
        expect(find.text('Unlock Premium'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show custom unlock message', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isContentUnlocked('test-content')).thenReturn(false);
        when(mockPurchaseProvider.isLoading).thenReturn(false);

        const customMessage = 'Custom unlock message';

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const ContentGate(
              contentId: 'test-content',
              unlockMessage: customMessage,
              child: Text('Content'),
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text(customMessage), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show custom unlock button text', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isContentUnlocked('test-content')).thenReturn(false);
        when(mockPurchaseProvider.isLoading).thenReturn(false);

        const customButtonText = 'Custom Button';

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const ContentGate(
              contentId: 'test-content',
              unlockButtonText: customButtonText,
              child: Text('Content'),
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text(customButtonText), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should navigate to premium screen when unlock button tapped', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isContentUnlocked('test-content')).thenReturn(false);
        when(mockPurchaseProvider.isLoading).thenReturn(false);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const ContentGate(
              contentId: 'test-content',
              child: Text('Content'),
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Act
        await tester.tap(find.text('Unlock Premium'));
        await tester.pumpAndSettle();

        // Assert
        // In a real test, we would verify navigation to PremiumScreen
        // For now, we verify the button is tappable
        expect(find.text('Unlock Premium'), findsOneWidget);
      });

      testWidgets('should show loading state', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isContentUnlocked('test-content')).thenReturn(false);
        when(mockPurchaseProvider.isLoading).thenReturn(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const ContentGate(
              contentId: 'test-content',
              child: Text('Content'),
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show error message', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isContentUnlocked('test-content')).thenReturn(false);
        when(mockPurchaseProvider.isLoading).thenReturn(false);
        when(mockPurchaseProvider.errorMessage).thenReturn('Purchase failed');

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const ContentGate(
              contentId: 'test-content',
              child: Text('Content'),
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text('Purchase failed'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should handle restore purchases button', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isContentUnlocked('test-content')).thenReturn(false);
        when(mockPurchaseProvider.isLoading).thenReturn(false);
        when(mockPurchaseProvider.restorePurchases()).thenAnswer((_) async => true);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const ContentGate(
              contentId: 'test-content',
              child: Text('Content'),
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Act
        await tester.tap(find.text('Restore Purchases'));
        await tester.pump();

        // Assert
        verify(mockPurchaseProvider.restorePurchases()).called(1);
      });
    });

    group('PremiumGate', () {
      testWidgets('should show child when premium is unlocked', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isPremiumUnlocked).thenReturn(true);

        const testChild = Text('Premium Content');

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const PremiumGate(
              child: testChild,
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text('Premium Content'), findsOneWidget);
        expect(find.byIcon(Icons.lock), findsNothing);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show lock screen when premium is locked', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isPremiumUnlocked).thenReturn(false);
        when(mockPurchaseProvider.isLoading).thenReturn(false);

        const testChild = Text('Premium Content');

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const PremiumGate(
              child: testChild,
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text('Premium Content'), findsNothing);
        expect(find.byIcon(Icons.lock), findsOneWidget);
        expect(find.text('PREMIUM CONTENT'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show custom unlock message', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isPremiumUnlocked).thenReturn(false);
        when(mockPurchaseProvider.isLoading).thenReturn(false);

        const customMessage = 'Upgrade to Premium';

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const PremiumGate(
              unlockMessage: customMessage,
              child: Text('Content'),
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text(customMessage), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });
    });

    group('StoryGate', () {
      testWidgets('should show child when story is unlocked', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isStoryUnlocked('test-story')).thenReturn(true);

        const testChild = Text('Story Content');

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const StoryGate(
              storyId: 'test-story',
              child: testChild,
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text('Story Content'), findsOneWidget);
        expect(find.byIcon(Icons.lock), findsNothing);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show lock screen when story is locked', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isStoryUnlocked('test-story')).thenReturn(false);
        when(mockPurchaseProvider.isLoading).thenReturn(false);

        const testChild = Text('Story Content');

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const StoryGate(
              storyId: 'test-story',
              child: testChild,
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text('Story Content'), findsNothing);
        expect(find.byIcon(Icons.lock), findsOneWidget);
        expect(find.text('Unlock this story with Premium'), findsOneWidget);
        expect(find.text('Get Premium Access'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });
    });

    group('Responsive Design', () {
      testWidgets('should handle different screen sizes', (tester) async {
        await TestHelpers.testOnAllScreenSizes(
          tester,
          (screenSize) {
            when(mockPurchaseProvider.isContentUnlocked('test-content')).thenReturn(false);
            when(mockPurchaseProvider.isLoading).thenReturn(false);
            
            return TestHelpers.createResponsiveTestWidget(
              child: const ContentGate(
                contentId: 'test-content',
                child: Text('Content'),
              ),
              screenSize: screenSize,
              purchaseProvider: mockPurchaseProvider,
            );
          },
          (tester, screenSize) async {
            await tester.pump();
            
            // Should show lock screen without overflow
            expect(find.byIcon(Icons.lock), findsOneWidget);
            expect(find.text('PREMIUM CONTENT'), findsOneWidget);
          },
        );
      });

      testWidgets('should adapt layout for landscape vs portrait', (tester) async {
        // Test portrait
        when(mockPurchaseProvider.isContentUnlocked('test-content')).thenReturn(false);
        when(mockPurchaseProvider.isLoading).thenReturn(false);

        await tester.pumpWidget(
          TestHelpers.createResponsiveTestWidget(
            child: const ContentGate(
              contentId: 'test-content',
              child: Text('Content'),
            ),
            screenSize: TestHelpers.phonePortrait,
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        expect(find.byIcon(Icons.lock), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);

        // Test landscape
        await tester.pumpWidget(
          TestHelpers.createResponsiveTestWidget(
            child: const ContentGate(
              contentId: 'test-content',
              child: Text('Content'),
            ),
            screenSize: TestHelpers.phoneLandscape,
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        expect(find.byIcon(Icons.lock), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });
    });

    group('Animation', () {
      testWidgets('should animate lock icon', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isContentUnlocked('test-content')).thenReturn(false);
        when(mockPurchaseProvider.isLoading).thenReturn(false);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const ContentGate(
              contentId: 'test-content',
              child: Text('Content'),
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Let animation complete
        await tester.pumpAndSettle();

        // Assert
        expect(find.byIcon(Icons.lock), findsOneWidget);
        
        // Verify animation completed without errors
        TestHelpers.verifyNoOverflow(tester);
      });
    });

    group('Benefits List', () {
      testWidgets('should show premium benefits', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isContentUnlocked('test-content')).thenReturn(false);
        when(mockPurchaseProvider.isLoading).thenReturn(false);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const ContentGate(
              contentId: 'test-content',
              child: Text('Content'),
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text('Premium Benefits'), findsOneWidget);
        expect(find.text('Access to all premium stories'), findsOneWidget);
        expect(find.text('New content added monthly'), findsOneWidget);
        expect(find.text('Ad-free experience'), findsOneWidget);
        expect(find.text('Offline downloads'), findsOneWidget);
        expect(find.text('Advanced parental controls'), findsOneWidget);
        expect(find.byIcon(Icons.check_circle), findsWidgets);
        TestHelpers.verifyNoOverflow(tester);
      });
    });

    group('Accessibility', () {
      testWidgets('should meet accessibility guidelines', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isContentUnlocked('test-content')).thenReturn(false);
        when(mockPurchaseProvider.isLoading).thenReturn(false);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const ContentGate(
              contentId: 'test-content',
              child: Text('Content'),
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        await TestHelpers.verifyAccessibility(tester);
      });
    });
  });
}
