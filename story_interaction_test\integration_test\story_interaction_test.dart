import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:story_interaction_test/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Story Interaction Integration Tests', () {
    testWidgets('Complete story flow - linear navigation', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Verify we're on the story selection screen
      expect(find.text('Choose a Story to Test'), findsOneWidget);
      expect(find.text('Coral\'s Lost Colors'), findsOneWidget);

      // Select the coral story
      await tester.tap(find.text('Coral\'s Lost Colors'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Verify we're now on the story interaction screen
      expect(find.byType(app.StoryInteractionTestApp), findsOneWidget);
      
      // Wait for story to load
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Verify the first scene is displayed
      // Should show the control panel at the bottom
      expect(find.byIcon(Icons.play_circle_filled_rounded), findsOneWidget);
      expect(find.byIcon(Icons.skip_next_rounded), findsOneWidget);
      expect(find.byIcon(Icons.skip_previous_rounded), findsOneWidget);

      // Verify subtitle text is displayed
      expect(find.textContaining('Finn the little dolphin'), findsOneWidget);

      // Test navigation through segments
      // Tap next button to advance through segments
      final nextButton = find.byIcon(Icons.skip_next_rounded);
      
      // Advance through the first scene's segments
      for (int i = 0; i < 3; i++) {
        await tester.tap(nextButton);
        await tester.pumpAndSettle(const Duration(milliseconds: 500));
      }

      // Should now be at the choice point
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Verify choice popup appears
      expect(find.text('What should Finn do?'), findsOneWidget);
      expect(find.text('Help Shelly gather kelp'), findsOneWidget);
      expect(find.text('Go play tag with waves'), findsOneWidget);

      // Make a choice
      await tester.tap(find.text('Help Shelly gather kelp'));
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Verify we moved to the next scene
      expect(find.textContaining('I can help, Shelly!'), findsOneWidget);

      // Continue through the story until we reach an ending
      // This is a simplified test - in a real scenario, we'd test the full flow
      for (int i = 0; i < 10; i++) {
        // Try to advance
        if (tester.any(nextButton)) {
          await tester.tap(nextButton);
          await tester.pumpAndSettle(const Duration(milliseconds: 500));
        }
        
        // Handle choice points
        if (tester.any(find.text('Ask Squiggle for help'))) {
          await tester.tap(find.text('Ask Squiggle for help'));
          await tester.pumpAndSettle(const Duration(seconds: 1));
        }
        
        // Check if we've reached the end
        if (tester.any(find.text('The End!'))) {
          break;
        }
      }

      // Verify story completion
      expect(find.text('The End!'), findsOneWidget);
      expect(find.text('Try That Choice Again?'), findsOneWidget);
      expect(find.text('Read This Story Again?'), findsOneWidget);
      expect(find.text('Pick a New Story?'), findsOneWidget);
    });

    testWidgets('Settings panel functionality', (WidgetTester tester) async {
      // Start the app and navigate to story
      app.main();
      await tester.pumpAndSettle();

      await tester.tap(find.text('Coral\'s Lost Colors'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Open settings panel
      await tester.tap(find.byIcon(Icons.settings));
      await tester.pumpAndSettle();

      // Verify settings panel is displayed
      expect(find.text('Settings'), findsOneWidget);
      expect(find.text('Panel Transparency'), findsOneWidget);
      expect(find.text('Text Size'), findsOneWidget);
      expect(find.text('Speech Speed'), findsOneWidget);
      expect(find.text('Volume'), findsOneWidget);

      // Test adjusting font size
      final fontSizeSlider = find.byType(Slider).first;
      await tester.drag(fontSizeSlider, const Offset(50, 0));
      await tester.pumpAndSettle();

      // Test adjusting panel opacity
      final opacitySlider = find.byType(Slider).at(0);
      await tester.drag(opacitySlider, const Offset(-30, 0));
      await tester.pumpAndSettle();

      // Close settings panel
      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      // Verify settings panel is closed
      expect(find.text('Settings'), findsNothing);
    });

    testWidgets('Choice replay functionality', (WidgetTester tester) async {
      // Start the app and navigate to a choice point
      app.main();
      await tester.pumpAndSettle();

      await tester.tap(find.text('Coral\'s Lost Colors'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Navigate to the first choice point
      final nextButton = find.byIcon(Icons.skip_next_rounded);
      
      // Advance through segments until we reach a choice
      for (int i = 0; i < 5; i++) {
        if (tester.any(nextButton)) {
          await tester.tap(nextButton);
          await tester.pumpAndSettle(const Duration(milliseconds: 500));
        }
        
        if (tester.any(find.text('What should Finn do?'))) {
          break;
        }
      }

      // Verify we're at a choice point
      expect(find.text('What should Finn do?'), findsOneWidget);

      // Test replay choices button
      await tester.tap(find.text('Replay Choices'));
      await tester.pumpAndSettle(const Duration(seconds: 1));

      // The choices should still be visible
      expect(find.text('Help Shelly gather kelp'), findsOneWidget);
      expect(find.text('Go play tag with waves'), findsOneWidget);
    });

    testWidgets('Story restart functionality', (WidgetTester tester) async {
      // Start the app and complete a story
      app.main();
      await tester.pumpAndSettle();

      await tester.tap(find.text('Coral\'s Lost Colors'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Quickly navigate to an ending (simplified)
      final nextButton = find.byIcon(Icons.skip_next_rounded);
      
      // Fast-forward through the story
      for (int i = 0; i < 20; i++) {
        if (tester.any(nextButton)) {
          await tester.tap(nextButton);
          await tester.pumpAndSettle(const Duration(milliseconds: 200));
        }
        
        // Handle any choices by selecting the first option
        if (tester.any(find.textContaining('Help'))) {
          await tester.tap(find.textContaining('Help').first);
          await tester.pumpAndSettle(const Duration(milliseconds: 500));
        }
        
        if (tester.any(find.text('The End!'))) {
          break;
        }
      }

      // If we reached the end, test restart
      if (tester.any(find.text('The End!'))) {
        await tester.tap(find.text('Read This Story Again?'));
        await tester.pumpAndSettle(const Duration(seconds: 2));

        // Verify we're back at the beginning
        expect(find.textContaining('Finn the little dolphin'), findsOneWidget);
      }
    });

    testWidgets('Responsive design on different screen sizes', (WidgetTester tester) async {
      // Test on a tablet-like screen size
      await tester.binding.setSurfaceSize(const Size(1024, 768));
      
      app.main();
      await tester.pumpAndSettle();

      await tester.tap(find.text('Coral\'s Lost Colors'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Verify layout adapts to larger screen
      expect(find.byIcon(Icons.settings), findsOneWidget);
      expect(find.byIcon(Icons.play_circle_filled_rounded), findsOneWidget);

      // Test on a phone-like screen size
      await tester.binding.setSurfaceSize(const Size(375, 667));
      await tester.pumpAndSettle();

      // Verify layout still works on smaller screen
      expect(find.byIcon(Icons.settings), findsOneWidget);
      expect(find.byIcon(Icons.play_circle_filled_rounded), findsOneWidget);

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });
  });
}
