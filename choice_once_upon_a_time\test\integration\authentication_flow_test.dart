import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:provider/provider.dart';

import '../../lib/main.dart' as app;
import '../../lib/src/features/auth/providers/parent_auth_provider.dart';
import '../../lib/src/features/monetization/providers/purchase_provider.dart';
import '../../lib/src/features/profile/providers/active_child_provider.dart';
import '../../lib/src/features/story/providers/story_provider.dart';
import '../../lib/src/features/story/providers/story_settings_provider.dart';
import '../helpers/test_helpers.dart';
import '../mocks/mock_services.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Authentication Flow Integration Tests', () {
    late MockFirebaseAuthService mockAuthService;
    late MockFirestore mockFirestore;

    setUpAll(() {
      // Initialize mock services
      mockAuthService = MockFirebaseAuthService();
      MockFirestore.clear();
    });

    setUp(() {
      // Reset mock state before each test
      mockAuthService.setAuthState(isSignedIn: false);
      MockFirestore.clear();
    });

    testWidgets('Complete sign-up flow', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Complete sign-up flow',
        () async {
          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Should start at auth screen
          expect(find.text('Sign In'), findsOneWidget);
          expect(find.text('Sign Up'), findsOneWidget);

          // Navigate to sign up
          await tester.tap(find.text('Sign Up'));
          await tester.pumpAndSettle();

          // Fill in sign up form
          await tester.enterText(
            find.byKey(const Key('email_field')),
            '<EMAIL>',
          );
          await tester.enterText(
            find.byKey(const Key('password_field')),
            'password123',
          );
          await tester.enterText(
            find.byKey(const Key('name_field')),
            'Test Parent',
          );

          // Submit sign up
          await tester.tap(find.text('Create Account'));
          await tester.pumpAndSettle();

          // Should navigate to no children screen
          expect(find.text('Create Your First Child Profile'), findsOneWidget);

          // Create child profile
          await tester.tap(find.text('Create Child Profile'));
          await tester.pumpAndSettle();

          await tester.enterText(
            find.byKey(const Key('child_name_field')),
            'Test Child',
          );
          await tester.tap(find.text('Create'));
          await tester.pumpAndSettle();

          // Should navigate to main menu
          expect(find.text('New Story'), findsOneWidget);
          expect(find.text('Continue Story'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Complete sign-in flow', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Complete sign-in flow',
        () async {
          // Set up existing user data
          mockAuthService.setAuthState(isSignedIn: false);
          MockFirestore.setDocument(
            'users/test-user-id',
            TestDataFactory.createUserData(
              email: '<EMAIL>',
              children: [
                TestDataFactory.createChildData(name: 'Existing Child'),
              ],
            ),
          );

          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Should start at auth screen
          expect(find.text('Sign In'), findsOneWidget);

          // Fill in sign in form
          await tester.enterText(
            find.byKey(const Key('email_field')),
            '<EMAIL>',
          );
          await tester.enterText(
            find.byKey(const Key('password_field')),
            'password123',
          );

          // Submit sign in
          await tester.tap(find.text('Sign In'));
          await tester.pumpAndSettle();

          // Should show child selection
          expect(find.text('Select Child'), findsOneWidget);
          expect(find.text('Existing Child'), findsOneWidget);

          // Select child
          await tester.tap(find.text('Existing Child'));
          await tester.pumpAndSettle();

          // Should navigate to main menu
          expect(find.text('New Story'), findsOneWidget);
          expect(find.text('Continue Story'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Sign-in error handling', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Sign-in error handling',
        () async {
          // Set up auth service to return error
          mockAuthService.setAuthState(
            isSignedIn: false,
            errorMessage: 'Invalid credentials',
          );

          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Fill in sign in form with wrong credentials
          await tester.enterText(
            find.byKey(const Key('email_field')),
            '<EMAIL>',
          );
          await tester.enterText(
            find.byKey(const Key('password_field')),
            'wrongpassword',
          );

          // Submit sign in
          await tester.tap(find.text('Sign In'));
          await tester.pumpAndSettle();

          // Should show error message
          expect(find.textContaining('Invalid'), findsOneWidget);

          // Should still be on auth screen
          expect(find.text('Sign In'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Sign-out flow', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Sign-out flow',
        () async {
          // Set up authenticated state
          mockAuthService.setAuthState(isSignedIn: true, userEmail: '<EMAIL>');
          MockFirestore.setDocument(
            'users/test-user-id',
            TestDataFactory.createUserData(
              children: [TestDataFactory.createChildData()],
            ),
          );

          // Launch app (should start authenticated)
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Should be at main menu (assuming child is auto-selected)
          expect(find.text('New Story'), findsOneWidget);

          // Navigate to settings
          await tester.tap(find.byIcon(Icons.settings));
          await tester.pumpAndSettle();

          // Find and tap sign out
          await tester.tap(find.text('Sign Out'));
          await tester.pumpAndSettle();

          // Confirm sign out
          await tester.tap(find.text('Sign Out')); // In confirmation dialog
          await tester.pumpAndSettle();

          // Should return to auth screen
          expect(find.text('Sign In'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Multiple child profiles flow', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Multiple child profiles flow',
        () async {
          // Set up user with multiple children
          mockAuthService.setAuthState(isSignedIn: true);
          MockFirestore.setDocument(
            'users/test-user-id',
            TestDataFactory.createUserData(
              children: [
                TestDataFactory.createChildData(id: 'child1', name: 'Child One'),
                TestDataFactory.createChildData(id: 'child2', name: 'Child Two'),
                TestDataFactory.createChildData(id: 'child3', name: 'Child Three'),
              ],
            ),
          );

          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Should show child selection
          expect(find.text('Select Child'), findsOneWidget);
          expect(find.text('Child One'), findsOneWidget);
          expect(find.text('Child Two'), findsOneWidget);
          expect(find.text('Child Three'), findsOneWidget);

          // Select second child
          await tester.tap(find.text('Child Two'));
          await tester.pumpAndSettle();

          // Should navigate to main menu
          expect(find.text('New Story'), findsOneWidget);

          // Navigate to settings to switch child
          await tester.tap(find.byIcon(Icons.settings));
          await tester.pumpAndSettle();

          await tester.tap(find.text('Switch Adventurer'));
          await tester.pumpAndSettle();

          // Should show child selection again
          expect(find.text('Select Child'), findsOneWidget);

          // Select different child
          await tester.tap(find.text('Child Three'));
          await tester.pumpAndSettle();

          // Should return to main menu with new child
          expect(find.text('New Story'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Cloud sync after authentication', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Cloud sync after authentication',
        () async {
          // Set up user with existing cloud data
          const childId = 'test-child-1';
          mockAuthService.setAuthState(isSignedIn: false);
          
          MockFirestore.setDocument(
            'users/test-user-id',
            TestDataFactory.createUserData(
              children: [TestDataFactory.createChildData(id: childId)],
            ),
          );

          MockFirestore.setDocument(
            'children/$childId',
            TestDataFactory.createChildData(
              id: childId,
              storyProgress: {
                'story1': TestDataFactory.createStoryProgressData(storyId: 'story1'),
              },
            ),
          );

          // Launch app and sign in
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.enterText(
            find.byKey(const Key('email_field')),
            '<EMAIL>',
          );
          await tester.enterText(
            find.byKey(const Key('password_field')),
            'password123',
          );

          await tester.tap(find.text('Sign In'));
          await tester.pumpAndSettle();

          // Select child
          await tester.tap(find.textContaining('test-child'));
          await tester.pumpAndSettle();

          // Should be at main menu with sync indicator
          expect(find.text('New Story'), findsOneWidget);
          
          // Look for sync status indicator
          expect(find.byIcon(Icons.cloud_done), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Responsive design across screen sizes', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Responsive design across screen sizes',
        () async {
          for (final screenSize in TestHelpers.allScreenSizes) {
            // Set screen size
            await tester.binding.setSurfaceSize(screenSize);
            await tester.pumpAndSettle();

            // Launch app
            await tester.pumpWidget(app.MyApp());
            await tester.pumpAndSettle();

            // Should show auth screen without overflow
            expect(find.text('Sign In'), findsOneWidget);
            TestHelpers.verifyNoOverflow(tester);

            // Test sign in form
            await tester.enterText(
              find.byKey(const Key('email_field')),
              '<EMAIL>',
            );
            await tester.enterText(
              find.byKey(const Key('password_field')),
              'password123',
            );

            // Form should be usable without overflow
            TestHelpers.verifyNoOverflow(tester);
          }

          // Reset to default size
          await tester.binding.setSurfaceSize(null);
        },
      );
    });

    testWidgets('Network error handling during authentication', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Network error handling during authentication',
        () async {
          // Set up network error
          mockAuthService.setAuthState(
            isSignedIn: false,
            errorMessage: 'Network error. Please check your connection.',
          );

          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Try to sign in
          await tester.enterText(
            find.byKey(const Key('email_field')),
            '<EMAIL>',
          );
          await tester.enterText(
            find.byKey(const Key('password_field')),
            'password123',
          );

          await tester.tap(find.text('Sign In'));
          await tester.pumpAndSettle();

          // Should show network error message
          expect(find.textContaining('Network error'), findsOneWidget);

          // Should provide retry option
          expect(find.text('Try Again'), findsOneWidget);

          // Test retry functionality
          mockAuthService.setAuthState(isSignedIn: false, errorMessage: null);
          await tester.tap(find.text('Try Again'));
          await tester.pumpAndSettle();

          // Error should be cleared
          expect(find.textContaining('Network error'), findsNothing);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Data migration flow', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Data migration flow',
        () async {
          // Set up local data that needs migration
          // This would involve setting up SharedPreferences with legacy data
          
          // Launch app and sign in
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Complete authentication
          await tester.enterText(
            find.byKey(const Key('email_field')),
            '<EMAIL>',
          );
          await tester.enterText(
            find.byKey(const Key('password_field')),
            'password123',
          );

          await tester.tap(find.text('Sign In'));
          await tester.pumpAndSettle();

          // Migration should happen automatically in background
          // User should see normal flow
          expect(find.text('Select Child'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Accessibility compliance throughout flow', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Accessibility compliance throughout flow',
        () async {
          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Test auth screen accessibility
          await TestHelpers.verifyAccessibility(tester);

          // Sign in
          await tester.enterText(
            find.byKey(const Key('email_field')),
            '<EMAIL>',
          );
          await tester.enterText(
            find.byKey(const Key('password_field')),
            'password123',
          );

          await tester.tap(find.text('Sign In'));
          await tester.pumpAndSettle();

          // Test child selection accessibility
          await TestHelpers.verifyAccessibility(tester);

          // Continue through flow and test each screen
          // This would be expanded to test all major screens
        },
      );
    });
  });
}
