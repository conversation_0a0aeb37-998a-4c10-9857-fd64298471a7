import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'story_provider.dart';

class SceneBackgroundWidget extends StatefulWidget {
  @override
  _SceneBackgroundWidgetState createState() => _SceneBackgroundWidgetState();
}

class _SceneBackgroundWidgetState extends State<SceneBackgroundWidget> {
  Widget? _cachedImage;

  @override
  Widget build(BuildContext context) {
    return Consumer<StoryProvider>(
      builder: (context, provider, child) {
        final imagePath = provider.currentSceneImage;
        final shouldUpdateImage = provider.shouldUpdateImage;

        print('SceneBackgroundWidget build: Scene: ${provider.currentScene?.id}, Image: $imagePath, UpdateImage: $shouldUpdateImage');

        if (imagePath == null || imagePath.isEmpty) {
          print('No valid image for scene: ${provider.currentScene?.id}');
          _cachedImage = Container(color: Colors.grey.shade300);
          return _cachedImage!;
        }

        if (!shouldUpdateImage && _cachedImage != null) {
          print('Using cached image for scene: ${provider.currentScene?.id}, Image: $imagePath');
          return _cachedImage!;
        }

        print('Loading new image for scene: ${provider.currentScene?.id}, Image: $imagePath');
        _cachedImage = Image.asset(
          imagePath,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            print('Failed to load image: $imagePath, Error: $error');
            return Container(color: Colors.grey.shade300);
          },
        );
        return _cachedImage!;
      },
    );
  }
}