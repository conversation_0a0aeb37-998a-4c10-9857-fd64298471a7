import 'package:cloud_firestore/cloud_firestore.dart';

/// Model representing a parent profile in the system
class ParentProfile {
  /// Unique identifier for the parent
  final String id;

  /// <PERSON><PERSON>'s email address
  final String email;

  /// Pa<PERSON>'s display name
  final String displayName;

  /// When the profile was created
  final DateTime createdAt;

  /// When the profile was last updated
  final DateTime updatedAt;

  /// List of child profile IDs associated with this parent
  final List<String> childProfileIds;

  /// Whether the parent has premium access
  final bool isPremium;

  /// Premium subscription expiry date (null if not premium)
  final DateTime? premiumExpiryDate;

  /// Parent's preferences and settings
  final Map<String, dynamic> preferences;

  /// Constructor
  const ParentProfile({
    required this.id,
    required this.email,
    required this.displayName,
    required this.createdAt,
    required this.updatedAt,
    required this.childProfileIds,
    this.isPremium = false,
    this.premiumExpiryDate,
    this.preferences = const {},
  });

  /// Create a ParentProfile from Firestore document
  factory ParentProfile.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return ParentProfile(
      id: doc.id,
      email: data['email'] ?? '',
      displayName: data['displayName'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      childProfileIds: List<String>.from(data['childProfileIds'] ?? []),
      isPremium: data['isPremium'] ?? false,
      premiumExpiryDate: (data['premiumExpiryDate'] as Timestamp?)?.toDate(),
      preferences: Map<String, dynamic>.from(data['preferences'] ?? {}),
    );
  }

  /// Create a ParentProfile from a map
  factory ParentProfile.fromMap(Map<String, dynamic> map) {
    return ParentProfile(
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      displayName: map['displayName'] ?? '',
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate()
          : DateTime.parse(map['updatedAt'] ?? DateTime.now().toIso8601String()),
      childProfileIds: List<String>.from(map['childProfileIds'] ?? []),
      isPremium: map['isPremium'] ?? false,
      premiumExpiryDate: map['premiumExpiryDate'] is Timestamp 
          ? (map['premiumExpiryDate'] as Timestamp).toDate()
          : map['premiumExpiryDate'] != null 
              ? DateTime.parse(map['premiumExpiryDate'])
              : null,
      preferences: Map<String, dynamic>.from(map['preferences'] ?? {}),
    );
  }

  /// Convert to Firestore document data
  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'displayName': displayName,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'childProfileIds': childProfileIds,
      'isPremium': isPremium,
      'premiumExpiryDate': premiumExpiryDate != null 
          ? Timestamp.fromDate(premiumExpiryDate!)
          : null,
      'preferences': preferences,
    };
  }

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'childProfileIds': childProfileIds,
      'isPremium': isPremium,
      'premiumExpiryDate': premiumExpiryDate?.toIso8601String(),
      'preferences': preferences,
    };
  }

  /// Create a copy with updated fields
  ParentProfile copyWith({
    String? id,
    String? email,
    String? displayName,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? childProfileIds,
    bool? isPremium,
    DateTime? premiumExpiryDate,
    Map<String, dynamic>? preferences,
  }) {
    return ParentProfile(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      childProfileIds: childProfileIds ?? this.childProfileIds,
      isPremium: isPremium ?? this.isPremium,
      premiumExpiryDate: premiumExpiryDate ?? this.premiumExpiryDate,
      preferences: preferences ?? this.preferences,
    );
  }

  /// Check if premium subscription is active
  bool get isPremiumActive {
    if (!isPremium) return false;
    if (premiumExpiryDate == null) return true; // Lifetime premium
    return premiumExpiryDate!.isAfter(DateTime.now());
  }

  /// Get preference value with default
  T getPreference<T>(String key, T defaultValue) {
    return preferences[key] as T? ?? defaultValue;
  }

  /// Set preference value
  ParentProfile setPreference(String key, dynamic value) {
    final newPreferences = Map<String, dynamic>.from(preferences);
    newPreferences[key] = value;
    return copyWith(
      preferences: newPreferences,
      updatedAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ParentProfile &&
        other.id == id &&
        other.email == email &&
        other.displayName == displayName;
  }

  @override
  int get hashCode {
    return id.hashCode ^ email.hashCode ^ displayName.hashCode;
  }

  @override
  String toString() {
    return 'ParentProfile(id: $id, email: $email, displayName: $displayName, '
           'isPremium: $isPremium, childrenCount: ${childProfileIds.length})';
  }
}
