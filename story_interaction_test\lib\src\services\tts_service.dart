import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_tts/flutter_tts.dart';

/// Service for managing text-to-speech functionality
class TTSService {
  static final TTSService _instance = TTSService._internal();
  factory TTSService() => _instance;
  TTSService._internal();

  final FlutterTts _flutterTts = FlutterTts();
  
  bool _isInitialized = false;
  bool _isSpeaking = false;
  bool _isPaused = false;
  
  List<String> _words = [];
  int _currentWordIndex = 0;
  String _currentText = '';
  
  // Stream controllers for events
  final StreamController<int> _wordProgressController = StreamController<int>.broadcast();
  final StreamController<bool> _speechStateController = StreamController<bool>.broadcast();
  final StreamController<void> _speechCompleteController = StreamController<void>.broadcast();
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isSpeaking => _isSpeaking;
  bool get isPaused => _isPaused;
  List<String> get words => List.unmodifiable(_words);
  int get currentWordIndex => _currentWordIndex;
  String get currentWord => _currentWordIndex < _words.length ? _words[_currentWordIndex] : '';
  
  // Streams
  Stream<int> get wordProgressStream => _wordProgressController.stream;
  Stream<bool> get speechStateStream => _speechStateController.stream;
  Stream<void> get speechCompleteStream => _speechCompleteController.stream;
  
  /// Initialize the TTS service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Set up TTS handlers
      _flutterTts.setStartHandler(() {
        _isSpeaking = true;
        _isPaused = false;
        _speechStateController.add(true);
        debugPrint('TTS: Started speaking');
      });
      
      _flutterTts.setCompletionHandler(() {
        _isSpeaking = false;
        _isPaused = false;
        _speechStateController.add(false);
        _speechCompleteController.add(null);
        debugPrint('TTS: Completed speaking');
      });
      
      _flutterTts.setPauseHandler(() {
        _isPaused = true;
        _speechStateController.add(false);
        debugPrint('TTS: Paused');
      });
      
      _flutterTts.setContinueHandler(() {
        _isPaused = false;
        _speechStateController.add(true);
        debugPrint('TTS: Resumed');
      });
      
      _flutterTts.setErrorHandler((msg) {
        debugPrint('TTS Error: $msg');
        _isSpeaking = false;
        _isPaused = false;
        _speechStateController.add(false);
      });
      
      // Set default settings
      await _flutterTts.setLanguage('en-US');
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setVolume(1.0);
      await _flutterTts.setPitch(1.0);
      
      _isInitialized = true;
      debugPrint('TTS: Initialized successfully');
    } catch (e) {
      debugPrint('TTS: Failed to initialize: $e');
    }
  }
  
  /// Speak the given text
  Future<void> speak(String text) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    if (text.isEmpty) return;
    
    try {
      // Stop any current speech
      if (_isSpeaking) {
        await stop();
      }
      
      _currentText = text.trim();
      _words = _currentText.split(RegExp(r'\s+')).where((word) => word.isNotEmpty).toList();
      _currentWordIndex = 0;
      
      debugPrint('TTS: Speaking "${_currentText}" (${_words.length} words)');
      
      // Start speaking
      await _flutterTts.speak(_currentText);
      
      // Simulate word progress for testing (since flutter_tts doesn't provide word callbacks)
      _simulateWordProgress();
      
    } catch (e) {
      debugPrint('TTS: Error speaking: $e');
    }
  }
  
  /// Pause speaking
  Future<void> pause() async {
    if (_isSpeaking && !_isPaused) {
      await _flutterTts.pause();
    }
  }
  
  /// Resume speaking
  Future<void> resume() async {
    if (_isPaused) {
      // Flutter TTS doesn't have a resume method, so we restart from current position
      if (_words.isNotEmpty && _currentWordIndex < _words.length) {
        final remainingText = _words.sublist(_currentWordIndex).join(' ');
        await _flutterTts.speak(remainingText);
      }
    }
  }
  
  /// Stop speaking
  Future<void> stop() async {
    await _flutterTts.stop();
    _isSpeaking = false;
    _isPaused = false;
    _speechStateController.add(false);
  }
  
  /// Set speech rate (0.0 to 1.0)
  Future<void> setSpeechRate(double rate) async {
    await _flutterTts.setSpeechRate(rate);
  }
  
  /// Set volume (0.0 to 1.0)
  Future<void> setVolume(double volume) async {
    await _flutterTts.setVolume(volume);
  }
  
  /// Simulate word progress for testing purposes
  void _simulateWordProgress() {
    if (_words.isEmpty) return;
    
    // Calculate approximate duration per word (assuming 150 words per minute)
    const wordsPerMinute = 150;
    final millisecondsPerWord = (60 * 1000) ~/ wordsPerMinute;
    
    Timer.periodic(Duration(milliseconds: millisecondsPerWord), (timer) {
      if (!_isSpeaking || _isPaused || _currentWordIndex >= _words.length) {
        timer.cancel();
        return;
      }
      
      _wordProgressController.add(_currentWordIndex);
      _currentWordIndex++;
    });
  }
  
  /// Dispose of resources
  void dispose() {
    _wordProgressController.close();
    _speechStateController.close();
    _speechCompleteController.close();
  }
}
