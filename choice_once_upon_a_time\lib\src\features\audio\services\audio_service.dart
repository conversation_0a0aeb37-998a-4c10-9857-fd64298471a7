import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:audioplayers/audioplayers.dart';
import '../../../core/config/app_config.dart';

/// Service for managing background music and sound effects
class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  /// Audio player for background music
  final AudioPlayer _musicPlayer = AudioPlayer();

  /// Audio player for sound effects
  final AudioPlayer _sfxPlayer = AudioPlayer();

  /// Whether the service is initialized
  bool _isInitialized = false;

  /// Current background music volume
  double _musicVolume = 0.5;

  /// Current sound effects volume
  double _sfxVolume = 0.7;

  /// Whether background music is enabled
  bool _musicEnabled = true;

  /// Whether sound effects are enabled
  bool _sfxEnabled = true;

  /// Currently playing background music
  String? _currentMusicTrack;

  /// Whether background music is currently playing
  bool _isMusicPlaying = false;

  /// Getters
  bool get isInitialized => _isInitialized;
  double get musicVolume => _musicVolume;
  double get sfxVolume => _sfxVolume;
  bool get musicEnabled => _musicEnabled;
  bool get sfxEnabled => _sfxEnabled;
  String? get currentMusicTrack => _currentMusicTrack;
  bool get isMusicPlaying => _isMusicPlaying;

  /// Initialize the audio service
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Set up audio players
      await _musicPlayer.setReleaseMode(ReleaseMode.loop);
      await _sfxPlayer.setReleaseMode(ReleaseMode.stop);

      // Set initial volumes
      await _musicPlayer.setVolume(_musicVolume);
      await _sfxPlayer.setVolume(_sfxVolume);

      // Set up event listeners
      _musicPlayer.onPlayerStateChanged.listen((state) {
        _isMusicPlaying = state == PlayerState.playing;
        AppConfig.logInfo('Music player state changed: $state');
      });

      _sfxPlayer.onPlayerStateChanged.listen((state) {
        AppConfig.logInfo('SFX player state changed: $state');
      });

      _isInitialized = true;
      AppConfig.logInfo('Audio service initialized successfully');
      return true;
    } catch (e) {
      AppConfig.logError('Failed to initialize audio service: $e');
      return false;
    }
  }

  /// Play background music
  Future<bool> playBackgroundMusic(String assetPath) async {
    if (!_isInitialized || !_musicEnabled) return false;

    try {
      // Stop current music if playing
      if (_isMusicPlaying) {
        await _musicPlayer.stop();
      }

      // Play new music
      await _musicPlayer.play(AssetSource(assetPath));
      _currentMusicTrack = assetPath;
      
      AppConfig.logInfo('Started playing background music: $assetPath');
      return true;
    } catch (e) {
      AppConfig.logError('Failed to play background music: $e');
      return false;
    }
  }

  /// Stop background music
  Future<void> stopBackgroundMusic() async {
    if (!_isInitialized) return;

    try {
      await _musicPlayer.stop();
      _currentMusicTrack = null;
      AppConfig.logInfo('Stopped background music');
    } catch (e) {
      AppConfig.logError('Failed to stop background music: $e');
    }
  }

  /// Pause background music
  Future<void> pauseBackgroundMusic() async {
    if (!_isInitialized || !_isMusicPlaying) return;

    try {
      await _musicPlayer.pause();
      AppConfig.logInfo('Paused background music');
    } catch (e) {
      AppConfig.logError('Failed to pause background music: $e');
    }
  }

  /// Resume background music
  Future<void> resumeBackgroundMusic() async {
    if (!_isInitialized || _isMusicPlaying) return;

    try {
      await _musicPlayer.resume();
      AppConfig.logInfo('Resumed background music');
    } catch (e) {
      AppConfig.logError('Failed to resume background music: $e');
    }
  }

  /// Play sound effect
  Future<bool> playSoundEffect(String assetPath) async {
    if (!_isInitialized || !_sfxEnabled) return false;

    try {
      await _sfxPlayer.play(AssetSource(assetPath));
      AppConfig.logInfo('Played sound effect: $assetPath');
      return true;
    } catch (e) {
      AppConfig.logError('Failed to play sound effect: $e');
      return false;
    }
  }

  /// Set background music volume
  Future<void> setMusicVolume(double volume) async {
    _musicVolume = volume.clamp(0.0, 1.0);
    
    if (_isInitialized) {
      await _musicPlayer.setVolume(_musicEnabled ? _musicVolume : 0.0);
    }
  }

  /// Set sound effects volume
  Future<void> setSfxVolume(double volume) async {
    _sfxVolume = volume.clamp(0.0, 1.0);
    
    if (_isInitialized) {
      await _sfxPlayer.setVolume(_sfxEnabled ? _sfxVolume : 0.0);
    }
  }

  /// Enable/disable background music
  Future<void> setMusicEnabled(bool enabled) async {
    _musicEnabled = enabled;
    
    if (_isInitialized) {
      if (enabled) {
        await _musicPlayer.setVolume(_musicVolume);
      } else {
        await _musicPlayer.setVolume(0.0);
      }
    }
  }

  /// Enable/disable sound effects
  Future<void> setSfxEnabled(bool enabled) async {
    _sfxEnabled = enabled;
    
    if (_isInitialized) {
      if (enabled) {
        await _sfxPlayer.setVolume(_sfxVolume);
      } else {
        await _sfxPlayer.setVolume(0.0);
      }
    }
  }

  /// Update settings from settings provider
  Future<void> updateFromSettings({
    required bool musicEnabled,
    required double musicVolume,
    required bool sfxEnabled,
    required double sfxVolume,
  }) async {
    await setMusicEnabled(musicEnabled);
    await setMusicVolume(musicVolume);
    await setSfxEnabled(sfxEnabled);
    await setSfxVolume(sfxVolume);
  }

  /// Fade out background music
  Future<void> fadeOutMusic({Duration duration = const Duration(seconds: 2)}) async {
    if (!_isInitialized || !_isMusicPlaying) return;

    try {
      const steps = 20;
      final stepDuration = duration.inMilliseconds ~/ steps;
      final volumeStep = _musicVolume / steps;

      for (int i = steps; i > 0; i--) {
        await _musicPlayer.setVolume(volumeStep * i);
        await Future.delayed(Duration(milliseconds: stepDuration));
      }

      await _musicPlayer.stop();
      await _musicPlayer.setVolume(_musicVolume); // Restore original volume
    } catch (e) {
      AppConfig.logError('Failed to fade out music: $e');
    }
  }

  /// Fade in background music
  Future<void> fadeInMusic(String assetPath, {Duration duration = const Duration(seconds: 2)}) async {
    if (!_isInitialized || !_musicEnabled) return;

    try {
      // Start playing at volume 0
      await _musicPlayer.setVolume(0.0);
      await _musicPlayer.play(AssetSource(assetPath));
      _currentMusicTrack = assetPath;

      // Fade in
      const steps = 20;
      final stepDuration = duration.inMilliseconds ~/ steps;
      final volumeStep = _musicVolume / steps;

      for (int i = 1; i <= steps; i++) {
        await _musicPlayer.setVolume(volumeStep * i);
        await Future.delayed(Duration(milliseconds: stepDuration));
      }
    } catch (e) {
      AppConfig.logError('Failed to fade in music: $e');
    }
  }

  /// Get available background music tracks
  List<String> getAvailableMusicTracks() {
    return [
      'audio/music/gentle_adventure.mp3',
      'audio/music/magical_forest.mp3',
      'audio/music/ocean_dreams.mp3',
      'audio/music/bedtime_lullaby.mp3',
    ];
  }

  /// Get available sound effects
  List<String> getAvailableSoundEffects() {
    return [
      'audio/sfx/page_turn.mp3',
      'audio/sfx/choice_select.mp3',
      'audio/sfx/story_complete.mp3',
      'audio/sfx/button_click.mp3',
      'audio/sfx/magic_sparkle.mp3',
    ];
  }

  /// Dispose of resources
  Future<void> dispose() async {
    try {
      await _musicPlayer.dispose();
      await _sfxPlayer.dispose();
      _isInitialized = false;
      AppConfig.logInfo('Audio service disposed');
    } catch (e) {
      AppConfig.logError('Failed to dispose audio service: $e');
    }
  }
}
