import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/parent_auth_provider.dart';
import '../screens/parent_auth_screen.dart';
import '../screens/onboarding_screen.dart';
import '../../main_menu/screens/main_menu_screen.dart';
import '../../profile/providers/active_child_provider.dart';

/// Widget that handles authentication state and navigation
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  @override
  void initState() {
    super.initState();
    // Trigger initial sync when app starts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _handleInitialSync();
    });
  }

  /// Handle initial sync when app starts
  Future<void> _handleInitialSync() async {
    final authProvider = Provider.of<ParentAuthProvider>(context, listen: false);
    final activeChildProvider = Provider.of<ActiveChildProvider>(context, listen: false);

    // If user is authenticated, try to sync data
    if (authProvider.isAuthenticated) {
      await _syncUserData(authProvider, activeChildProvider);
    }
  }

  /// Sync user data from cloud
  Future<void> _syncUserData(
    ParentAuthProvider authProvider,
    ActiveChildProvider activeChildProvider,
  ) async {
    try {
      // Refresh parent and child data
      await authProvider.refreshData();

      // If there are child profiles, try to sync the active child
      if (authProvider.hasChildren) {
        final activeProfile = activeChildProvider.activeProfile;

        if (activeProfile != null) {
          // Try to sync with cloud
          await activeChildProvider.syncWithCloud();
        } else if (authProvider.childProfiles.isNotEmpty) {
          // Set the first child as active if no active child
          final firstChild = authProvider.childProfiles.first;
          activeChildProvider.updateProfile(firstChild);
        }
      }
    } catch (e) {
      debugPrint('Error syncing user data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ParentAuthProvider>(
      builder: (context, authProvider, child) {
        // Show loading screen while checking auth state
        if (authProvider.isLoading) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading...'),
                ],
              ),
            ),
          );
        }

        // Show error if authentication failed
        if (authProvider.errorMessage != null) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Authentication Error',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      authProvider.errorMessage!,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      authProvider.clearError();
                    },
                    child: const Text('Try Again'),
                  ),
                ],
              ),
            ),
          );
        }

        // If authenticated, show main app
        if (authProvider.isAuthenticated) {
          // Guest users go directly to story library
          if (authProvider.isGuestMode) {
            return const MainMenuScreen();
          }

          // Regular authenticated users go through profile setup
          return _AuthenticatedApp(
            authProvider: authProvider,
            onSignOut: () => _handleSignOut(authProvider),
          );
        }

        // If not authenticated, show onboarding screen
        return const OnboardingScreen();
      },
    );
  }

  /// Handle sign out
  Future<void> _handleSignOut(ParentAuthProvider authProvider) async {
    try {
      await authProvider.signOut();

      // Clear active child data
      final activeChildProvider = Provider.of<ActiveChildProvider>(context, listen: false);
      activeChildProvider.clearProfile();

      // Navigate to auth screen (handled by AuthWrapper rebuild)
    } catch (e) {
      debugPrint('Error signing out: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error signing out: $e')),
        );
      }
    }
  }
}

/// Widget for authenticated app content
class _AuthenticatedApp extends StatelessWidget {
  final ParentAuthProvider authProvider;
  final VoidCallback onSignOut;

  const _AuthenticatedApp({
    required this.authProvider,
    required this.onSignOut,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ActiveChildProvider>(
      builder: (context, activeChildProvider, child) {
        // If no child profiles exist, show child creation screen
        if (!authProvider.hasChildren) {
          return _NoChildrenScreen(
            onCreateChild: () => _showCreateChildDialog(context),
          );
        }

        // If no active child is selected, show child selection
        if (activeChildProvider.activeProfile == null) {
          return _ChildSelectionScreen(
            children: authProvider.childProfiles,
            onChildSelected: (child) {
              activeChildProvider.updateProfile(child);
            },
          );
        }

        // Show main app
        return const MainMenuScreen();
      },
    );
  }

  /// Show create child dialog
  void _showCreateChildDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => _CreateChildDialog(
        onChildCreated: (name) async {
          final child = await authProvider.createChildProfile(name: name);
          if (child != null && context.mounted) {
            final activeChildProvider = Provider.of<ActiveChildProvider>(context, listen: false);
            activeChildProvider.updateProfile(child);
          }
        },
      ),
    );
  }
}

/// Screen shown when no children exist
class _NoChildrenScreen extends StatelessWidget {
  final VoidCallback onCreateChild;

  const _NoChildrenScreen({required this.onCreateChild});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Welcome!'),
        actions: [
          Consumer<ParentAuthProvider>(
            builder: (context, authProvider, child) {
              return IconButton(
                onPressed: () => authProvider.signOut(),
                icon: const Icon(Icons.logout),
                tooltip: 'Sign Out',
              );
            },
          ),
        ],
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.child_care,
                size: 80,
                color: Colors.blue,
              ),
              const SizedBox(height: 24),
              Text(
                'Create Your First Child Profile',
                style: Theme.of(context).textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              const Text(
                'Create a profile for your child to start their storytelling adventure!',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: onCreateChild,
                icon: const Icon(Icons.add),
                label: const Text('Create Child Profile'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Screen for selecting active child
class _ChildSelectionScreen extends StatelessWidget {
  final List children;
  final Function(dynamic) onChildSelected;

  const _ChildSelectionScreen({
    required this.children,
    required this.onChildSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Child'),
        actions: [
          Consumer<ParentAuthProvider>(
            builder: (context, authProvider, child) {
              return IconButton(
                onPressed: () => authProvider.signOut(),
                icon: const Icon(Icons.logout),
                tooltip: 'Sign Out',
              );
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: children.length,
        itemBuilder: (context, index) {
          final child = children[index];
          return Card(
            child: ListTile(
              leading: CircleAvatar(
                child: Text(child.name[0].toUpperCase()),
              ),
              title: Text(child.name),
              subtitle: Text('Last active: ${_formatDate(child.lastActive)}'),
              onTap: () => onChildSelected(child),
            ),
          );
        },
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else {
      return 'Recently';
    }
  }
}

/// Dialog for creating a new child
class _CreateChildDialog extends StatefulWidget {
  final Function(String) onChildCreated;

  const _CreateChildDialog({required this.onChildCreated});

  @override
  State<_CreateChildDialog> createState() => _CreateChildDialogState();
}

class _CreateChildDialogState extends State<_CreateChildDialog> {
  final _nameController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create Child Profile'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Child\'s Name',
              hintText: 'Enter your child\'s name',
            ),
            autofocus: true,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _createChild,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Create'),
        ),
      ],
    );
  }

  Future<void> _createChild() async {
    final name = _nameController.text.trim();
    if (name.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await widget.onChildCreated(name);
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error creating child: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
