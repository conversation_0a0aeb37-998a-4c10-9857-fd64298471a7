import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../shared_kernel/theme/app_theme.dart';
import '../models/child_profile.dart';
import '../providers/active_child_provider.dart';

/// Screen for managing child profiles
class ChildProfileScreen extends StatefulWidget {
  /// Constructor
  const ChildProfileScreen({super.key});

  /// Route name for navigation
  static const routeName = '/child-profiles';

  @override
  State<ChildProfileScreen> createState() => _ChildProfileScreenState();
}

class _ChildProfileScreenState extends State<ChildProfileScreen> {
  /// Text controller for new profile name
  final _nameController = TextEditingController();
  
  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }
  
  /// Show dialog to add a new profile
  void _showAddProfileDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Adventurer'),
        content: TextField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'Name',
            hintText: 'Enter adventurer\'s name',
          ),
          autofocus: true,
          textCapitalization: TextCapitalization.words,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _nameController.clear();
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final name = _nameController.text.trim();
              if (name.isNotEmpty) {
                // Create a new profile
                final profile = ChildProfile(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  name: name,
                );
                
                // Add to provider
                Provider.of<ActiveChildProvider>(context, listen: false)
                    .addProfile(profile);
                
                Navigator.of(context).pop();
                _nameController.clear();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }
  
  /// Show dialog to confirm profile deletion
  void _showDeleteConfirmation(ChildProfile profile) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Profile'),
        content: Text('Are you sure you want to delete ${profile.name}\'s profile?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Provider.of<ActiveChildProvider>(context, listen: false)
                  .removeProfile(profile.id);
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
  
  /// Show dialog to edit a profile
  void _showEditProfileDialog(ChildProfile profile) {
    _nameController.text = profile.name;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Adventurer'),
        content: TextField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'Name',
            hintText: 'Enter adventurer\'s name',
          ),
          autofocus: true,
          textCapitalization: TextCapitalization.words,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _nameController.clear();
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final name = _nameController.text.trim();
              if (name.isNotEmpty) {
                // Update the profile
                final updatedProfile = profile.copyWith(name: name);
                
                // Update in provider
                Provider.of<ActiveChildProvider>(context, listen: false)
                    .updateProfile(updatedProfile);
                
                Navigator.of(context).pop();
                _nameController.clear();
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Manage Adventurers'),
      ),
      body: Consumer<ActiveChildProvider>(
        builder: (context, provider, child) {
          final profiles = provider.profiles;
          final activeProfile = provider.activeProfile;
          
          return Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // List of profiles
                Expanded(
                  child: profiles.isEmpty
                      ? const Center(
                          child: Text(
                            'No adventurers yet. Add one to get started!',
                            style: TextStyle(fontSize: 18),
                            textAlign: TextAlign.center,
                          ),
                        )
                      : ListView.builder(
                          itemCount: profiles.length,
                          itemBuilder: (context, index) {
                            final profile = profiles[index];
                            final isActive = profile.id == activeProfile?.id;
                            
                            return Card(
                              margin: const EdgeInsets.only(bottom: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                                side: isActive
                                    ? BorderSide(
                                        color: AppTheme.primaryColor,
                                        width: 2,
                                      )
                                    : BorderSide.none,
                              ),
                              child: ListTile(
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                leading: CircleAvatar(
                                  backgroundColor: isActive
                                      ? AppTheme.primaryColor
                                      : Colors.grey[300],
                                  child: Text(
                                    profile.name.substring(0, 1).toUpperCase(),
                                    style: TextStyle(
                                      color: isActive
                                          ? Colors.white
                                          : Colors.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  profile.name,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                subtitle: isActive
                                    ? const Text(
                                        'Active Adventurer',
                                        style: TextStyle(
                                          color: AppTheme.primaryColor,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      )
                                    : null,
                                trailing: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // Make active button
                                    if (!isActive)
                                      IconButton(
                                        icon: const Icon(Icons.check_circle),
                                        tooltip: 'Make Active',
                                        onPressed: () {
                                          provider.setActiveProfile(profile.id);
                                        },
                                      ),
                                    
                                    // Edit button
                                    IconButton(
                                      icon: const Icon(Icons.edit),
                                      tooltip: 'Edit',
                                      onPressed: () => _showEditProfileDialog(profile),
                                    ),
                                    
                                    // Delete button
                                    IconButton(
                                      icon: const Icon(Icons.delete),
                                      tooltip: 'Delete',
                                      onPressed: () => _showDeleteConfirmation(profile),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                ),
                
                // Add profile button
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: ElevatedButton.icon(
                    onPressed: _showAddProfileDialog,
                    icon: const Icon(Icons.add),
                    label: const Text('Add New Adventurer'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
