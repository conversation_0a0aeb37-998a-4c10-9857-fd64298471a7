import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// Model representing a story character
class <PERSON><PERSON><PERSON><PERSON> {
  /// The name of the character
  final String name;

  /// Description of the character for visual reference
  final String description;

  /// Constructor
  <PERSON><PERSON>haracter({
    required this.name,
    required this.description,
  });

  /// Create a character from a map
  factory StoryCharacter.fromMap(Map<String, dynamic> map) {
    return StoryCharacter(
      name: map['characterName'] as String,
      description: map['characterDescriptionForVisual'] as String,
    );
  }
}

/// Model representing a choice in a story scene
class StoryChoice {
  /// The text of the choice
  final String text;

  /// The ID of the next scene
  final String nextSceneId;

  /// Constructor
  StoryChoice({
    required this.text,
    required this.nextSceneId,
  });

  /// Create a choice from a map
  factory StoryChoice.fromMap(Map<String, dynamic> map) {
    return StoryChoice(
      text: map['choiceText'] as String,
      nextSceneId: map['nextSceneId'] as String,
    );
  }
}

/// Model representing a scene in a story
class StoryScene {
  /// The ID of the scene
  final String id;

  /// The narration text for the scene
  final String narrationText;

  /// The path to the image asset
  final String imagePath;

  /// Whether this scene is a choice point
  final bool isChoicePoint;

  /// The prompt for the choice (if this is a choice point)
  final String? choicePrompt;

  /// The choices available (if this is a choice point)
  final List<StoryChoice> choices;

  /// Whether this scene is an ending scene
  final bool isEndingScene;

  /// The moral lesson reinforced in this scene (if any)
  final String? moralLesson;

  /// The ID of the next scene (if this is not a choice point)
  final String? defaultNextSceneId;

  /// Constructor
  StoryScene({
    required this.id,
    required this.narrationText,
    required this.imagePath,
    required this.isChoicePoint,
    this.choicePrompt,
    required this.choices,
    required this.isEndingScene,
    this.moralLesson,
    this.defaultNextSceneId,
  });

  /// Create a scene from a map
  factory StoryScene.fromMap(Map<String, dynamic> map) {
    final List<dynamic> choicesData = map['choices'] as List<dynamic>? ?? [];

    // Handle different JSON structures
    final String id = map['sceneId'] as String;
    final String narrationText = map['narrationText'] as String;

    // Handle different image path formats
    String imagePath = map['imageAssetPath'] as String? ?? '';
    if (imagePath.isEmpty && map.containsKey('image')) {
      imagePath = map['image'] as String;
    }

    // Some stories use storyNodes instead of scenes and might have different field names
    final bool isChoicePoint = map['isChoicePoint'] as bool? ??
                              (choicesData.isNotEmpty ? true : false);

    final bool isEndingScene = map['isEndingScene'] as bool? ?? false;

    return StoryScene(
      id: id,
      narrationText: narrationText,
      imagePath: imagePath,
      isChoicePoint: isChoicePoint,
      choicePrompt: map['choicePrompt'] as String?,
      choices: choicesData
          .map((choice) => StoryChoice.fromMap(choice as Map<String, dynamic>))
          .toList(),
      isEndingScene: isEndingScene,
      moralLesson: map['moralLessonReinforced'] as String?,
      defaultNextSceneId: map['defaultNextSceneId'] as String?,
    );
  }
}

/// Model representing a complete story
class Story {
  /// The ID of the story
  final String id;

  /// The title of the story
  final String title;

  /// The target age range
  final String? targetAge;

  /// The moral theme of the story
  final String? moralTheme;

  /// The path to the cover image
  final String? coverImagePath;

  /// Whether the story is locked (requires purchase)
  final bool isLocked;

  /// The list of characters in the story
  final List<StoryCharacter> characters;

  /// The list of scenes in the story
  final List<StoryScene> scenes;

  /// Constructor
  Story({
    required this.id,
    required this.title,
    this.targetAge,
    this.moralTheme,
    this.coverImagePath,
    this.isLocked = false,
    required this.characters,
    required this.scenes,
  });

  /// Get the first scene of the story
  StoryScene get firstScene => scenes.first;

  /// Get a scene by ID
  StoryScene? getSceneById(String sceneId) {
    try {
      return scenes.firstWhere((scene) => scene.id == sceneId);
    } catch (e) {
      debugPrint('Scene not found: $sceneId');
      return null;
    }
  }

  /// Get the next scene by array index (for auto-advance)
  StoryScene? getNextSceneByIndex(String currentSceneId) {
    try {
      final currentIndex = scenes.indexWhere((scene) => scene.id == currentSceneId);
      if (currentIndex != -1 && currentIndex < scenes.length - 1) {
        return scenes[currentIndex + 1];
      }
      return null;
    } catch (e) {
      debugPrint('Error finding next scene by index for: $currentSceneId');
      return null;
    }
  }

  /// Create a story from a map
  factory Story.fromMap(Map<String, dynamic> map) {
    // Handle different JSON structures for scenes
    List<dynamic> scenesData = [];
    if (map.containsKey('scenes')) {
      scenesData = map['scenes'] as List<dynamic>;
    } else if (map.containsKey('storyNodes')) {
      scenesData = map['storyNodes'] as List<dynamic>;
    } else {
      debugPrint('Warning: No scenes or storyNodes found in story JSON');
    }

    // Handle different JSON structures for characters
    List<dynamic> charactersData = [];
    if (map.containsKey('characterList')) {
      charactersData = map['characterList'] as List<dynamic>;
    }

    // Handle different ID and title fields
    String id = '';
    if (map.containsKey('storyId')) {
      id = map['storyId'] as String;
    } else if (map.containsKey('id')) {
      id = map['id'] as String;
    } else {
      // Extract ID from file path if available
      final String filePath = map['_filePath'] as String? ?? '';
      if (filePath.isNotEmpty) {
        final fileName = filePath.split('/').last;
        id = fileName.replaceAll('.json', '');
      }
    }

    // Handle different title fields
    String title = '';
    if (map.containsKey('storyTitle')) {
      title = map['storyTitle'] as String;
    } else if (map.containsKey('title')) {
      title = map['title'] as String;
    } else {
      // Use ID as title if no title is found
      title = id.replaceAll('-', ' ').replaceAll('_', ' ');
      // Capitalize each word
      title = title.split(' ').map((word) =>
        word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : ''
      ).join(' ');
    }

    return Story(
      id: id,
      title: title,
      targetAge: map['targetAge'] as String?,
      moralTheme: map['moralTheme'] as String?,
      coverImagePath: map['coverImage'] as String?,
      isLocked: map['isLocked'] as bool? ?? false,
      characters: charactersData
          .map((character) => StoryCharacter.fromMap(character as Map<String, dynamic>))
          .toList(),
      scenes: scenesData
          .map((scene) => StoryScene.fromMap(scene as Map<String, dynamic>))
          .toList(),
    );
  }

  /// Load a story from a JSON file
  static Future<Story> fromAsset(String assetPath) async {
    try {
      final String jsonString = await rootBundle.loadString(assetPath);
      final Map<String, dynamic> jsonMap = json.decode(jsonString);

      // Add the file path to the map for reference
      jsonMap['_filePath'] = assetPath;

      return Story.fromMap(jsonMap);
    } catch (e) {
      debugPrint('Error loading story from asset: $e');
      rethrow;
    }
  }
}
