import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import 'src/providers/story_provider.dart';
import 'src/providers/story_settings_provider.dart';
import 'src/screens/story_selection_screen.dart';
import 'src/screens/story_interaction_screen.dart';

void main() {
  // Force landscape orientation
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);
  
  runApp(const StoryInteractionTestApp());
}

class StoryInteractionTestApp extends StatelessWidget {
  const StoryInteractionTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => StoryProvider()),
        ChangeNotifierProvider(create: (_) => StorySettingsProvider()),
      ],
      child: MaterialApp(
        title: 'Story Interaction Test',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          fontFamily: 'Comic Sans MS',
        ),
        home: const StorySelectionScreen(),
        routes: {
          '/story': (context) => const StoryInteractionScreen(),
        },
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
