# Story Interaction Test Runner Guide

This document provides instructions for running the comprehensive test suite for the Story Interaction functionality.

## Prerequisites

1. Flutter SDK installed and configured
2. Chrome browser installed
3. Project dependencies installed: `flutter pub get`

## Test Types

### 1. Unit Tests

Unit tests verify the core logic and data models.

**Run all unit tests:**
```bash
flutter test test/unit/
```

**Run specific unit test files:**
```bash
flutter test test/unit/story_model_test.dart
flutter test test/unit/text_segmenter_test.dart
```

### 2. Widget Tests

Widget tests verify individual UI components.

**Run all widget tests:**
```bash
flutter test test/widget/
```

**Run specific widget test files:**
```bash
flutter test test/widget/highlighted_text_test.dart
flutter test test/widget/sentence_progress_indicator_test.dart
```

### 3. Integration Tests (Chrome)

Integration tests verify the complete story interaction flow in Chrome.

**Run integration tests in Chrome:**
```bash
flutter drive \
  --driver=test_driver/integration_test.dart \
  --target=integration_test/story_interaction_test.dart \
  -d chrome
```

**Alternative method using integration_test package:**
```bash
flutter test integration_test/story_interaction_test.dart -d chrome
```

## Test Coverage

**Generate test coverage report:**
```bash
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

Then open `coverage/html/index.html` in a browser to view the coverage report.

## Running All Tests

**Run all tests (unit + widget):**
```bash
flutter test
```

**Run all tests including integration tests:**
```bash
# Run unit and widget tests
flutter test

# Run integration tests
flutter test integration_test/ -d chrome
```

## Test Scenarios Covered

### Unit Tests
- ✅ Story model parsing and validation
- ✅ Text segmentation algorithms
- ✅ Choice navigation logic
- ✅ Scene state management

### Widget Tests
- ✅ Highlighted text rendering
- ✅ Progress indicator display
- ✅ Control panel interactions
- ✅ Settings panel functionality

### Integration Tests
- ✅ Complete story flow navigation
- ✅ Choice selection and branching
- ✅ Settings panel interactions
- ✅ Story restart functionality
- ✅ Responsive design on different screen sizes
- ✅ TTS integration (simulated)
- ✅ Image loading with fallbacks

## Debugging Tests

**Run tests with verbose output:**
```bash
flutter test --verbose
```

**Run a specific test with debugging:**
```bash
flutter test test/unit/story_model_test.dart --verbose
```

**Run integration tests with debugging:**
```bash
flutter drive \
  --driver=test_driver/integration_test.dart \
  --target=integration_test/story_interaction_test.dart \
  -d chrome \
  --verbose
```

## Continuous Integration

For CI/CD pipelines, use:

```bash
# Install dependencies
flutter pub get

# Run static analysis
flutter analyze

# Run all tests
flutter test

# Run integration tests (if Chrome is available)
flutter test integration_test/ -d chrome --headless
```

## Test Data

The tests use the following test story:
- **Story ID**: `corals-lost-colors`
- **Scenes**: Multiple scenes with choice points
- **Features tested**: Linear navigation, choice branching, story completion

## Expected Test Results

All tests should pass with the following approximate execution times:
- Unit tests: ~5-10 seconds
- Widget tests: ~10-15 seconds  
- Integration tests: ~30-60 seconds

## Troubleshooting

### Common Issues

1. **Chrome not found**: Ensure Chrome is installed and in PATH
2. **Tests timeout**: Increase timeout values in test configuration
3. **Asset loading failures**: Verify test assets are properly included
4. **TTS errors**: TTS functionality is mocked in tests

### Performance Considerations

- Integration tests may take longer on slower machines
- Use `--headless` flag for faster execution in CI environments
- Consider running tests in parallel for large test suites

## Adding New Tests

When adding new functionality:

1. **Add unit tests** for new logic/models
2. **Add widget tests** for new UI components  
3. **Update integration tests** for new user flows
4. **Update this documentation** with new test scenarios

## Test Environment

- **Target Platform**: Flutter Web (Chrome)
- **Orientation**: Landscape (enforced)
- **Screen Sizes Tested**: 375x667 (phone), 1024x768 (tablet)
- **TTS**: Mocked for testing purposes
- **Images**: Placeholder/error handling tested
