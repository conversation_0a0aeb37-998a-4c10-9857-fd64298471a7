import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/story_provider.dart';
import 'story_interaction_screen.dart';

/// Simple story selection screen for testing
class StorySelectionScreen extends StatelessWidget {
  const StorySelectionScreen({super.key});

  // Available test stories
  static const List<Map<String, String>> _testStories = [
    {
      'id': 'corals-lost-colors',
      'title': 'Coral\'s Lost Colors',
      'description': 'A story about a coral reef losing its colors',
    },
    {
      'id': 'the_lost_kitten',
      'title': 'The Lost Kitten',
      'description': 'A heartwarming tale about finding a lost kitten',
    },
    {
      'id': 'leo-the-little-lions-big-roar',
      'title': '<PERSON> the Little Lion\'s Big Roar',
      'description': 'A young lion learns to find his voice',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.blue[50],
      appBar: AppBar(
        title: const Text('Story Interaction Test'),
        backgroundColor: Colors.blue[200],
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.blue[50]!, Colors.blue[100]!],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.auto_stories,
                size: 80,
                color: Colors.blue,
              ),
              const SizedBox(height: 20),
              const Text(
                'Choose a Story to Test',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(height: 40),
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  itemCount: _testStories.length,
                  itemBuilder: (context, index) {
                    final story = _testStories[index];
                    return _buildStoryCard(context, story);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStoryCard(BuildContext context, Map<String, String> story) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _selectStory(context, story['id']!),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.book,
                  color: Colors.blue,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      story['title']!,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      story['description']!,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                color: Colors.blue,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _selectStory(BuildContext context, String storyId) {
    // Load the story and navigate to the interaction screen
    final storyProvider = Provider.of<StoryProvider>(context, listen: false);
    
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );
    
    storyProvider.loadStory(storyId).then((_) {
      // Close loading dialog
      Navigator.of(context).pop();
      
      if (storyProvider.errorMessage != null) {
        // Show error dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Error'),
            content: Text(storyProvider.errorMessage!),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      } else {
        // Navigate to story interaction screen
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const StoryInteractionScreen(),
          ),
        );
      }
    });
  }
}
