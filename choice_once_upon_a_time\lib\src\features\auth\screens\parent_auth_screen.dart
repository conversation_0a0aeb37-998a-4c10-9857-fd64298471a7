import 'package:flutter/material.dart';
import '../../../shared_kernel/theme/app_theme.dart';

/// Screen for parent authentication (login/signup)
class ParentAuthScreen extends StatefulWidget {
  /// Constructor
  const ParentAuthScreen({super.key});

  /// Route name for navigation
  static const routeName = '/parent-auth';

  @override
  State<ParentAuthScreen> createState() => _ParentAuthScreenState();
}

class _ParentAuthScreenState extends State<ParentAuthScreen> {
  /// Whether to show the login form (true) or signup form (false)
  bool _showLogin = true;
  
  /// Form key for validation
  final _formKey = GlobalKey<FormState>();
  
  /// Text controllers
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }
  
  /// Toggle between login and signup forms
  void _toggleForm() {
    setState(() {
      _showLogin = !_showLogin;
    });
  }
  
  /// Handle form submission
  void _submitForm() {
    if (_formKey.currentState?.validate() ?? false) {
      // In a real app, we would authenticate with Firebase
      // For now, just show a success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_showLogin ? 'Login successful!' : 'Account created!'),
        ),
      );
      
      // Navigate back
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_showLogin ? 'Parent Sign In' : 'Create Parent Account'),
      ),
      body: Center(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 400),
              child: Form(
                key: _formKey,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // App logo or icon
                    Icon(
                      Icons.book,
                      size: 80,
                      color: AppTheme.primaryColor,
                    ),
                    const SizedBox(height: 32),
                    
                    // Title
                    Text(
                      _showLogin ? 'Welcome Back!' : 'Join Us!',
                      style: AppTheme.headingStyle,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 32),
                    
                    // Email field
                    TextFormField(
                      controller: _emailController,
                      decoration: const InputDecoration(
                        labelText: 'Email',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.email),
                      ),
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your email';
                        }
                        if (!value.contains('@')) {
                          return 'Please enter a valid email';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // Password field
                    TextFormField(
                      controller: _passwordController,
                      decoration: const InputDecoration(
                        labelText: 'Password',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.lock),
                      ),
                      obscureText: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your password';
                        }
                        if (value.length < 6) {
                          return 'Password must be at least 6 characters';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // Confirm password field (signup only)
                    if (!_showLogin)
                      TextFormField(
                        controller: _confirmPasswordController,
                        decoration: const InputDecoration(
                          labelText: 'Confirm Password',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.lock),
                        ),
                        obscureText: true,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please confirm your password';
                          }
                          if (value != _passwordController.text) {
                            return 'Passwords do not match';
                          }
                          return null;
                        },
                      ),
                    if (!_showLogin) const SizedBox(height: 16),
                    
                    // Submit button
                    ElevatedButton(
                      onPressed: _submitForm,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: Text(
                        _showLogin ? 'Sign In' : 'Create Account',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Toggle form button
                    TextButton(
                      onPressed: _toggleForm,
                      child: Text(
                        _showLogin
                            ? 'Don\'t have an account? Sign Up'
                            : 'Already have an account? Sign In',
                      ),
                    ),
                    
                    // Forgot password (login only)
                    if (_showLogin)
                      TextButton(
                        onPressed: () {
                          // Show forgot password dialog
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Forgot Password - Coming Soon'),
                            ),
                          );
                        },
                        child: const Text('Forgot Password?'),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
