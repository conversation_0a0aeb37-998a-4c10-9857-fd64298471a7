import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/parent_auth_provider.dart';
import '../../../shared_kernel/theme/app_theme.dart';

/// Screen for parent authentication (login/signup)
class ParentAuthScreen extends StatefulWidget {
  /// Constructor
  const ParentAuthScreen({super.key});

  /// Route name for navigation
  static const routeName = '/parent-auth';

  @override
  State<ParentAuthScreen> createState() => _ParentAuthScreenState();
}

class _ParentAuthScreenState extends State<ParentAuthScreen> {
  /// Whether to show the login form (true) or signup form (false)
  bool _showLogin = true;

  /// Form key for validation
  final _formKey = GlobalKey<FormState>();

  /// Text controllers
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  /// Toggle between login and signup forms
  void _toggleForm() {
    setState(() {
      _showLogin = !_showLogin;
    });
  }

  /// Handle form submission
  Future<void> _submitForm() async {
    if (!(_formKey.currentState?.validate() ?? false)) return;

    final authProvider = Provider.of<ParentAuthProvider>(context, listen: false);

    bool success;
    if (_showLogin) {
      success = await authProvider.signIn(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );
    } else {
      success = await authProvider.signUp(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        displayName: _emailController.text.split('@')[0], // Use email prefix as display name
      );
    }

    if (success && mounted) {
      Navigator.of(context).pop();
    }
  }

  /// Send password reset email
  Future<void> _sendPasswordReset() async {
    if (_emailController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter your email address')),
      );
      return;
    }

    final authProvider = Provider.of<ParentAuthProvider>(context, listen: false);

    final success = await authProvider.sendPasswordResetEmail(_emailController.text.trim());

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Password reset email sent!')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ParentAuthProvider>(
      builder: (context, authProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(_showLogin ? 'Parent Sign In' : 'Create Parent Account'),
          ),
          body: Center(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 400),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                    // App logo or icon
                    Icon(
                      Icons.book,
                      size: 80,
                      color: AppTheme.primaryColor,
                    ),
                    const SizedBox(height: 32),

                    // Title
                    Text(
                      _showLogin ? 'Welcome Back!' : 'Join Us!',
                      style: AppTheme.headingStyle,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 32),

                    // Email field
                    TextFormField(
                      controller: _emailController,
                      decoration: const InputDecoration(
                        labelText: 'Email',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.email),
                      ),
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your email';
                        }
                        if (!value.contains('@')) {
                          return 'Please enter a valid email';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Password field
                    TextFormField(
                      controller: _passwordController,
                      decoration: const InputDecoration(
                        labelText: 'Password',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.lock),
                      ),
                      obscureText: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your password';
                        }
                        if (value.length < 6) {
                          return 'Password must be at least 6 characters';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Confirm password field (signup only)
                    if (!_showLogin)
                      TextFormField(
                        controller: _confirmPasswordController,
                        decoration: const InputDecoration(
                          labelText: 'Confirm Password',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.lock),
                        ),
                        obscureText: true,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please confirm your password';
                          }
                          if (value != _passwordController.text) {
                            return 'Passwords do not match';
                          }
                          return null;
                        },
                      ),
                    if (!_showLogin) const SizedBox(height: 16),

                    // Submit button
                    ElevatedButton(
                      onPressed: authProvider.isLoading ? null : _submitForm,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: authProvider.isLoading
                          ? const CircularProgressIndicator()
                          : Text(
                              _showLogin ? 'Sign In' : 'Create Account',
                              style: const TextStyle(fontSize: 16),
                            ),
                    ),
                    const SizedBox(height: 16),

                    // Error message
                    if (authProvider.errorMessage != null)
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red[200]!),
                        ),
                        child: Text(
                          authProvider.errorMessage!,
                          style: TextStyle(color: Colors.red[700]),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    if (authProvider.errorMessage != null) const SizedBox(height: 16),

                    // Toggle form button
                    TextButton(
                      onPressed: _toggleForm,
                      child: Text(
                        _showLogin
                            ? 'Don\'t have an account? Sign Up'
                            : 'Already have an account? Sign In',
                      ),
                    ),

                    // Forgot password (login only)
                    if (_showLogin)
                      TextButton(
                        onPressed: _sendPasswordReset,
                        child: const Text('Forgot Password?'),
                      ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
