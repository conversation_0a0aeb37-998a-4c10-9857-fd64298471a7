import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/story_model.dart';
import '../services/text_segmenter.dart';

/// Class to hold validated image information
class ValidatedImageInfo {
  final String sceneId;
  final String validPath;
  final bool isLoaded;

  ValidatedImageInfo({
    required this.sceneId,
    required this.validPath,
    this.isLoaded = false,
  });

  ValidatedImageInfo copyWith({bool? isLoaded}) {
    return ValidatedImageInfo(
      sceneId: sceneId,
      validPath: validPath,
      isLoaded: isLoaded ?? this.isLoaded,
    );
  }
}

/// Provider to manage story state
class StoryProvider extends ChangeNotifier {
  /// List of all available stories
  List<Story> _stories = [];

  /// The currently active story
  Story? _activeStory;

  /// The current scene in the active story
  StoryScene? _currentScene;

  /// History of scenes visited in the current story
  final List<String> _sceneHistory = [];

  /// History of choices made in the current story
  final List<Map<String, dynamic>> _choiceHistory = [];

  /// Loading state
  bool _isLoading = false;

  /// Error message (if any)
  String? _errorMessage;

  /// Whether the narration is complete
  bool _isNarrationComplete = false;

  /// Whether the choices should be shown
  bool _showChoices = false;

  /// Whether the entire scene narration is complete (all segments)
  bool _isSceneNarrationComplete = false;

  /// Whether navigation is blocked until narration completes
  bool _isNavigationBlocked = false;

  /// The current text segments for the current scene
  List<String> _currentTextSegments = [];

  /// The index of the current text segment
  int _currentSegmentIndex = 0;

  /// Whether auto-proceed is enabled
  bool _autoProceedEnabled = true;

  /// Timer for auto-proceeding to the next scene
  Timer? _autoProceedTimer;

  /// Map of scene IDs to their validated image information
  final Map<String, ValidatedImageInfo> _validatedImages = {};

  /// **NEW**: Map of story IDs to their validated explicit cover image paths
  final Map<String, String> _validatedStoryCoverImages = {};

  /// Set of currently loading images to prevent duplicate requests
  final Set<String> _loadingImages = {};

  /// Cache of loaded images by scene ID
  final Map<String, bool> _imageCache = {};

  /// Getters
  List<Story> get stories => _stories;
  Story? get activeStory => _activeStory;
  StoryScene? get currentScene => _currentScene;
  List<String> get sceneHistory => List.unmodifiable(_sceneHistory);
  List<Map<String, dynamic>> get choiceHistory => List.unmodifiable(_choiceHistory);
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isNarrationComplete => _isNarrationComplete;
  bool get showChoices => _showChoices;
  bool get isSceneNarrationComplete => _isSceneNarrationComplete;
  bool get isNavigationBlocked => _isNavigationBlocked;
  List<String> get currentTextSegments => _currentTextSegments;
  int get currentSegmentIndex => _currentSegmentIndex;
  String get currentTextSegment =>
      _currentSegmentIndex >= 0 && _currentSegmentIndex < _currentTextSegments.length
          ? _currentTextSegments[_currentSegmentIndex]
          : '';
  bool get autoProceedEnabled => _autoProceedEnabled;

  /// Get validated image info for a scene
  ValidatedImageInfo? getValidatedImageInfo(String sceneId) => _validatedImages[sceneId];

  /// **NEW**: Get validated explicit cover image path for a story
  String? getValidatedStoryCoverPath(String storyId) => _validatedStoryCoverImages[storyId];

  /// Check if an image is currently loading
  bool isImageLoading(String imagePath) => _loadingImages.contains(imagePath);

  /// Check if an image is cached
  bool isImageCached(String sceneId) => _imageCache[sceneId] == true;

  /// Pre-validate story assets and create image mappings
  Future<void> _validateStoryAssets(Story story) async {
    debugPrint('Validating assets for story: ${story.id}');

    // Get the asset manifest to check available files
    final manifestContent = await rootBundle.loadString('AssetManifest.json');
    final Map<String, dynamic> manifestMap = Map.from(
      json.decode(manifestContent) as Map<dynamic, dynamic>,
    );

    final availableAssets = manifestMap.keys.toSet();

    // **ENHANCEMENT**: Validate explicit cover image for the story
    if (story.coverImagePath != null && story.coverImagePath!.isNotEmpty) {
      final validCoverPath = await _findValidImagePathFromManifest(
        story.coverImagePath!,
        availableAssets,
      );
      if (validCoverPath != null) {
        _validatedStoryCoverImages[story.id] = validCoverPath;
        debugPrint('Validated explicit cover for story ${story.id}: $validCoverPath');
      } else {
        debugPrint('No valid explicit cover found for story ${story.id}: ${story.coverImagePath}');
      }
    }

    // Validate each scene's image
    for (final scene in story.scenes) {
      if (scene.imagePath == null) continue;

      final validPath = await _findValidImagePathFromManifest(
        scene.imagePath!,
        availableAssets,
      );

      if (validPath != null) {
        _validatedImages[scene.id] = ValidatedImageInfo(
          sceneId: scene.id,
          validPath: validPath,
        );
        debugPrint('Validated image for scene ${scene.id}: $validPath');
      } else {
        debugPrint('No valid image found for scene ${scene.id}: ${scene.imagePath}');
      }
    }
  }

  /// Find a valid image path from the asset manifest
  Future<String?> _findValidImagePathFromManifest(
    String assetPathFromJson, // e.g., "assets/story1/cover" or "assets/story1/cover.jpeg"
    Set<String> availableAssets, // Exact paths from AssetManifest.json (case-sensitive)
  ) async {
    // 1. Direct check: If the path from JSON exists as-is in the manifest.
    if (availableAssets.contains(assetPathFromJson)) {
      return assetPathFromJson;
    }

    String baseName = assetPathFromJson;
    String originalExtension = "";

    int dotIndex = assetPathFromJson.lastIndexOf('.');
    // Ensure the dot is for an extension, not part of a directory name.
    int lastSlashIndex = assetPathFromJson.lastIndexOf('/');
    if (dotIndex > lastSlashIndex) {
      baseName = assetPathFromJson.substring(0, dotIndex); // Part before the extension
      originalExtension = assetPathFromJson.substring(dotIndex); // e.g., ".jpeg", ".JPEG"
    }
    // If no dot, baseName remains assetPathFromJson, originalExtension remains "".

    // 2. Try a list of common extensions and their typical casings with the derived baseName.
    //    This is the most crucial part for handling .jpeg/.JPEG etc.
    const List<String> commonExtensionsToTry = [
      '.png', '.PNG',
      '.jpg', '.JPG',
      '.jpeg', '.JPEG', // Explicitly trying both common casings for .jpeg
      '.webp', '.WEBP'
    ];

    for (final String ext in commonExtensionsToTry) {
      String testPath = baseName + ext;
      if (availableAssets.contains(testPath)) {
        return testPath; // Found a match with one of the common extensions/casings
      }
    }

    // 3. If the original path from JSON had an extension (originalExtension is not empty),
    //    and it wasn't found in step 2 (e.g., it was ".Jpg" which is not in commonExtensionsToTry),
    //    try its direct lowercase and uppercase versions.
    //    This is a fallback for less common casings not in the list above.
    if (originalExtension.isNotEmpty) {
      String pathWithLowerExt = baseName + originalExtension.toLowerCase();
      if (availableAssets.contains(pathWithLowerExt)) {
        return pathWithLowerExt;
      }
      String pathWithUpperExt = baseName + originalExtension.toUpperCase();
      if (availableAssets.contains(pathWithUpperExt)) {
        return pathWithUpperExt;
      }
    }

    // 4. If assetPathFromJson was originally extensionless (i.e. baseName == assetPathFromJson)
    //    and it was not found by appending extensions, this means we couldn't find a match.
    //    The case where an extensionless path in the manifest should match an extensionless path
    //    from JSON is covered by step 1.

    debugPrint('No valid image found in manifest for base: $assetPathFromJson (derived baseName: $baseName)');
    return null; // No valid path found
  }

  /// Load all stories from the assets directory
  Future<void> loadAllStories() async {
    _setLoading(true);
    _stories = [];

    try {
      // Get list of story JSON files
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = Map.from(
        json.decode(manifestContent) as Map<dynamic, dynamic>,
      );

      final storyPaths = manifestMap.keys
          .where((String key) =>
              (key.startsWith('assets/images/') || key.startsWith('assets/stories/')) && // Modified to include stories in images folder for flexibility if any
              key.endsWith('.json') &&
              !key.contains('/ui_elements/')) // Assuming ui_elements will not contain story JSONs
          .toList();

      // Load each story and validate its assets
      for (final path in storyPaths) {
        try {
          final story = await Story.fromAsset(path); // Assumes Story.fromAsset is in story_model.dart
          await _validateStoryAssets(story);
          _stories.add(story);
        } catch (e) {
          debugPrint('Error loading story from $path: $e');
        }
      }

      _setLoading(false);
    } catch (e) {
      _setError('Error loading stories: $e');
    }
  }

  /// Set the active story by ID and load the first scene
  Future<void> setActiveStoryById(String storyId, {String? initialSceneId}) async {
    _setLoading(true);
    _clearStoryState(); // _clearStoryState will also clear new _validatedStoryCoverImages map

    try {
      // Find the story by ID
      final story = _stories.firstWhere(
        (story) => story.id == storyId,
        orElse: () => throw Exception('Story not found: $storyId'),
      );

      _activeStory = story;

      // **IMPORTANT**: Re-validate assets for the active story to populate _validatedImages
      // This is crucial if _clearStoryState clears _validatedImages and _validatedStoryCoverImages
      // However, _validatedStoryCoverImages is populated during loadAllStories.
      // If setActiveStory is called for a story already in _stories, its cover is already validated.
      // _validatedImages for scenes are specific to an active story session.
      await _validateStoryAssets(story); // Ensures scene images are validated for this active story

      // Load the initial scene
      final sceneId = initialSceneId ?? story.firstScene.id;
      await navigateToSceneAsync(sceneId);

      _setLoading(false);
    } catch (e) {
      _setError('Error setting active story: $e');
    }
  }

  /// Navigate to a specific scene
  Future<void> navigateToSceneAsync(String sceneId) async {
    if (_activeStory == null) {
      _setError('No active story');
      return;
    }

    _setLoading(true);
    _isNarrationComplete = false;
    _showChoices = false;
    _isSceneNarrationComplete = false;
    _isNavigationBlocked = true; // Block navigation until narration completes

    // Cancel any pending auto-proceed
    _autoProceedTimer?.cancel();

    try {
      // Find the scene by ID
      final scene = _activeStory!.getSceneById(sceneId);

      if (scene == null) {
        throw Exception('Scene not found: $sceneId');
      }

      _currentScene = scene;
      _sceneHistory.add(sceneId);

      // Segment the narration text
      _segmentText(scene.narrationText); // Assumes _segmentText uses TextSegmenter

      // Mark image as loaded for this scene if it's validated
      final validatedImage = _validatedImages[scene.id];
      if (validatedImage != null) {
        _imageCache[scene.id] = true;
        _validatedImages[scene.id] = validatedImage.copyWith(isLoaded: true);
      }

      _setLoading(false);

      // Start narrating the first segment immediately
      if (_currentTextSegments.isNotEmpty) {
        _startNarration();
      } else {
        // If no segments, handle auto-advance immediately
        _handleEmptySceneNavigation(scene);
      }
    } catch (e) {
      _setError('Error navigating to scene: $e');
    }
  }

  /// Determine the next scene ID for auto-advance
  String? _determineNextSceneId(StoryScene currentScene) {
    // If there's an explicit defaultNextSceneId, use it
    if (currentScene.defaultNextSceneId != null) {
      debugPrint('Using explicit defaultNextSceneId: ${currentScene.defaultNextSceneId}');
      return currentScene.defaultNextSceneId;
    }

    // If no explicit next scene and not an ending scene,
    // auto-advance to the next scene in the JSON array sequence
    if (!currentScene.isEndingScene && _activeStory != null) {
      final nextScene = _activeStory!.getNextSceneByIndex(currentScene.id);
      if (nextScene != null) {
        debugPrint('Auto-advancing to next scene in sequence: ${nextScene.id}');
        return nextScene.id;
      } else {
        debugPrint('No next scene found in sequence for: ${currentScene.id}');
      }
    }

    return null;
  }

  /// Start narrating the current segment
  void _startNarration() {
    if (_currentTextSegments.isEmpty || _currentSegmentIndex >= _currentTextSegments.length) {
      debugPrint('Cannot start narration: No segments or invalid index');
      return;
    }

    final text = _currentTextSegments[_currentSegmentIndex];
    debugPrint('Starting narration for segment $_currentSegmentIndex: "$text"');

    // Simulate TTS completion after a delay
    // In a real app, this would be triggered by actual TTS completion
    Future.delayed(const Duration(seconds: 2), () {
      _onNarrationComplete();
    });
  }

  /// Handle narration completion
  void _onNarrationComplete() {
    debugPrint('Narration complete for segment $_currentSegmentIndex in scene: ${_currentScene?.id}');

    if (_currentScene == null) {
      return;
    }

    _isNarrationComplete = true;

    if (_currentSegmentIndex < _currentTextSegments.length - 1) {
      // More segments in current scene - advance to next segment
      debugPrint('Advancing to next segment: ${_currentSegmentIndex + 1}');
      _scheduleAutoAction(() => _advanceToNextSegment());
    } else {
      // Last segment of scene - handle scene completion
      _handleSceneCompletion();
    }

    notifyListeners();
  }

  /// Handle completion of a scene (last segment)
  void _handleSceneCompletion() {
    if (_currentScene == null || !_isSceneNarrationComplete) {
      debugPrint('Scene completion blocked - scene: ${_currentScene?.id}, narration complete: $_isSceneNarrationComplete');
      return;
    }

    if (_currentScene!.isChoicePoint && _currentScene!.choices.isNotEmpty) {
      debugPrint('All narration complete - showing choices for scene: ${_currentScene!.id}');
      _showChoices = true;
    } else if (!_currentScene!.isEndingScene) {
      // Auto-advance to next scene only after complete narration
      final nextSceneId = _determineNextSceneId(_currentScene!);
      if (nextSceneId != null) {
        debugPrint('All narration complete - auto-advancing to next scene: $nextSceneId');
        _scheduleAutoProceed(nextSceneId);
      } else {
        debugPrint('No next scene found, story may be complete');
      }
    } else {
      debugPrint('Reached ending scene: ${_currentScene!.id}');
    }
  }

  /// Handle navigation for scenes with no text segments
  void _handleEmptySceneNavigation(StoryScene scene) {
    debugPrint('Handling empty scene navigation for: ${scene.id}');

    if (scene.isChoicePoint) {
      _showChoices = true;
      notifyListeners();
    } else if (!scene.isEndingScene) {
      final nextSceneId = _determineNextSceneId(scene);
      if (nextSceneId != null) {
        debugPrint('Empty scene auto-advancing to: $nextSceneId');
        _scheduleAutoProceed(nextSceneId);
      }
    }
  }

  /// Advance to the next text segment within the same scene
  void _advanceToNextSegment() {
    if (_currentSegmentIndex < _currentTextSegments.length - 1) {
      _currentSegmentIndex++;
      _isNarrationComplete = false;
      debugPrint('Advanced to segment $_currentSegmentIndex in scene: ${_currentScene?.id}');
      notifyListeners();
      // Note: The StoryInteractionScreen will handle starting narration for the new segment
    }
  }

  /// Segment the narration text into smaller chunks
  void _segmentText(String text) {
    _currentTextSegments = TextSegmenter.splitIntoSegments(text);
    _currentSegmentIndex = 0;
  }

  /// Handle a choice selection
  Future<void> makeChoiceAsync(String choiceText, String nextSceneId) async {
    if (_currentScene == null) {
      _setError('No current scene');
      return;
    }

    // Record the choice
    _choiceHistory.add({
      'sceneId': _currentScene!.id,
      'choiceText': choiceText,
      'nextSceneId': nextSceneId,
    });

    // Hide choices
    _showChoices = false;

    // Navigate to the next scene
    await navigateToSceneAsync(nextSceneId);
  }

  /// Go back to the previous choice point
  Future<void> goBackToPreviousChoice() async {
    if (_choiceHistory.isEmpty) {
      // If no choices made, go back to the first scene
      if (_activeStory != null) {
        await navigateToSceneAsync(_activeStory!.firstScene.id);
      }
      return;
    }

    // Remove the last choice
    final lastChoice = _choiceHistory.removeLast();

    // Navigate to the scene where the choice was made
    await navigateToSceneAsync(lastChoice['sceneId'] as String);
  }

  /// Restart the current story
  Future<void> restartStory() async {
    if (_activeStory == null) {
      _setError('No active story');
      return;
    }

    _clearStoryState();
    await setActiveStoryById(_activeStory!.id); // Changed to call setActiveStoryById to ensure re-validation
  }

  /// Mark narration as complete (called by TTS when done)
  void completeNarration() {
    debugPrint('Narration complete for segment $_currentSegmentIndex of ${_currentTextSegments.length} in scene: ${_currentScene?.id}');

    _isNarrationComplete = true;

    if (_currentScene == null) {
      debugPrint('No current scene, cannot complete narration');
      notifyListeners();
      return;
    }

    // Check if there are more segments in the current scene
    if (_currentSegmentIndex < _currentTextSegments.length - 1) {
      // More segments to narrate - advance to next segment
      debugPrint('Advancing to next segment: ${_currentSegmentIndex + 1}');
      _scheduleAutoAction(() => _advanceToNextSegment());
    } else {
      // Last segment of scene - mark scene narration as complete
      debugPrint('All segments completed for scene: ${_currentScene!.id}');
      _isSceneNarrationComplete = true;
      _isNavigationBlocked = false; // Allow navigation now
      _handleSceneCompletion();
    }

    notifyListeners();
  }



  /// Schedule auto-proceeding to the next scene
  void _scheduleAutoProceed(String nextSceneId) {
    // Cancel any existing timer
    _autoProceedTimer?.cancel();

    // Schedule a new timer
    _autoProceedTimer = Timer(const Duration(milliseconds: 800), () {
      if (_currentScene != null &&
          !_currentScene!.isChoicePoint &&
          !_currentScene!.isEndingScene) {
        navigateToSceneAsync(nextSceneId);
      }
    });
  }

  /// Schedule auto-proceeding with a custom action
  void _scheduleAutoAction(VoidCallback action) {
    // Cancel any existing timer
    _autoProceedTimer?.cancel();

    // Schedule a new timer
    _autoProceedTimer = Timer(const Duration(milliseconds: 800), action);
  }

  /// Enable or disable auto-proceeding
  void setAutoProceedEnabled(bool enabled) {
    _autoProceedEnabled = enabled;

    if (!enabled) {
      // Cancel any pending auto-proceed
      _autoProceedTimer?.cancel();
    } else if (_isNarrationComplete &&
               _currentScene != null &&
               !_currentScene!.isChoicePoint &&
               !_currentScene!.isEndingScene &&
               _currentSegmentIndex == _currentTextSegments.length - 1) {
      // If narration is already complete and we're enabling auto-proceed,
      // schedule an auto-proceed
      final nextSceneId = _determineNextSceneId(_currentScene!);
      if (nextSceneId != null) {
        _scheduleAutoProceed(nextSceneId);
      }
    }

    notifyListeners();
  }

  /// Navigate to the next text segment
  bool navigateToNextSegment() {
    // Allow manual navigation even if blocked for user control
    if (_currentSegmentIndex < _currentTextSegments.length - 1) {
      _currentSegmentIndex++;
      _isNarrationComplete = false;
      debugPrint('Manually navigated to next segment: $_currentSegmentIndex');
      notifyListeners();
      // Note: StoryInteractionScreen will handle starting narration
      return true;
    } else if (_currentScene != null &&
               !_currentScene!.isChoicePoint &&
               !_currentScene!.isEndingScene &&
               _isSceneNarrationComplete) {
      // Only allow scene navigation if scene narration is complete
      final nextSceneId = _determineNextSceneId(_currentScene!);
      if (nextSceneId != null) {
        navigateToSceneAsync(nextSceneId);
        return true;
      }
    }
    return false;
  }

  /// Navigate to the previous text segment
  bool navigateToPreviousSegment() {
    if (_currentSegmentIndex > 0) {
      _currentSegmentIndex--;
      _isNarrationComplete = false;
      debugPrint('Navigated to previous segment: $_currentSegmentIndex');
      notifyListeners();
      // Note: StoryInteractionScreen will handle starting narration
      return true;
    } else if (_sceneHistory.length > 1) {
      // If we're at the first segment and there's a previous scene,
      // navigate to it by removing current and going to new last
      _sceneHistory.removeLast(); // Remove current scene from history
      final previousSceneId = _sceneHistory.removeLast(); // Get and remove the actual previous scene
                                                        // navigateToSceneAsync will re-add it.
      navigateToSceneAsync(previousSceneId);
      return true;
    }
    return false;
  }


  /// Toggle showing choices
  void setShowChoices(bool show) {
    _showChoices = show;
    notifyListeners();
  }

  /// Mark an image as loading to prevent duplicate requests
  void markImageAsLoading(String imagePath) {
    _loadingImages.add(imagePath);
    notifyListeners();
  }

  /// Mark an image as finished loading
  void markImageAsLoaded(String imagePath, String sceneId) {
    _loadingImages.remove(imagePath);
    _imageCache[sceneId] = true;

    // Update validated image info if it exists
    final validatedImage = _validatedImages[sceneId];
    if (validatedImage != null) {
      _validatedImages[sceneId] = validatedImage.copyWith(isLoaded: true);
    }

    notifyListeners();
  }

  /// Clear the current story state
  void _clearStoryState() {
    // _activeStory = null; // Should not clear active story if we intend to restart it.
                         // Let setActiveStory manage this.
    _currentScene = null;
    _sceneHistory.clear();
    _choiceHistory.clear();
    _isNarrationComplete = false;
    _showChoices = false;
    _isSceneNarrationComplete = false;
    _isNavigationBlocked = false;
    _currentTextSegments = [];
    _currentSegmentIndex = 0;

    // Cancel any pending auto-proceed
    _autoProceedTimer?.cancel();

    // Clear image loading state for the current story
    // Note: _validatedStoryCoverImages is related to all stories, not just active,
    // so it should be cleared when all stories are reloaded, not per active story.
    // However, _validatedImages IS per active story session.
    _loadingImages.clear();
    _imageCache.clear();
    _validatedImages.clear(); // Clears scene-specific validated images
  }

  /// Clear all loaded stories and their validated cover images.
  /// Typically called before a fresh loadAllStories.
  void clearAllStoryData() {
      _stories = [];
      _validatedStoryCoverImages.clear();
      _activeStory = null;
      _clearStoryState(); // Clear active story specific states too
      notifyListeners();
  }


  @override
  void dispose() {
    _autoProceedTimer?.cancel();
    super.dispose();
  }

  /// Set the active story directly for testing purposes
  /// This method should only be used in tests
  Future<void> setActiveStoryForTesting(Story story) async {
    _stories = [story]; // For testing, assume this is the only story
    _activeStory = story;
    _clearStoryState(); // Clear previous state

    // Manually validate assets for this test story
    await _validateStoryAssets(story);

    _currentScene = story.firstScene;
    if (_currentScene != null) {
        _sceneHistory.add(_currentScene!.id);
        _segmentText(_currentScene!.narrationText);
        // Mark its image as loaded for test consistency
        final validatedImage = _validatedImages[_currentScene!.id];
        if (validatedImage != null) {
          _imageCache[_currentScene!.id] = true;
          _validatedImages[_currentScene!.id] = validatedImage.copyWith(isLoaded: true);
        }
    }
    notifyListeners();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Set error message
  void _setError(String message) {
    _errorMessage = message;
    _isLoading = false;
    debugPrint('StoryProvider error: $message');
    notifyListeners();
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Load all available stories
  Future<void> loadStories() async {
    _setLoading(true);
    try {
      // Load stories from assets with access control
      final storyConfigs = [
        {
          'path': 'assets/stories/corals-lost-colors.json',
          'isFree': true, // Free for all users
        },
        {
          'path': 'assets/stories/the-brave-little-turtle.json',
          'isFree': true, // Free for all users
        },
        {
          'path': 'assets/stories/the-magical-forest-adventure.json',
          'isFree': false, // Premium content
        },
      ];

      final loadedStories = <Story>[];

      for (final config in storyConfigs) {
        try {
          final story = await Story.fromAsset(config['path'] as String);
          // Update the story's locked status based on configuration
          final updatedStory = Story(
            id: story.id,
            title: story.title,
            targetAge: story.targetAge,
            moralTheme: story.moralTheme,
            coverImagePath: story.coverImagePath,
            isLocked: !(config['isFree'] as bool), // Lock if not free
            characters: story.characters,
            scenes: story.scenes,
          );
          loadedStories.add(updatedStory);
        } catch (e) {
          debugPrint('Failed to load story from ${config['path']}: $e');
          // Continue loading other stories even if one fails
        }
      }

      _stories = loadedStories;
      _setLoading(false);

      debugPrint('Loaded ${_stories.length} stories successfully');
      debugPrint('Free stories: ${_stories.where((s) => !s.isLocked).length}');
      debugPrint('Premium stories: ${_stories.where((s) => s.isLocked).length}');
    } catch (e) {
      _setError('Failed to load stories: $e');
    }
  }

  /// Get story by ID
  Story? getStoryById(String id) {
    try {
      return _stories.firstWhere((story) => story.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Clear the active story
  void clearActiveStory() {
    _activeStory = null;
    _clearStoryState();
    notifyListeners();
  }

  /// Check if can go to next scene
  bool canGoToNextScene() {
    if (_currentScene == null) return false;

    // Can go to next scene if:
    // 1. Scene has choices (user needs to make a choice)
    // 2. Scene has defaultNextSceneId (auto-advance)
    // 3. There's a next scene in the story array
    return _currentScene!.choices.isNotEmpty ||
           _currentScene!.defaultNextSceneId != null ||
           _getNextSceneInArray() != null;
  }

  /// Check if can go to previous scene
  bool canGoToPreviousScene() {
    return _sceneHistory.length > 1;
  }

  /// Go to previous scene
  bool goToPreviousScene() {
    if (!canGoToPreviousScene()) return false;

    // Remove current scene from history
    _sceneHistory.removeLast();

    // Get previous scene ID
    final previousSceneId = _sceneHistory.last;

    // Navigate to previous scene (this will add it back to history)
    navigateToScene(previousSceneId);
    return true;
  }

  /// Check if story is completed
  bool isStoryCompleted() {
    return _currentScene?.isEndingScene == true;
  }

  /// Get scene progress percentage (0.0 to 1.0)
  double getSceneProgressPercentage() {
    if (_activeStory == null || _activeStory!.scenes.isEmpty) return 0.0;

    final currentIndex = _activeStory!.scenes.indexWhere(
      (scene) => scene.id == _currentScene?.id
    );

    if (currentIndex == -1) return 0.0;

    return (currentIndex + 1) / _activeStory!.scenes.length;
  }

  /// Get available choices for current scene
  List<StoryChoice> getAvailableChoices() {
    if (_currentScene == null) return [];
    return _currentScene!.choices;
  }

  /// Get choice history
  List<String> getChoiceHistory() {
    return _choiceHistory.map((choice) => choice['choiceText'] as String).toList();
  }

  /// Save current state
  Map<String, dynamic> saveState() {
    return {
      'activeStoryId': _activeStory?.id,
      'currentSceneId': _currentScene?.id,
      'sceneHistory': List<String>.from(_sceneHistory),
      'choiceHistory': List<Map<String, dynamic>>.from(_choiceHistory),
      'currentSegmentIndex': _currentSegmentIndex,
      'isNarrationComplete': _isNarrationComplete,
      'showChoices': _showChoices,
    };
  }

  /// Restore state from saved data
  Future<void> restoreState(Map<String, dynamic> state) async {
    try {
      final storyId = state['activeStoryId'] as String?;
      if (storyId != null) {
        final story = getStoryById(storyId);
        if (story != null) {
          _activeStory = story;

          final sceneId = state['currentSceneId'] as String?;
          if (sceneId != null) {
            _currentScene = story.getSceneById(sceneId);
          }

          _sceneHistory.clear();
          _sceneHistory.addAll(List<String>.from(state['sceneHistory'] ?? []));
          _choiceHistory.clear();
          _choiceHistory.addAll(List<Map<String, dynamic>>.from(state['choiceHistory'] ?? []));
          _currentSegmentIndex = state['currentSegmentIndex'] ?? 0;
          _isNarrationComplete = state['isNarrationComplete'] ?? false;
          _showChoices = state['showChoices'] ?? false;

          if (_currentScene != null) {
            _segmentText(_currentScene!.narrationText);
          }

          notifyListeners();
        }
      }
    } catch (e) {
      _setError('Failed to restore state: $e');
    }
  }

  /// Set error message (public method for tests)
  void setError(String message) {
    _setError(message);
  }

  /// Get next scene in story array
  StoryScene? _getNextSceneInArray() {
    if (_activeStory == null || _currentScene == null) return null;

    final currentIndex = _activeStory!.scenes.indexWhere(
      (scene) => scene.id == _currentScene!.id
    );

    if (currentIndex == -1 || currentIndex >= _activeStory!.scenes.length - 1) {
      return null;
    }

    return _activeStory!.scenes[currentIndex + 1];
  }

  /// Set active story directly (for backward compatibility with tests)
  void setActiveStory(Story story) {
    _activeStory = story;
    _clearStoryState();

    if (story.scenes.isNotEmpty) {
      _currentScene = story.scenes.first;
      _sceneHistory.add(story.scenes.first.id);
      _segmentText(story.scenes.first.narrationText);
    }

    notifyListeners();
  }

  /// Navigate to scene by ID (returns bool for backward compatibility)
  bool navigateToScene(String sceneId) {
    if (_activeStory == null) return false;

    final scene = _activeStory!.getSceneById(sceneId);
    if (scene == null) return false;

    _currentScene = scene;
    _sceneHistory.add(sceneId);
    _segmentText(scene.narrationText);
    notifyListeners();

    return true;
  }

  /// Make choice by choice ID (returns bool for backward compatibility)
  bool makeChoice(String choiceId) {
    if (_currentScene == null) return false;

    final choice = _currentScene!.choices.firstWhere(
      (c) => c.text == choiceId, // StoryChoice uses text as identifier
      orElse: () => StoryChoice(text: '', nextSceneId: ''),
    );

    if (choice.text.isEmpty) return false;

    // Record the choice
    _choiceHistory.add({
      'sceneId': _currentScene!.id,
      'choiceText': choice.text,
      'nextSceneId': choice.nextSceneId,
    });

    return navigateToScene(choice.nextSceneId);
  }
}