import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/story_model.dart'; // Assuming story_model.dart is in the parent directory of providers
import '../services/text_segmenter.dart'; // Assuming text_segmenter.dart is in the parent directory of providers

/// Class to hold validated image information
class ValidatedImageInfo {
  final String sceneId;
  final String validPath;
  final bool isLoaded;

  ValidatedImageInfo({
    required this.sceneId,
    required this.validPath,
    this.isLoaded = false,
  });

  ValidatedImageInfo copyWith({bool? isLoaded}) {
    return ValidatedImageInfo(
      sceneId: sceneId,
      validPath: validPath,
      isLoaded: isLoaded ?? this.isLoaded,
    );
  }
}

/// Provider to manage story state
class StoryProvider extends ChangeNotifier {
  /// List of all available stories
  List<Story> _stories = [];

  /// The currently active story
  Story? _activeStory;

  /// The current scene in the active story
  StoryScene? _currentScene;

  /// History of scenes visited in the current story
  final List<String> _sceneHistory = [];

  /// History of choices made in the current story
  final List<Map<String, dynamic>> _choiceHistory = [];

  /// Loading state
  bool _isLoading = false;

  /// Error message (if any)
  String? _errorMessage;

  /// Whether the narration is complete
  bool _isNarrationComplete = false;

  /// Whether the choices should be shown
  bool _showChoices = false;

  /// The current text segments for the current scene
  List<String> _currentTextSegments = [];

  /// The index of the current text segment
  int _currentSegmentIndex = 0;

  /// Whether auto-proceed is enabled
  bool _autoProceedEnabled = true;

  /// Timer for auto-proceeding to the next scene
  Timer? _autoProceedTimer;

  /// Map of scene IDs to their validated image information
  final Map<String, ValidatedImageInfo> _validatedImages = {};

  /// **NEW**: Map of story IDs to their validated explicit cover image paths
  final Map<String, String> _validatedStoryCoverImages = {};

  /// Set of currently loading images to prevent duplicate requests
  final Set<String> _loadingImages = {};

  /// Cache of loaded images by scene ID
  final Map<String, bool> _imageCache = {};

  /// Getters
  List<Story> get stories => _stories;
  Story? get activeStory => _activeStory;
  StoryScene? get currentScene => _currentScene;
  List<String> get sceneHistory => List.unmodifiable(_sceneHistory);
  List<Map<String, dynamic>> get choiceHistory => List.unmodifiable(_choiceHistory);
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isNarrationComplete => _isNarrationComplete;
  bool get showChoices => _showChoices;
  List<String> get currentTextSegments => _currentTextSegments;
  int get currentSegmentIndex => _currentSegmentIndex;
  String get currentTextSegment =>
      _currentSegmentIndex >= 0 && _currentSegmentIndex < _currentTextSegments.length
          ? _currentTextSegments[_currentSegmentIndex]
          : '';
  bool get autoProceedEnabled => _autoProceedEnabled;

  /// Get validated image info for a scene
  ValidatedImageInfo? getValidatedImageInfo(String sceneId) => _validatedImages[sceneId];

  /// **NEW**: Get validated explicit cover image path for a story
  String? getValidatedStoryCoverPath(String storyId) => _validatedStoryCoverImages[storyId];

  /// Check if an image is currently loading
  bool isImageLoading(String imagePath) => _loadingImages.contains(imagePath);

  /// Check if an image is cached
  bool isImageCached(String sceneId) => _imageCache[sceneId] == true;

  /// Pre-validate story assets and create image mappings
  Future<void> _validateStoryAssets(Story story) async {
    debugPrint('Validating assets for story: ${story.id}');

    // Get the asset manifest to check available files
    final manifestContent = await rootBundle.loadString('AssetManifest.json');
    final Map<String, dynamic> manifestMap = Map.from(
      json.decode(manifestContent) as Map<dynamic, dynamic>,
    );

    final availableAssets = manifestMap.keys.toSet();

    // **ENHANCEMENT**: Validate explicit cover image for the story
    if (story.coverImagePath != null && story.coverImagePath!.isNotEmpty) {
      final validCoverPath = await _findValidImagePathFromManifest(
        story.coverImagePath!,
        availableAssets,
      );
      if (validCoverPath != null) {
        _validatedStoryCoverImages[story.id] = validCoverPath;
        debugPrint('Validated explicit cover for story ${story.id}: $validCoverPath');
      } else {
        debugPrint('No valid explicit cover found for story ${story.id}: ${story.coverImagePath}');
      }
    }

    // Validate each scene's image
    for (final scene in story.scenes) {
      final validPath = await _findValidImagePathFromManifest(
        scene.imagePath,
        availableAssets,
      );

      if (validPath != null) {
        _validatedImages[scene.id] = ValidatedImageInfo(
          sceneId: scene.id,
          validPath: validPath,
        );
        debugPrint('Validated image for scene ${scene.id}: $validPath');
      } else {
        debugPrint('No valid image found for scene ${scene.id}: ${scene.imagePath}');
      }
    }
  }

  /// Find a valid image path from the asset manifest
  Future<String?> _findValidImagePathFromManifest(
    String assetPathFromJson, // e.g., "assets/story1/cover" or "assets/story1/cover.jpeg"
    Set<String> availableAssets, // Exact paths from AssetManifest.json (case-sensitive)
  ) async {
    // 1. Direct check: If the path from JSON exists as-is in the manifest.
    if (availableAssets.contains(assetPathFromJson)) {
      return assetPathFromJson;
    }

    String baseName = assetPathFromJson;
    String originalExtension = "";

    int dotIndex = assetPathFromJson.lastIndexOf('.');
    // Ensure the dot is for an extension, not part of a directory name.
    int lastSlashIndex = assetPathFromJson.lastIndexOf('/');
    if (dotIndex > lastSlashIndex) {
      baseName = assetPathFromJson.substring(0, dotIndex); // Part before the extension
      originalExtension = assetPathFromJson.substring(dotIndex); // e.g., ".jpeg", ".JPEG"
    }
    // If no dot, baseName remains assetPathFromJson, originalExtension remains "".

    // 2. Try a list of common extensions and their typical casings with the derived baseName.
    //    This is the most crucial part for handling .jpeg/.JPEG etc.
    const List<String> commonExtensionsToTry = [
      '.png', '.PNG',
      '.jpg', '.JPG',
      '.jpeg', '.JPEG', // Explicitly trying both common casings for .jpeg
      '.webp', '.WEBP'
    ];

    for (final String ext in commonExtensionsToTry) {
      String testPath = baseName + ext;
      if (availableAssets.contains(testPath)) {
        return testPath; // Found a match with one of the common extensions/casings
      }
    }

    // 3. If the original path from JSON had an extension (originalExtension is not empty),
    //    and it wasn't found in step 2 (e.g., it was ".Jpg" which is not in commonExtensionsToTry),
    //    try its direct lowercase and uppercase versions.
    //    This is a fallback for less common casings not in the list above.
    if (originalExtension.isNotEmpty) {
      String pathWithLowerExt = baseName + originalExtension.toLowerCase();
      if (availableAssets.contains(pathWithLowerExt)) {
        return pathWithLowerExt;
      }
      String pathWithUpperExt = baseName + originalExtension.toUpperCase();
      if (availableAssets.contains(pathWithUpperExt)) {
        return pathWithUpperExt;
      }
    }
    
    // 4. If assetPathFromJson was originally extensionless (i.e. baseName == assetPathFromJson)
    //    and it was not found by appending extensions, this means we couldn't find a match.
    //    The case where an extensionless path in the manifest should match an extensionless path
    //    from JSON is covered by step 1.

    debugPrint('No valid image found in manifest for base: $assetPathFromJson (derived baseName: $baseName)');
    return null; // No valid path found
  }

  /// Load all stories from the assets directory
  Future<void> loadAllStories() async {
    _setLoading(true);
    _stories = [];

    try {
      // Get list of story JSON files
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = Map.from(
        json.decode(manifestContent) as Map<dynamic, dynamic>,
      );

      final storyPaths = manifestMap.keys
          .where((String key) =>
              (key.startsWith('assets/images/') || key.startsWith('assets/stories/')) && // Modified to include stories in images folder for flexibility if any
              key.endsWith('.json') &&
              !key.contains('/ui_elements/')) // Assuming ui_elements will not contain story JSONs
          .toList();

      // Load each story and validate its assets
      for (final path in storyPaths) {
        try {
          final story = await Story.fromAsset(path); // Assumes Story.fromAsset is in story_model.dart
          await _validateStoryAssets(story);
          _stories.add(story);
        } catch (e) {
          debugPrint('Error loading story from $path: $e');
        }
      }

      _setLoading(false);
    } catch (e) {
      _setError('Error loading stories: $e');
    }
  }

  /// Set the active story by ID and load the first scene
  Future<void> setActiveStory(String storyId, {String? initialSceneId}) async {
    _setLoading(true);
    _clearStoryState(); // _clearStoryState will also clear new _validatedStoryCoverImages map

    try {
      // Find the story by ID
      final story = _stories.firstWhere(
        (story) => story.id == storyId,
        orElse: () => throw Exception('Story not found: $storyId'),
      );

      _activeStory = story;

      // **IMPORTANT**: Re-validate assets for the active story to populate _validatedImages
      // This is crucial if _clearStoryState clears _validatedImages and _validatedStoryCoverImages
      // However, _validatedStoryCoverImages is populated during loadAllStories.
      // If setActiveStory is called for a story already in _stories, its cover is already validated.
      // _validatedImages for scenes are specific to an active story session.
      await _validateStoryAssets(story); // Ensures scene images are validated for this active story

      // Load the initial scene
      final sceneId = initialSceneId ?? story.firstScene.id;
      await navigateToScene(sceneId);

      _setLoading(false);
    } catch (e) {
      _setError('Error setting active story: $e');
    }
  }

  /// Navigate to a specific scene
  Future<void> navigateToScene(String sceneId) async {
    if (_activeStory == null) {
      _setError('No active story');
      return;
    }

    _setLoading(true);
    _isNarrationComplete = false;
    _showChoices = false;

    // Cancel any pending auto-proceed
    _autoProceedTimer?.cancel();

    try {
      // Find the scene by ID
      final scene = _activeStory!.getSceneById(sceneId);

      if (scene == null) {
        throw Exception('Scene not found: $sceneId');
      }

      _currentScene = scene;
      _sceneHistory.add(sceneId);

      // Segment the narration text
      _segmentText(scene.narrationText); // Assumes _segmentText uses TextSegmenter

      // Mark image as loaded for this scene if it's validated
      final validatedImage = _validatedImages[scene.id];
      if (validatedImage != null) {
        _imageCache[scene.id] = true;
        _validatedImages[scene.id] = validatedImage.copyWith(isLoaded: true);
      }

      _setLoading(false);

      // Start narrating the first segment immediately
      if (_currentTextSegments.isNotEmpty) {
        _startNarration();
      } else {
        // If no segments, handle auto-advance immediately
        _handleEmptySceneNavigation(scene);
      }
    } catch (e) {
      _setError('Error navigating to scene: $e');
    }
  }

  /// Determine the next scene ID for auto-advance
  String? _determineNextSceneId(StoryScene currentScene) {
    // If there's an explicit defaultNextSceneId, use it
    if (currentScene.defaultNextSceneId != null) {
      debugPrint('Using explicit defaultNextSceneId: ${currentScene.defaultNextSceneId}');
      return currentScene.defaultNextSceneId;
    }

    // If no explicit next scene and not an ending scene,
    // auto-advance to the next scene in the JSON array sequence
    if (!currentScene.isEndingScene && _activeStory != null) {
      final nextScene = _activeStory!.getNextSceneByIndex(currentScene.id);
      if (nextScene != null) {
        debugPrint('Auto-advancing to next scene in sequence: ${nextScene.id}');
        return nextScene.id;
      } else {
        debugPrint('No next scene found in sequence for: ${currentScene.id}');
      }
    }

    return null;
  }

  /// Start narrating the current segment
  void _startNarration() {
    if (_currentTextSegments.isEmpty || _currentSegmentIndex >= _currentTextSegments.length) {
      debugPrint('Cannot start narration: No segments or invalid index');
      return;
    }

    final text = _currentTextSegments[_currentSegmentIndex];
    debugPrint('Starting narration for segment $_currentSegmentIndex: "$text"');

    // Simulate TTS completion after a delay
    // In a real app, this would be triggered by actual TTS completion
    Future.delayed(const Duration(seconds: 2), () {
      _onNarrationComplete();
    });
  }

  /// Handle narration completion
  void _onNarrationComplete() {
    debugPrint('Narration complete for segment $_currentSegmentIndex in scene: ${_currentScene?.id}');

    if (_currentScene == null) {
      return;
    }

    _isNarrationComplete = true;

    if (_currentSegmentIndex < _currentTextSegments.length - 1) {
      // More segments in current scene - advance to next segment
      debugPrint('Advancing to next segment: ${_currentSegmentIndex + 1}');
      _scheduleAutoAction(() => _advanceToNextSegment());
    } else {
      // Last segment of scene - handle scene completion
      _handleSceneCompletion();
    }

    notifyListeners();
  }

  /// Handle completion of a scene (last segment)
  void _handleSceneCompletion() {
    if (_currentScene == null) return;

    if (_currentScene!.isChoicePoint) {
      debugPrint('Showing choices for scene: ${_currentScene!.id}');
      _showChoices = true;
    } else if (!_currentScene!.isEndingScene) {
      // Auto-advance to next scene
      final nextSceneId = _determineNextSceneId(_currentScene!);
      if (nextSceneId != null) {
        debugPrint('Auto-advancing to next scene: $nextSceneId');
        _scheduleAutoProceed(nextSceneId);
      } else {
        debugPrint('No next scene found, story may be complete');
      }
    } else {
      debugPrint('Reached ending scene: ${_currentScene!.id}');
    }
  }

  /// Handle navigation for scenes with no text segments
  void _handleEmptySceneNavigation(StoryScene scene) {
    debugPrint('Handling empty scene navigation for: ${scene.id}');

    if (scene.isChoicePoint) {
      _showChoices = true;
      notifyListeners();
    } else if (!scene.isEndingScene) {
      final nextSceneId = _determineNextSceneId(scene);
      if (nextSceneId != null) {
        debugPrint('Empty scene auto-advancing to: $nextSceneId');
        _scheduleAutoProceed(nextSceneId);
      }
    }
  }

  /// Advance to the next text segment within the same scene
  void _advanceToNextSegment() {
    if (_currentSegmentIndex < _currentTextSegments.length - 1) {
      _currentSegmentIndex++;
      _isNarrationComplete = false;
      debugPrint('Advanced to segment $_currentSegmentIndex in scene: ${_currentScene?.id}');
      notifyListeners();
      _startNarration();
    }
  }

  /// Segment the narration text into smaller chunks
  void _segmentText(String text) {
    _currentTextSegments = TextSegmenter.splitIntoSegments(text);
    _currentSegmentIndex = 0;
  }

  /// Handle a choice selection
  Future<void> makeChoice(String choiceText, String nextSceneId) async {
    if (_currentScene == null) {
      _setError('No current scene');
      return;
    }

    // Record the choice
    _choiceHistory.add({
      'sceneId': _currentScene!.id,
      'choiceText': choiceText,
      'nextSceneId': nextSceneId,
    });

    // Navigate to the next scene
    await navigateToScene(nextSceneId);
  }

  /// Go back to the previous choice point
  Future<void> goBackToPreviousChoice() async {
    if (_choiceHistory.isEmpty) {
      // If no choices made, go back to the first scene
      if (_activeStory != null) {
        await navigateToScene(_activeStory!.firstScene.id);
      }
      return;
    }

    // Remove the last choice
    final lastChoice = _choiceHistory.removeLast();

    // Navigate to the scene where the choice was made
    await navigateToScene(lastChoice['sceneId'] as String);
  }

  /// Restart the current story
  Future<void> restartStory() async {
    if (_activeStory == null) {
      _setError('No active story');
      return;
    }

    _clearStoryState();
    await setActiveStory(_activeStory!.id); // Changed to call setActiveStory to ensure re-validation
  }

  /// Mark narration as complete (called by TTS when done)
  void completeNarration() {
    _isNarrationComplete = true;

    // If this is a choice point, show choices
    if (_currentScene?.isChoicePoint ?? false) {
      _showChoices = true;
    } else if (_autoProceedEnabled &&
               _currentScene != null &&
               !_currentScene!.isEndingScene &&
               _currentSegmentIndex == _currentTextSegments.length - 1) {
      // If auto-proceed is enabled and this is the last segment of a non-choice, non-ending scene,
      // automatically proceed to the next scene after a delay
      final nextSceneId = _determineNextSceneId(_currentScene!);
      if (nextSceneId != null) {
        _scheduleAutoProceed(nextSceneId);
      }
    }

    notifyListeners();
  }

  /// Schedule auto-proceeding to the next scene
  void _scheduleAutoProceed(String nextSceneId) {
    // Cancel any existing timer
    _autoProceedTimer?.cancel();

    // Schedule a new timer
    _autoProceedTimer = Timer(const Duration(milliseconds: 800), () {
      if (_currentScene != null &&
          !_currentScene!.isChoicePoint &&
          !_currentScene!.isEndingScene) {
        navigateToScene(nextSceneId);
      }
    });
  }

  /// Schedule auto-proceeding with a custom action
  void _scheduleAutoAction(VoidCallback action) {
    // Cancel any existing timer
    _autoProceedTimer?.cancel();

    // Schedule a new timer
    _autoProceedTimer = Timer(const Duration(milliseconds: 800), action);
  }

  /// Enable or disable auto-proceeding
  void setAutoProceedEnabled(bool enabled) {
    _autoProceedEnabled = enabled;

    if (!enabled) {
      // Cancel any pending auto-proceed
      _autoProceedTimer?.cancel();
    } else if (_isNarrationComplete &&
               _currentScene != null &&
               !_currentScene!.isChoicePoint &&
               !_currentScene!.isEndingScene &&
               _currentSegmentIndex == _currentTextSegments.length - 1) {
      // If narration is already complete and we're enabling auto-proceed,
      // schedule an auto-proceed
      final nextSceneId = _determineNextSceneId(_currentScene!);
      if (nextSceneId != null) {
        _scheduleAutoProceed(nextSceneId);
      }
    }

    notifyListeners();
  }

  /// Navigate to the next text segment
  bool navigateToNextSegment() {
    if (_currentSegmentIndex < _currentTextSegments.length - 1) {
      _currentSegmentIndex++;
      _isNarrationComplete = false;
      notifyListeners();
      _startNarration(); // Start narration for the new segment
      return true;
    } else if (_currentScene != null &&
               !_currentScene!.isChoicePoint &&
               !_currentScene!.isEndingScene) {
      // If we're at the last segment, determine the next scene
      final nextSceneId = _determineNextSceneId(_currentScene!);
      if (nextSceneId != null) {
        navigateToScene(nextSceneId);
        return true;
      }
    }
    return false;
  }

  /// Navigate to the previous text segment
  bool navigateToPreviousSegment() {
    if (_currentSegmentIndex > 0) {
      _currentSegmentIndex--;
      _isNarrationComplete = false;
      notifyListeners();
      _startNarration(); // Start narration for the new segment
      return true;
    } else if (_sceneHistory.length > 1) {
      // If we're at the first segment and there's a previous scene,
      // navigate to it by removing current and going to new last
      _sceneHistory.removeLast(); // Remove current scene from history
      final previousSceneId = _sceneHistory.removeLast(); // Get and remove the actual previous scene
                                                        // navigateToScene will re-add it.
      navigateToScene(previousSceneId);
      return true;
    }
    return false;
  }


  /// Toggle showing choices
  void setShowChoices(bool show) {
    _showChoices = show;
    notifyListeners();
  }

  /// Mark an image as loading to prevent duplicate requests
  void markImageAsLoading(String imagePath) {
    _loadingImages.add(imagePath);
    notifyListeners();
  }

  /// Mark an image as finished loading
  void markImageAsLoaded(String imagePath, String sceneId) {
    _loadingImages.remove(imagePath);
    _imageCache[sceneId] = true;

    // Update validated image info if it exists
    final validatedImage = _validatedImages[sceneId];
    if (validatedImage != null) {
      _validatedImages[sceneId] = validatedImage.copyWith(isLoaded: true);
    }

    notifyListeners();
  }

  /// Clear the current story state
  void _clearStoryState() {
    // _activeStory = null; // Should not clear active story if we intend to restart it.
                         // Let setActiveStory manage this.
    _currentScene = null;
    _sceneHistory.clear();
    _choiceHistory.clear();
    _isNarrationComplete = false;
    _showChoices = false;
    _currentTextSegments = [];
    _currentSegmentIndex = 0;

    // Cancel any pending auto-proceed
    _autoProceedTimer?.cancel();

    // Clear image loading state for the current story
    // Note: _validatedStoryCoverImages is related to all stories, not just active,
    // so it should be cleared when all stories are reloaded, not per active story.
    // However, _validatedImages IS per active story session.
    _loadingImages.clear();
    _imageCache.clear();
    _validatedImages.clear(); // Clears scene-specific validated images
  }

  /// Clear all loaded stories and their validated cover images.
  /// Typically called before a fresh loadAllStories.
  void clearAllStoryData() {
      _stories = [];
      _validatedStoryCoverImages.clear();
      _activeStory = null;
      _clearStoryState(); // Clear active story specific states too
      notifyListeners();
  }


  @override
  void dispose() {
    _autoProceedTimer?.cancel();
    super.dispose();
  }

  /// Set the active story directly for testing purposes
  /// This method should only be used in tests
  Future<void> setActiveStoryForTesting(Story story) async {
    _stories = [story]; // For testing, assume this is the only story
    _activeStory = story;
    _clearStoryState(); // Clear previous state

    // Manually validate assets for this test story
    await _validateStoryAssets(story);

    _currentScene = story.firstScene;
    if (_currentScene != null) {
        _sceneHistory.add(_currentScene!.id);
        _segmentText(_currentScene!.narrationText);
        // Mark its image as loaded for test consistency
        final validatedImage = _validatedImages[_currentScene!.id];
        if (validatedImage != null) {
          _imageCache[_currentScene!.id] = true;
          _validatedImages[_currentScene!.id] = validatedImage.copyWith(isLoaded: true);
        }
    }
    notifyListeners();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Set error message
  void _setError(String message) {
    _errorMessage = message;
    _isLoading = false;
    debugPrint('StoryProvider error: $message');
    notifyListeners();
  }
}