import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/story_model.dart';
import '../services/text_segmenter.dart';

/// Class to hold validated image information
class ValidatedImageInfo {
  final String sceneId;
  final String validPath;
  final bool isLoaded;

  ValidatedImageInfo({
    required this.sceneId,
    required this.validPath,
    this.isLoaded = false,
  });

  ValidatedImageInfo copyWith({bool? isLoaded}) {
    return ValidatedImageInfo(
      sceneId: sceneId,
      validPath: validPath,
      isLoaded: isLoaded ?? this.isLoaded,
    );
  }
}

/// Provider to manage story state
class StoryProvider extends ChangeNotifier {
  /// List of all available stories
  List<Story> _stories = [];

  /// The currently active story
  Story? _activeStory;

  /// The current scene in the active story
  StoryScene? _currentScene;

  /// History of scenes visited in the current story
  final List<String> _sceneHistory = [];

  /// History of choices made in the current story
  final List<Map<String, dynamic>> _choiceHistory = [];

  /// Loading state
  bool _isLoading = false;

  /// Error message (if any)
  String? _errorMessage;

  /// Whether the narration is complete
  bool _isNarrationComplete = false;

  /// Whether the choices should be shown
  bool _showChoices = false;

  /// The current text segments for the current scene
  List<String> _currentTextSegments = [];

  /// The index of the current text segment
  int _currentSegmentIndex = 0;

  /// Whether auto-proceed is enabled
  bool _autoProceedEnabled = true;

  /// Timer for auto-proceeding to the next scene
  Timer? _autoProceedTimer;

  /// Map of scene IDs to their validated image information
  final Map<String, ValidatedImageInfo> _validatedImages = {};

  /// Set of currently loading images to prevent duplicate requests
  final Set<String> _loadingImages = {};

  /// Cache of loaded images by scene ID
  final Map<String, bool> _imageCache = {};

  /// Getters
  List<Story> get stories => _stories;
  Story? get activeStory => _activeStory;
  StoryScene? get currentScene => _currentScene;
  List<String> get sceneHistory => List.unmodifiable(_sceneHistory);
  List<Map<String, dynamic>> get choiceHistory => List.unmodifiable(_choiceHistory);
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isNarrationComplete => _isNarrationComplete;
  bool get showChoices => _showChoices;
  List<String> get currentTextSegments => _currentTextSegments;
  int get currentSegmentIndex => _currentSegmentIndex;
  String get currentTextSegment =>
      _currentSegmentIndex >= 0 && _currentSegmentIndex < _currentTextSegments.length
          ? _currentTextSegments[_currentSegmentIndex]
          : '';
  bool get autoProceedEnabled => _autoProceedEnabled;

  /// Get validated image info for a scene
  ValidatedImageInfo? getValidatedImageInfo(String sceneId) => _validatedImages[sceneId];

  /// Check if an image is currently loading
  bool isImageLoading(String imagePath) => _loadingImages.contains(imagePath);

  /// Check if an image is cached
  bool isImageCached(String sceneId) => _imageCache[sceneId] == true;

  /// Pre-validate story assets and create image mappings
  Future<void> _validateStoryAssets(Story story) async {
    debugPrint('Validating assets for story: ${story.id}');

    // Get the asset manifest to check available files
    final manifestContent = await rootBundle.loadString('AssetManifest.json');
    final Map<String, dynamic> manifestMap = Map.from(
      json.decode(manifestContent) as Map<dynamic, dynamic>,
    );

    final availableAssets = manifestMap.keys.toSet();

    // Validate each scene's image
    for (final scene in story.scenes) {
      final validPath = await _findValidImagePathFromManifest(
        scene.imagePath,
        availableAssets,
      );

      if (validPath != null) {
        _validatedImages[scene.id] = ValidatedImageInfo(
          sceneId: scene.id,
          validPath: validPath,
        );
        debugPrint('Validated image for scene ${scene.id}: $validPath');
      } else {
        debugPrint('No valid image found for scene ${scene.id}: ${scene.imagePath}');
      }
    }
  }

  /// Find a valid image path from the asset manifest
  Future<String?> _findValidImagePathFromManifest(
    String basePath,
    Set<String> availableAssets,
  ) async {
    // List of extensions to try
    final extensions = ['', '.png', '.jpg', '.jpeg', '.webp'];

    // First, check if the path already has a valid extension
    final String basePathLower = basePath.toLowerCase();
    if (basePathLower.endsWith('.png') ||
        basePathLower.endsWith('.jpg') ||
        basePathLower.endsWith('.jpeg') ||
        basePathLower.endsWith('.webp')) {
      if (availableAssets.contains(basePath)) {
        return basePath;
      }
    }

    // Try each extension
    for (final extension in extensions) {
      final path = extension.isEmpty ? basePath : '$basePath$extension';
      if (availableAssets.contains(path)) {
        return path;
      }
    }

    return null;
  }

  /// Load all stories from the assets directory
  Future<void> loadAllStories() async {
    _setLoading(true);
    _stories = [];

    try {
      // Get list of story JSON files
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = Map.from(
        json.decode(manifestContent) as Map<dynamic, dynamic>,
      );

      final storyPaths = manifestMap.keys
          .where((String key) =>
              (key.startsWith('assets/images/') || key.startsWith('assets/stories/')) &&
              key.endsWith('.json') &&
              !key.contains('/ui_elements/'))
          .toList();

      // Load each story and validate its assets
      for (final path in storyPaths) {
        try {
          final story = await Story.fromAsset(path);
          await _validateStoryAssets(story);
          _stories.add(story);
        } catch (e) {
          debugPrint('Error loading story from $path: $e');
        }
      }

      _setLoading(false);
    } catch (e) {
      _setError('Error loading stories: $e');
    }
  }

  /// Set the active story by ID and load the first scene
  Future<void> setActiveStory(String storyId, {String? initialSceneId}) async {
    _setLoading(true);
    _clearStoryState();

    try {
      // Find the story by ID
      final story = _stories.firstWhere(
        (story) => story.id == storyId,
        orElse: () => throw Exception('Story not found: $storyId'),
      );

      _activeStory = story;

      // Load the initial scene
      final sceneId = initialSceneId ?? story.firstScene.id;
      await navigateToScene(sceneId);

      _setLoading(false);
    } catch (e) {
      _setError('Error setting active story: $e');
    }
  }

  /// Navigate to a specific scene
  Future<void> navigateToScene(String sceneId) async {
    if (_activeStory == null) {
      _setError('No active story');
      return;
    }

    _setLoading(true);
    _isNarrationComplete = false;
    _showChoices = false;

    // Cancel any pending auto-proceed
    _autoProceedTimer?.cancel();

    try {
      // Find the scene by ID
      final scene = _activeStory!.getSceneById(sceneId);

      if (scene == null) {
        throw Exception('Scene not found: $sceneId');
      }

      _currentScene = scene;
      _sceneHistory.add(sceneId);

      // Segment the narration text
      _segmentText(scene.narrationText);

      // Mark image as loaded for this scene if it's validated
      final validatedImage = _validatedImages[scene.id];
      if (validatedImage != null) {
        _imageCache[scene.id] = true;
        _validatedImages[scene.id] = validatedImage.copyWith(isLoaded: true);
      }

      _setLoading(false);

      // Start narrating the first segment immediately
      if (_currentTextSegments.isNotEmpty) {
        _startNarration();
      } else {
        // If no segments, handle auto-advance immediately
        _handleEmptySceneNavigation(scene);
      }
    } catch (e) {
      _setError('Error navigating to scene: $e');
    }
  }

  /// Determine the next scene ID for auto-advance
  String? _determineNextSceneId(StoryScene currentScene) {
    // If there's an explicit defaultNextSceneId, use it
    if (currentScene.defaultNextSceneId != null) {
      debugPrint('Using explicit defaultNextSceneId: ${currentScene.defaultNextSceneId}');
      return currentScene.defaultNextSceneId;
    }

    // If no explicit next scene and not an ending scene,
    // auto-advance to the next scene in the JSON array sequence
    if (!currentScene.isEndingScene && _activeStory != null) {
      final nextScene = _activeStory!.getNextSceneByIndex(currentScene.id);
      if (nextScene != null) {
        debugPrint('Auto-advancing to next scene in sequence: ${nextScene.id}');
        return nextScene.id;
      } else {
        debugPrint('No next scene found in sequence for: ${currentScene.id}');
      }
    }

    return null;
  }

  /// Start narrating the current segment
  void _startNarration() {
    if (_currentTextSegments.isEmpty || _currentSegmentIndex >= _currentTextSegments.length) {
      debugPrint('Cannot start narration: No segments or invalid index');
      return;
    }

    final text = _currentTextSegments[_currentSegmentIndex];
    debugPrint('Starting narration for segment $_currentSegmentIndex: "$text"');

    // Simulate TTS completion after a delay
    // In a real app, this would be triggered by actual TTS completion
    Future.delayed(const Duration(seconds: 2), () {
      _onNarrationComplete();
    });
  }

  /// Handle narration completion
  void _onNarrationComplete() {
    debugPrint('Narration complete for segment $_currentSegmentIndex in scene: ${_currentScene?.id}');

    if (_currentScene == null) {
      return;
    }

    _isNarrationComplete = true;

    if (_currentSegmentIndex < _currentTextSegments.length - 1) {
      // More segments in current scene - advance to next segment
      debugPrint('Advancing to next segment: ${_currentSegmentIndex + 1}');
      _scheduleAutoAction(() => _advanceToNextSegment());
    } else {
      // Last segment of scene - handle scene completion
      _handleSceneCompletion();
    }

    notifyListeners();
  }

  /// Handle completion of a scene (last segment)
  void _handleSceneCompletion() {
    if (_currentScene == null) return;

    if (_currentScene!.isChoicePoint) {
      debugPrint('Showing choices for scene: ${_currentScene!.id}');
      _showChoices = true;
    } else if (!_currentScene!.isEndingScene) {
      // Auto-advance to next scene
      final nextSceneId = _determineNextSceneId(_currentScene!);
      if (nextSceneId != null) {
        debugPrint('Auto-advancing to next scene: $nextSceneId');
        _scheduleAutoProceed(nextSceneId);
      } else {
        debugPrint('No next scene found, story may be complete');
      }
    } else {
      debugPrint('Reached ending scene: ${_currentScene!.id}');
    }
  }

  /// Handle navigation for scenes with no text segments
  void _handleEmptySceneNavigation(StoryScene scene) {
    debugPrint('Handling empty scene navigation for: ${scene.id}');

    if (scene.isChoicePoint) {
      _showChoices = true;
      notifyListeners();
    } else if (!scene.isEndingScene) {
      final nextSceneId = _determineNextSceneId(scene);
      if (nextSceneId != null) {
        debugPrint('Empty scene auto-advancing to: $nextSceneId');
        _scheduleAutoProceed(nextSceneId);
      }
    }
  }

  /// Advance to the next text segment within the same scene
  void _advanceToNextSegment() {
    if (_currentSegmentIndex < _currentTextSegments.length - 1) {
      _currentSegmentIndex++;
      _isNarrationComplete = false;
      debugPrint('Advanced to segment $_currentSegmentIndex in scene: ${_currentScene?.id}');
      notifyListeners();
      _startNarration();
    }
  }

  /// Segment the narration text into smaller chunks
  void _segmentText(String text) {
    _currentTextSegments = TextSegmenter.splitIntoSegments(text);
    _currentSegmentIndex = 0;
  }

  /// Handle a choice selection
  Future<void> makeChoice(String choiceText, String nextSceneId) async {
    if (_currentScene == null) {
      _setError('No current scene');
      return;
    }

    // Record the choice
    _choiceHistory.add({
      'sceneId': _currentScene!.id,
      'choiceText': choiceText,
      'nextSceneId': nextSceneId,
    });

    // Navigate to the next scene
    await navigateToScene(nextSceneId);
  }

  /// Go back to the previous choice point
  Future<void> goBackToPreviousChoice() async {
    if (_choiceHistory.isEmpty) {
      // If no choices made, go back to the first scene
      if (_activeStory != null) {
        await navigateToScene(_activeStory!.firstScene.id);
      }
      return;
    }

    // Remove the last choice
    final lastChoice = _choiceHistory.removeLast();

    // Navigate to the scene where the choice was made
    await navigateToScene(lastChoice['sceneId'] as String);
  }

  /// Restart the current story
  Future<void> restartStory() async {
    if (_activeStory == null) {
      _setError('No active story');
      return;
    }

    _clearStoryState();
    await navigateToScene(_activeStory!.firstScene.id);
  }

  /// Mark narration as complete (called by TTS when done)
  void completeNarration() {
    _isNarrationComplete = true;

    // If this is a choice point, show choices
    if (_currentScene?.isChoicePoint ?? false) {
      _showChoices = true;
    } else if (_autoProceedEnabled &&
               _currentScene != null &&
               !_currentScene!.isEndingScene &&
               _currentSegmentIndex == _currentTextSegments.length - 1) {
      // If auto-proceed is enabled and this is the last segment of a non-choice, non-ending scene,
      // automatically proceed to the next scene after a delay
      final nextSceneId = _determineNextSceneId(_currentScene!);
      if (nextSceneId != null) {
        _scheduleAutoProceed(nextSceneId);
      }
    }

    notifyListeners();
  }

  /// Schedule auto-proceeding to the next scene
  void _scheduleAutoProceed(String nextSceneId) {
    // Cancel any existing timer
    _autoProceedTimer?.cancel();

    // Schedule a new timer
    _autoProceedTimer = Timer(const Duration(milliseconds: 800), () {
      if (_currentScene != null &&
          !_currentScene!.isChoicePoint &&
          !_currentScene!.isEndingScene) {
        navigateToScene(nextSceneId);
      }
    });
  }

  /// Schedule auto-proceeding with a custom action
  void _scheduleAutoAction(VoidCallback action) {
    // Cancel any existing timer
    _autoProceedTimer?.cancel();

    // Schedule a new timer
    _autoProceedTimer = Timer(const Duration(milliseconds: 800), action);
  }

  /// Enable or disable auto-proceeding
  void setAutoProceedEnabled(bool enabled) {
    _autoProceedEnabled = enabled;

    if (!enabled) {
      // Cancel any pending auto-proceed
      _autoProceedTimer?.cancel();
    } else if (_isNarrationComplete &&
               _currentScene != null &&
               !_currentScene!.isChoicePoint &&
               !_currentScene!.isEndingScene &&
               _currentSegmentIndex == _currentTextSegments.length - 1) {
      // If narration is already complete and we're enabling auto-proceed,
      // schedule an auto-proceed
      final nextSceneId = _determineNextSceneId(_currentScene!);
      if (nextSceneId != null) {
        _scheduleAutoProceed(nextSceneId);
      }
    }

    notifyListeners();
  }

  /// Navigate to the next text segment
  bool navigateToNextSegment() {
    if (_currentSegmentIndex < _currentTextSegments.length - 1) {
      _currentSegmentIndex++;
      _isNarrationComplete = false;
      notifyListeners();
      return true;
    } else if (_currentScene != null &&
               !_currentScene!.isChoicePoint &&
               !_currentScene!.isEndingScene) {
      // If we're at the last segment, determine the next scene
      final nextSceneId = _determineNextSceneId(_currentScene!);
      if (nextSceneId != null) {
        navigateToScene(nextSceneId);
        return true;
      }
    }
    return false;
  }

  /// Navigate to the previous text segment
  bool navigateToPreviousSegment() {
    if (_currentSegmentIndex > 0) {
      _currentSegmentIndex--;
      _isNarrationComplete = false;
      notifyListeners();
      return true;
    } else if (_sceneHistory.length > 1) {
      // If we're at the first segment and there's a previous scene,
      // navigate to it
      final previousSceneId = _sceneHistory[_sceneHistory.length - 2];
      navigateToScene(previousSceneId);
      return true;
    }
    return false;
  }

  /// Toggle showing choices
  void setShowChoices(bool show) {
    _showChoices = show;
    notifyListeners();
  }

  /// Mark an image as loading to prevent duplicate requests
  void markImageAsLoading(String imagePath) {
    _loadingImages.add(imagePath);
    notifyListeners();
  }

  /// Mark an image as finished loading
  void markImageAsLoaded(String imagePath, String sceneId) {
    _loadingImages.remove(imagePath);
    _imageCache[sceneId] = true;

    // Update validated image info if it exists
    final validatedImage = _validatedImages[sceneId];
    if (validatedImage != null) {
      _validatedImages[sceneId] = validatedImage.copyWith(isLoaded: true);
    }

    notifyListeners();
  }

  /// Clear the current story state
  void _clearStoryState() {
    _currentScene = null;
    _sceneHistory.clear();
    _choiceHistory.clear();
    _isNarrationComplete = false;
    _showChoices = false;
    _currentTextSegments = [];
    _currentSegmentIndex = 0;

    // Cancel any pending auto-proceed
    _autoProceedTimer?.cancel();

    // Clear image loading state for the current story
    _loadingImages.clear();
    _imageCache.clear();
    _validatedImages.clear();
  }

  @override
  void dispose() {
    _autoProceedTimer?.cancel();
    super.dispose();
  }

  /// Set the active story directly for testing purposes
  /// This method should only be used in tests
  void setActiveStoryForTesting(Story story) {
    _stories = [story];
    _activeStory = story;
    _currentScene = story.firstScene;
    _sceneHistory.clear();
    _choiceHistory.clear();
    _sceneHistory.add(story.firstScene.id);
    _segmentText(story.firstScene.narrationText);
    notifyListeners();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Set error message
  void _setError(String message) {
    _errorMessage = message;
    _isLoading = false;
    debugPrint('StoryProvider error: $message');
    notifyListeners();
  }
}
