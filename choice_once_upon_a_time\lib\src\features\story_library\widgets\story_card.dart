import 'package:flutter/material.dart';
import '../../../shared_kernel/theme/app_theme.dart';

/// Widget that displays a story card in the story library
class StoryCard extends StatelessWidget {
  /// The title of the story
  final String title;

  /// The path to the cover image
  final String coverImagePath;

  /// Whether the story is locked
  final bool isLocked;

  /// The action to perform when tapped
  final VoidCallback onTap;

  /// Constructor
  const StoryCard({
    super.key,
    required this.title,
    required this.coverImagePath,
    required this.isLocked,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        child: Stack(
          children: [
            // Cover image or placeholder
            Positioned.fill(
              child: _buildCoverImage(),
            ),

            // Title at the bottom
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black.withOpacity(0.8),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),

            // Lock overlay for locked stories
            if (isLocked)
              Positioned.fill(
                child: Container(
                  color: Colors.black.withOpacity(0.5),
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.lock,
                          color: Colors.white,
                          size: 48,
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.secondaryColor,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: const Text(
                            'Unlock',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

            // "Free" badge for free stories
            if (!isLocked)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.accentColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Text(
                    'Free',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Build the cover image or a placeholder
  Widget _buildCoverImage() {
    if (coverImagePath.isEmpty) {
      return Container(
        color: Colors.grey[300],
        child: Center(
          child: Icon(
            Icons.book,
            size: 64,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    // Add .jpg extension if not present
    String fullPath = coverImagePath;
    if (!fullPath.endsWith('.jpg') &&
        !fullPath.endsWith('.jpeg') &&
        !fullPath.endsWith('.png')) {
      fullPath = '$fullPath.jpeg';
    }

    return Image.asset(
      fullPath,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        debugPrint('Error loading image: $error');
        return Container(
          color: Colors.grey[300],
          child: Center(
            child: Icon(
              Icons.broken_image,
              size: 64,
              color: Colors.grey[600],
            ),
          ),
        );
      },
    );
  }
}
