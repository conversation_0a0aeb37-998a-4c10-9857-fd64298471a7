import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../features/coming_soon/screens/coming_soon_screen.dart';
import '../../../features/profile/providers/active_child_provider.dart';
import '../../../features/settings/screens/settings_screen.dart';
import '../../../features/story/providers/story_provider.dart';
import '../../../features/story_interaction/screens/story_interaction_screen.dart';
import '../../../features/story_library/screens/story_library_screen.dart';
import '../../../features/monetization/providers/purchase_provider.dart';
import '../../../features/monetization/screens/premium_screen.dart';
import '../../../features/auth/providers/parent_auth_provider.dart';
import '../../../features/profile/widgets/sync_status_indicator.dart';

import '../widgets/action_button.dart';
import '../widgets/character_card.dart';
import '../widgets/settings_button.dart';

/// The main menu screen of the application
class MainMenuScreen extends StatefulWidget {
  /// Constructor
  const MainMenuScreen({super.key});

  /// Route name for navigation
  static const routeName = '/main-menu';

  @override
  State<MainMenuScreen> createState() => _MainMenuScreenState();
}

class _MainMenuScreenState extends State<MainMenuScreen> {
  /// Whether the resume button is enabled
  bool _canResumeStory = false;

  @override
  void initState() {
    super.initState();
    // Check if there's a story to resume
    _checkForResumeStory();
  }

  /// Check if there's a story to resume
  Future<void> _checkForResumeStory() async {
    final activeChildProvider = Provider.of<ActiveChildProvider>(context, listen: false);
    final hasStory = await activeChildProvider.hasStoryToResume();

    if (mounted && hasStory != _canResumeStory) {
      setState(() {
        _canResumeStory = hasStory;
      });
    }
  }

  /// Resume the last story
  void _resumeLastStory(BuildContext context) {
    // Get the providers
    final storyProvider = Provider.of<StoryProvider>(context, listen: false);
    final activeChildProvider = Provider.of<ActiveChildProvider>(context, listen: false);

    // Show a loading indicator
    setState(() {
      _canResumeStory = false; // Disable the button while loading
    });

    // Start the loading process
    _startLoading(storyProvider, activeChildProvider);
  }

  /// Start loading the story data
  Future<void> _startLoading(
    StoryProvider storyProvider,
    ActiveChildProvider activeChildProvider,
  ) async {
    try {
      // Get the last story progress
      final progress = await activeChildProvider.getLastStoryProgress();

      if (!mounted) return;

      if (progress != null) {
        final String storyId = progress['storyId'] as String;
        final String sceneId = progress['sceneId'] as String;

        // Load all stories if not already loaded
        if (storyProvider.stories.isEmpty) {
          await storyProvider.loadAllStories();
        }

        if (!mounted) return;

        // Set the active story and navigate to the scene
        await storyProvider.setActiveStory(storyId, initialSceneId: sceneId);

        if (!mounted) return;

        // Navigate to the story interaction screen
        _navigateToStory(storyId);
      }
    } catch (e) {
      if (!mounted) return;

      // Show an error message
      _showErrorMessage('Error loading story: $e');
    } finally {
      if (mounted) {
        // Re-enable the button
        setState(() {
          _canResumeStory = true;
        });
      }
    }
  }

  /// Navigate to the story interaction screen
  void _navigateToStory(String storyId) {
    Navigator.pushNamed(
      context,
      StoryInteractionScreen.routeName,
      arguments: storyId,
    );
  }

  /// Show an error message
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    // Get the active child profile (if any)
    final activeChildProvider = Provider.of<ActiveChildProvider>(context);
    final activeProfile = activeChildProvider.activeProfile;
    final childName = activeProfile?.name;

    // Get screen size to determine layout
    final screenSize = MediaQuery.of(context).size;
    final isLandscape = screenSize.width > screenSize.height;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          // Background gradient
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFE0F7FA), // Light cyan
              Color(0xFFB2EBF2), // Cyan
            ],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              // Settings button in top right
              Positioned(
                top: 8,
                right: 8,
                child: SettingsButton(
                  onTap: () => Navigator.pushNamed(
                    context,
                    SettingsScreen.routeName,
                  ),
                ),
              ),

              // Premium status indicator in top left
              Positioned(
                top: 8,
                left: 8,
                child: Consumer<PurchaseProvider>(
                  builder: (context, purchaseProvider, child) {
                    if (purchaseProvider.isPremiumUnlocked) {
                      return Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.amber,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 4,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.star, color: Colors.white, size: 16),
                            SizedBox(width: 4),
                            Text(
                              'PREMIUM',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ),

              // Main content with responsive layout
              if (isLandscape)
                _buildLandscapeLayout(
                  context,
                  childName: childName,
                  canResumeStory: _canResumeStory,
                )
              else
                _buildPortraitLayout(
                  context,
                  childName: childName,
                  canResumeStory: _canResumeStory,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the landscape layout (TV/Tablet)
  Widget _buildLandscapeLayout(
    BuildContext context, {
    String? childName,
    bool canResumeStory = false,
  }) {
    // Get screen dimensions for responsive sizing
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    // Calculate responsive sizes
    final double minDimension = math.min(screenWidth, screenHeight);
    final double padding = minDimension * 0.02; // 2% of min dimension

    return Row(
      children: [
        // Left side (1/3): Character Card
        Expanded(
          flex: 1,
          child: Center(
            child: CharacterCard(childName: childName),
          ),
        ),

        // Right side (2/3): Action Buttons
        Expanded(
          flex: 2,
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: LayoutBuilder(
              builder: (context, constraints) {
                return _buildActionButtons(
                  context,
                  canResumeStory: canResumeStory,
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  /// Build the portrait layout (Phone)
  Widget _buildPortraitLayout(
    BuildContext context, {
    String? childName,
    bool canResumeStory = false,
  }) {
    // Get screen dimensions for responsive sizing
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    // Calculate responsive sizes
    final double minDimension = math.min(screenWidth, screenHeight);
    final double padding = minDimension * 0.02; // 2% of min dimension

    return Column(
      children: [
        // Top part: Character Card
        SizedBox(
          height: screenHeight * 0.35, // Slightly smaller to give more room for buttons
          child: Center(
            child: CharacterCard(childName: childName),
          ),
        ),

        // Bottom part: Action Buttons
        Expanded(
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: LayoutBuilder(
              builder: (context, constraints) {
                return Column(
                  children: [
                    Expanded(
                      child: _buildActionButtons(
                        context,
                        canResumeStory: canResumeStory,
                      ),
                    ),
                    // Sync status indicator
                    const Padding(
                      padding: EdgeInsets.only(top: 8),
                      child: SyncStatusIndicator(compact: true),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  /// Build the action buttons
  Widget _buildActionButtons(
    BuildContext context, {
    bool canResumeStory = false,
  }) {
    return Consumer<PurchaseProvider>(
      builder: (context, purchaseProvider, child) {
        final buttons = <Widget>[
          // Start New Adventure button
          ActionButton(
            icon: Icons.book,
            text: 'New Story',
            onTap: () => Navigator.pushNamed(
              context,
              StoryLibraryScreen.routeName,
            ),
          ),

          // Resume Previous Story button
          ActionButton(
            icon: Icons.play_arrow,
            text: 'Continue Story',
            onTap: () {
              _resumeLastStory(context);
            },
            isEnabled: _canResumeStory,
          ),

          // Premium unlock button (only show if not premium)
          if (!purchaseProvider.isPremiumUnlocked)
            ActionButton(
              icon: Icons.star,
              text: 'Unlock Premium',
              onTap: () => Navigator.pushNamed(
                context,
                PremiumScreen.routeName,
              ),
            ),

          // Surprise Me button
          ActionButton(
            icon: Icons.help,
            text: 'Surprise Me!',
            onTap: () {
              // Navigate to a random story
              // For now, just show a message
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Random Story - Coming Soon'),
                ),
              );
            },
          ),

          // Interactive AI button (only show if premium or as a teaser)
          ActionButton(
            icon: Icons.chat,
            text: purchaseProvider.isPremiumUnlocked ? 'Chat with AI' : 'AI Chat (Premium)',
            onTap: () {
              if (purchaseProvider.isPremiumUnlocked) {
                Navigator.pushNamed(
                  context,
                  ComingSoonScreen.routeName,
                  arguments: 'AI Storyteller',
                );
              } else {
                Navigator.pushNamed(
                  context,
                  PremiumScreen.routeName,
                );
              }
            },
            isEnabled: purchaseProvider.isPremiumUnlocked,
          ),
        ];

        return GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          children: buttons,
        );
      },
    );
  }
}
