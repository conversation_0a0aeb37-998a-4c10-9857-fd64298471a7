import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../features/coming_soon/screens/coming_soon_screen.dart';
import '../../../features/profile/providers/active_child_provider.dart';
import '../../../features/settings/screens/settings_screen.dart';
import '../../../features/story/providers/story_provider.dart';
import '../../../features/story_interaction/screens/story_interaction_screen.dart';
import '../../../features/story_library/screens/story_library_screen.dart';
import '../../../shared_kernel/theme/app_theme.dart';
import '../widgets/action_button.dart';
import '../widgets/character_card.dart';
import '../widgets/settings_button.dart';

/// The main menu screen of the application
class MainMenuScreen extends StatefulWidget {
  /// Constructor
  const MainMenuScreen({super.key});

  /// Route name for navigation
  static const routeName = '/';

  @override
  State<MainMenuScreen> createState() => _MainMenuScreenState();
}

class _MainMenuScreenState extends State<MainMenuScreen> {
  /// Whether the resume button is enabled
  bool _canResumeStory = false;

  @override
  void initState() {
    super.initState();
    // Check if there's a story to resume
    _checkForResumeStory();
  }

  /// Check if there's a story to resume
  Future<void> _checkForResumeStory() async {
    final activeChildProvider = Provider.of<ActiveChildProvider>(context, listen: false);
    final hasStory = await activeChildProvider.hasStoryToResume();

    if (mounted && hasStory != _canResumeStory) {
      setState(() {
        _canResumeStory = hasStory;
      });
    }
  }

  /// Resume the last story
  void _resumeLastStory(BuildContext context) {
    // Get the providers
    final storyProvider = Provider.of<StoryProvider>(context, listen: false);
    final activeChildProvider = Provider.of<ActiveChildProvider>(context, listen: false);

    // Show a loading indicator
    setState(() {
      _canResumeStory = false; // Disable the button while loading
    });

    // Start the loading process
    _startLoading(storyProvider, activeChildProvider);
  }

  /// Start loading the story data
  Future<void> _startLoading(
    StoryProvider storyProvider,
    ActiveChildProvider activeChildProvider,
  ) async {
    try {
      // Get the last story progress
      final progress = await activeChildProvider.getLastStoryProgress();

      if (!mounted) return;

      if (progress != null) {
        final String storyId = progress['storyId'] as String;
        final String sceneId = progress['sceneId'] as String;

        // Load all stories if not already loaded
        if (storyProvider.stories.isEmpty) {
          await storyProvider.loadAllStories();
        }

        if (!mounted) return;

        // Set the active story and navigate to the scene
        await storyProvider.setActiveStory(storyId, initialSceneId: sceneId);

        if (!mounted) return;

        // Navigate to the story interaction screen
        _navigateToStory(storyId);
      }
    } catch (e) {
      if (!mounted) return;

      // Show an error message
      _showErrorMessage('Error loading story: $e');
    } finally {
      if (mounted) {
        // Re-enable the button
        setState(() {
          _canResumeStory = true;
        });
      }
    }
  }

  /// Navigate to the story interaction screen
  void _navigateToStory(String storyId) {
    Navigator.pushNamed(
      context,
      StoryInteractionScreen.routeName,
      arguments: storyId,
    );
  }

  /// Show an error message
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    // Get the active child profile (if any)
    final activeChildProvider = Provider.of<ActiveChildProvider>(context);
    final activeProfile = activeChildProvider.activeProfile;
    final childName = activeProfile?.name;

    // Get screen size to determine layout
    final screenSize = MediaQuery.of(context).size;
    final isLandscape = screenSize.width > screenSize.height;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          // Background gradient
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFE0F7FA), // Light cyan
              Color(0xFFB2EBF2), // Cyan
            ],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              // Settings button in top right
              Positioned(
                top: 8,
                right: 8,
                child: SettingsButton(
                  onTap: () => Navigator.pushNamed(
                    context,
                    SettingsScreen.routeName,
                  ),
                ),
              ),

              // Main content with responsive layout
              if (isLandscape)
                _buildLandscapeLayout(
                  context,
                  childName: childName,
                  canResumeStory: _canResumeStory,
                )
              else
                _buildPortraitLayout(
                  context,
                  childName: childName,
                  canResumeStory: _canResumeStory,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the landscape layout (TV/Tablet)
  Widget _buildLandscapeLayout(
    BuildContext context, {
    String? childName,
    bool canResumeStory = false,
  }) {
    // Get screen dimensions for responsive sizing
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    // Calculate responsive sizes
    final double minDimension = math.min(screenWidth, screenHeight);
    final double padding = minDimension * 0.02; // 2% of min dimension

    return Row(
      children: [
        // Left side (1/3): Character Card
        Expanded(
          flex: 1,
          child: Center(
            child: CharacterCard(childName: childName),
          ),
        ),

        // Right side (2/3): Action Buttons
        Expanded(
          flex: 2,
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: LayoutBuilder(
              builder: (context, constraints) {
                return GridView.count(
                  crossAxisCount: 2,
                  crossAxisSpacing: padding / 2,
                  mainAxisSpacing: padding / 2,
                  shrinkWrap: true, // Prevent scrolling
                  physics: const NeverScrollableScrollPhysics(), // Prevent scrolling
                  children: _buildActionButtons(
                    context,
                    canResumeStory: canResumeStory,
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  /// Build the portrait layout (Phone)
  Widget _buildPortraitLayout(
    BuildContext context, {
    String? childName,
    bool canResumeStory = false,
  }) {
    // Get screen dimensions for responsive sizing
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    // Calculate responsive sizes
    final double minDimension = math.min(screenWidth, screenHeight);
    final double padding = minDimension * 0.02; // 2% of min dimension

    return Column(
      children: [
        // Top part: Character Card
        SizedBox(
          height: screenHeight * 0.35, // Slightly smaller to give more room for buttons
          child: Center(
            child: CharacterCard(childName: childName),
          ),
        ),

        // Bottom part: Action Buttons
        Expanded(
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: LayoutBuilder(
              builder: (context, constraints) {
                return GridView.count(
                  crossAxisCount: 2,
                  crossAxisSpacing: padding / 2,
                  mainAxisSpacing: padding / 2,
                  shrinkWrap: true, // Prevent scrolling
                  physics: const NeverScrollableScrollPhysics(), // Prevent scrolling
                  children: _buildActionButtons(
                    context,
                    canResumeStory: canResumeStory,
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  /// Build the action buttons
  List<Widget> _buildActionButtons(
    BuildContext context, {
    bool canResumeStory = false,
  }) {
    return [
      // Start New Adventure button
      ActionButton(
        icon: Icons.book,
        text: 'New Story',
        onTap: () => Navigator.pushNamed(
          context,
          StoryLibraryScreen.routeName,
        ),
      ),

      // Resume Previous Story button
      ActionButton(
        icon: Icons.play_arrow,
        text: 'Continue Story',
        onTap: () {
          _resumeLastStory(context);
        },
        isEnabled: _canResumeStory,
      ),

      // Surprise Me button
      ActionButton(
        icon: Icons.help,
        text: 'Surprise Me!',
        onTap: () {
          // Navigate to a random story
          // For now, just show a message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Random Story - Coming Soon'),
            ),
          );
        },
      ),

      // Interactive AI button
      ActionButton(
        icon: Icons.chat,
        text: 'Chat with AI',
        onTap: () => Navigator.pushNamed(
          context,
          ComingSoonScreen.routeName,
          arguments: 'AI Storyteller',
        ),
      ),
    ];
  }
}
