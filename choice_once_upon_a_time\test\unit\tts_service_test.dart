import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:choice_once_upon_a_time/src/features/story/services/tts_service.dart';

import 'tts_service_test.mocks.dart';

@GenerateMocks([FlutterTts])
void main() {
  group('TTSService', () {
    late MockFlutterTts mockFlutterTts;
    late TTSService ttsService;

    setUp(() {
      mockFlutterTts = MockFlutterTts();
      
      // Mock the necessary methods
      when(mockFlutterTts.setLanguage(any)).thenAnswer((_) async => 1);
      when(mockFlutterTts.setSpeechRate(any)).thenAnswer((_) async => 1);
      when(mockFlutterTts.setVolume(any)).thenAnswer((_) async => 1);
      when(mockFlutterTts.setPitch(any)).thenAnswer((_) async => 1);
      when(mockFlutterTts.isLanguageAvailable(any)).thenAnswer((_) async => true);
      when(mockFlutterTts.speak(any)).thenAnswer((_) async => 1);
      when(mockFlutterTts.stop()).thenAnswer((_) async => 1);
      when(mockFlutterTts.pause()).thenAnswer((_) async => 1);
      
      // Create the service with the mock
      ttsService = TTSServiceForTesting(mockFlutterTts);
    });

    test('should initialize correctly', () {
      expect(ttsService.isEnabled, isTrue);
      expect(ttsService.isSpeaking, isFalse);
      expect(ttsService.currentWord, isEmpty);
      expect(ttsService.currentWordIndex, equals(0));
      expect(ttsService.words, isEmpty);
    });

    test('should speak text correctly', () async {
      const testText = 'This is a test sentence.';
      
      await ttsService.speak(testText);
      
      // Verify that speak was called with the correct text
      verify(mockFlutterTts.speak(testText)).called(1);
      
      // Verify that the words were split correctly
      expect(ttsService.words, equals(['This', 'is', 'a', 'test', 'sentence.']));
    });

    test('should handle empty text', () async {
      await ttsService.speak('');
      
      // Verify that speak was not called
      verifyNever(mockFlutterTts.speak(any));
    });

    test('should stop speaking', () async {
      // First speak something
      await ttsService.speak('Test');
      
      // Then stop
      await ttsService.stop();
      
      // Verify that stop was called
      verify(mockFlutterTts.stop()).called(1);
    });

    test('should normalize text correctly', () async {
      const testText = 'Text with  multiple   spaces and\tcontrol\ncharacters.';
      
      await ttsService.speak(testText);
      
      // Verify that speak was called with normalized text
      verify(mockFlutterTts.speak('Text with multiple spaces and control characters.')).called(1);
    });

    test('should resume from current word', () async {
      // Setup a specific state
      await ttsService.speak('One two three four five');
      
      // Manually set the current word index (simulating progress)
      (ttsService as TTSServiceForTesting).setCurrentWordIndex(2); // "three"
      
      // Resume speaking
      await ttsService.resume();
      
      // Verify that speak was called with the remaining text
      verify(mockFlutterTts.speak('three four five')).called(1);
    });

    test('should handle problematic characters', () async {
      const testText = 'Text with\u0000invisible\u007Fcontrol\u009Fcharacters.';
      
      await ttsService.speak(testText);
      
      // Verify that speak was called with cleaned text
      verify(mockFlutterTts.speak('Text with invisible control characters.')).called(1);
    });
  });
}

/// Test-specific extension of TTSService to allow access to protected methods
class TTSServiceForTesting extends TTSService {
  final FlutterTts _mockFlutterTts;
  
  TTSServiceForTesting(this._mockFlutterTts);
  
  @override
  FlutterTts get _flutterTts => _mockFlutterTts;
  
  /// Set the current word index for testing
  void setCurrentWordIndex(int index) {
    super._currentWordIndex = index;
  }
}
