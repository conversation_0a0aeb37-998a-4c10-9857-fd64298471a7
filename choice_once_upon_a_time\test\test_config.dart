/// Test configuration for Choice: Once Upon A Time
/// 
/// This file contains configuration settings and constants used across all tests.

class TestConfig {
  /// Test timeouts
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const Duration longTimeout = Duration(minutes: 2);
  static const Duration shortTimeout = Duration(seconds: 5);

  /// Test data
  static const String testEmail = '<EMAIL>';
  static const String testPassword = 'password123';
  static const String testChildName = 'Test Child';
  static const String testParentName = 'Test Parent';

  /// Mock service configurations
  static const bool useMockServices = true;
  static const bool enableNetworkMocking = true;
  static const bool enableFirebaseMocking = true;

  /// Test environment settings
  static const String testEnvironment = 'test';
  static const bool enableLogging = true;
  static const bool enableDebugPrints = false;

  /// Screen size configurations for responsive testing
  static const Map<String, Size> testScreenSizes = {
    'phone_portrait': <PERSON><PERSON>(360, 640),
    'phone_landscape': <PERSON><PERSON>(640, 360),
    'phone_large_portrait': <PERSON><PERSON>(414, 896),
    'phone_large_landscape': <PERSON><PERSON>(896, 414),
    'tablet_portrait': <PERSON><PERSON>(768, 1024),
    'tablet_landscape': <PERSON>ze(1024, 768),
    'tablet_large_portrait': Size(834, 1194),
    'tablet_large_landscape': Size(1194, 834),
    'desktop_small': Size(1200, 800),
    'desktop_large': Size(1920, 1080),
  };

  /// Test story data
  static const Map<String, dynamic> testStoryData = {
    'id': 'test-story-1',
    'title': 'Test Adventure',
    'description': 'A test story for integration testing',
    'coverImagePath': 'assets/images/test_cover.jpg',
    'isLocked': false,
  };

  /// Test child profile data
  static const Map<String, dynamic> testChildProfileData = {
    'id': 'test-child-1',
    'name': 'Test Child',
    'age': 8,
    'avatarId': 'avatar1',
  };

  /// Test parent profile data
  static const Map<String, dynamic> testParentProfileData = {
    'email': '<EMAIL>',
    'displayName': 'Test Parent',
    'uid': 'test-user-id',
  };

  /// Firebase test configuration
  static const Map<String, String> firebaseTestConfig = {
    'projectId': 'test-project',
    'authDomain': 'test-project.firebaseapp.com',
    'databaseURL': 'https://test-project.firebaseio.com',
    'storageBucket': 'test-project.appspot.com',
  };

  /// IAP test configuration
  static const Map<String, dynamic> iapTestConfig = {
    'premiumMonthlyId': 'premium_monthly_test',
    'premiumYearlyId': 'premium_yearly_test',
    'premiumLifetimeId': 'premium_lifetime_test',
    'testProductPrice': '\$4.99',
  };

  /// Test asset paths
  static const Map<String, String> testAssets = {
    'testCoverImage': 'assets/images/test_cover.jpg',
    'testBackgroundImage': 'assets/images/test_background.jpg',
    'testAvatarImage': 'assets/images/avatars/test_avatar.png',
  };

  /// Error messages for testing
  static const Map<String, String> testErrorMessages = {
    'networkError': 'Network error. Please check your connection.',
    'authError': 'Authentication failed. Please try again.',
    'purchaseError': 'Purchase failed. Please try again.',
    'syncError': 'Sync failed. Please try again.',
    'invalidCredentials': 'Invalid email or password.',
    'userCanceled': 'Purchase was canceled.',
    'billingUnavailable': 'Billing service is unavailable.',
  };

  /// Test delays and timing
  static const Map<String, Duration> testTimings = {
    'shortDelay': Duration(milliseconds: 100),
    'mediumDelay': Duration(milliseconds: 500),
    'longDelay': Duration(seconds: 1),
    'animationDuration': Duration(milliseconds: 300),
    'narrationDelay': Duration(seconds: 2),
  };

  /// Coverage thresholds
  static const Map<String, double> coverageThresholds = {
    'line': 0.90, // 90% line coverage
    'function': 0.85, // 85% function coverage
    'branch': 0.80, // 80% branch coverage
    'statement': 0.90, // 90% statement coverage
  };

  /// Test groups configuration
  static const Map<String, bool> testGroups = {
    'unit': true,
    'widget': true,
    'integration': true,
    'performance': false, // Disabled by default
    'accessibility': true,
    'responsive': true,
  };

  /// Platform-specific configurations
  static const Map<String, Map<String, dynamic>> platformConfigs = {
    'chrome': {
      'headless': false,
      'windowSize': {'width': 1200, 'height': 800},
      'devicePixelRatio': 1.0,
    },
    'android': {
      'deviceId': 'emulator-5554',
      'orientation': 'landscape',
    },
    'ios': {
      'deviceId': 'iPhone 12',
      'orientation': 'landscape',
    },
  };

  /// Test data generators
  static String generateTestEmail([String? prefix]) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${prefix ?? 'test'}$<EMAIL>';
  }

  static String generateTestChildName([String? prefix]) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${prefix ?? 'Child'} $timestamp';
  }

  static String generateTestId([String? prefix]) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${prefix ?? 'test'}-$timestamp';
  }

  /// Validation helpers
  static bool isValidTestEmail(String email) {
    return email.contains('@') && email.contains('example.com');
  }

  static bool isValidTestPassword(String password) {
    return password.length >= 6;
  }

  static bool isValidTestChildName(String name) {
    return name.isNotEmpty && name.length <= 50;
  }

  /// Test environment setup
  static Future<void> setupTestEnvironment() async {
    // Initialize test-specific configurations
    // This would include setting up mock services, test databases, etc.
  }

  static Future<void> tearDownTestEnvironment() async {
    // Clean up test environment
    // This would include clearing test data, resetting mocks, etc.
  }

  /// Test utilities
  static void logTestInfo(String message) {
    if (enableLogging) {
      print('[TEST INFO] $message');
    }
  }

  static void logTestError(String message, [dynamic error, StackTrace? stackTrace]) {
    if (enableLogging) {
      print('[TEST ERROR] $message');
      if (error != null) print('Error: $error');
      if (stackTrace != null) print('Stack trace: $stackTrace');
    }
  }

  static void debugPrint(String message) {
    if (enableDebugPrints) {
      print('[TEST DEBUG] $message');
    }
  }
}

/// Test result tracking
class TestResults {
  static final Map<String, TestResult> _results = {};

  static void recordResult(String testName, bool passed, {String? error}) {
    _results[testName] = TestResult(
      name: testName,
      passed: passed,
      error: error,
      timestamp: DateTime.now(),
    );
  }

  static TestResult? getResult(String testName) {
    return _results[testName];
  }

  static List<TestResult> getAllResults() {
    return _results.values.toList();
  }

  static Map<String, int> getSummary() {
    final passed = _results.values.where((r) => r.passed).length;
    final failed = _results.values.where((r) => !r.passed).length;
    return {'passed': passed, 'failed': failed, 'total': _results.length};
  }

  static void clear() {
    _results.clear();
  }
}

/// Individual test result
class TestResult {
  final String name;
  final bool passed;
  final String? error;
  final DateTime timestamp;

  const TestResult({
    required this.name,
    required this.passed,
    this.error,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'TestResult(name: $name, passed: $passed, error: $error, timestamp: $timestamp)';
  }
}

/// Size class for responsive testing
class Size {
  final double width;
  final double height;

  const Size(this.width, this.height);

  @override
  String toString() => 'Size($width, $height)';

  @override
  bool operator ==(Object other) {
    return other is Size && other.width == width && other.height == height;
  }

  @override
  int get hashCode => Object.hash(width, height);
}
