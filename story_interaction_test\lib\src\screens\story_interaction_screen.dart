import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../models/story_model.dart';
import '../providers/story_provider.dart';
import '../providers/story_settings_provider.dart';
import '../widgets/story_control_panel.dart';
import '../widgets/choice_popup.dart';
import '../widgets/settings_panel.dart';
import '../widgets/story_complete_panel.dart';

/// Main screen for story interaction and narration
class StoryInteractionScreen extends StatefulWidget {
  const StoryInteractionScreen({super.key});

  @override
  State<StoryInteractionScreen> createState() => _StoryInteractionScreenState();
}

class _StoryInteractionScreenState extends State<StoryInteractionScreen> {
  bool _showSettings = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<StoryProvider>(
        builder: (context, storyProvider, child) {
          if (storyProvider.isLoading) {
            return _buildLoadingScreen();
          }

          if (storyProvider.errorMessage != null) {
            return _buildErrorScreen(storyProvider.errorMessage!);
          }

          if (storyProvider.currentScene == null) {
            return _buildNoStoryScreen();
          }

          return _buildStoryScreen(storyProvider);
        },
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.blue[300]!, Colors.blue[600]!],
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 20),
            Text(
              'Loading Story...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen(String error) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.red[300]!, Colors.red[600]!],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white,
              size: 80,
            ),
            const SizedBox(height: 20),
            const Text(
              'Error Loading Story',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Text(
                error,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoStoryScreen() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.grey[300]!, Colors.grey[600]!],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.book_outlined,
              color: Colors.white,
              size: 80,
            ),
            const SizedBox(height: 20),
            const Text(
              'No Story Loaded',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Select a Story'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoryScreen(StoryProvider storyProvider) {
    return Stack(
      children: [
        // Background image
        _buildBackgroundImage(storyProvider.currentScene!.imagePath),

        // Settings button (top-right)
        Positioned(
          top: 40,
          right: 20,
          child: _buildSettingsButton(),
        ),

        // Story control panel (bottom)
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: const StoryControlPanel(),
        ),

        // Choice popup (center)
        if (storyProvider.showChoicePopup)
          const ChoicePopup(),

        // Story complete panel (center)
        if (storyProvider.isStoryComplete)
          const StoryCompletePanel(),

        // Settings panel (right side)
        if (_showSettings)
          Positioned(
            top: 0,
            right: 0,
            bottom: 0,
            width: 300,
            child: SettingsPanel(
              onClose: () => setState(() => _showSettings = false),
            ),
          ),
      ],
    );
  }

  Widget _buildBackgroundImage(String imagePath) {
    return SizedBox.expand(
      child: _tryLoadImage(imagePath),
    );
  }

  Widget _tryLoadImage(String basePath) {
    // List of extensions to try in order of preference
    final extensions = ['', '.png', '.jpg', '.jpeg', '.webp'];

    return FutureBuilder<String?>(
      future: _findValidImagePath(basePath, extensions),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            color: Colors.grey[800],
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        final validPath = snapshot.data;

        if (validPath != null) {
          return Image.asset(
            validPath,
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
            errorBuilder: (context, error, stackTrace) {
              debugPrint('Error loading image: $error');
              return _buildImageErrorWidget(validPath);
            },
          );
        } else {
          return _buildImageErrorWidget(basePath);
        }
      },
    );
  }

  Future<String?> _findValidImagePath(String basePath, List<String> extensions) async {
    debugPrint('Attempting to find valid image path for: $basePath');

    // First, check if the path already has a valid extension
    final String basePathLower = basePath.toLowerCase();
    if (basePathLower.endsWith('.png') ||
        basePathLower.endsWith('.jpg') ||
        basePathLower.endsWith('.jpeg') ||
        basePathLower.endsWith('.webp')) {
      try {
        await rootBundle.load(basePath);
        debugPrint('Found image with existing extension: $basePath');
        return basePath;
      } catch (e) {
        debugPrint('Image with existing extension not found: $basePath');
      }
    }

    // Try each extension
    for (final extension in extensions) {
      final path = extension.isEmpty ? basePath : '$basePath$extension';
      try {
        await rootBundle.load(path);
        debugPrint('Found valid image path: $path');
        return path;
      } catch (e) {
        debugPrint('Image not found with path: $path');
        continue;
      }
    }

    return null;
  }

  Widget _buildImageErrorWidget(String attemptedPath) {
    return Container(
      color: Colors.grey[800],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_not_supported,
              size: 100,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Image not found',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 18,
              ),
            ),
            if (kDebugMode)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  'Path: $attemptedPath',
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(128),
        borderRadius: BorderRadius.circular(25),
      ),
      child: IconButton(
        icon: const Icon(
          Icons.settings,
          color: Colors.white,
          size: 28,
        ),
        onPressed: () => setState(() => _showSettings = !_showSettings),
      ),
    );
  }
}
