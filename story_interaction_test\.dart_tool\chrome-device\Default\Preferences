{"aadc_info": {"age_group": 0}, "accessibility": {"captions": {"live_caption_language": "en-US"}}, "account_info": [{"access_point": 17, "account_id": "0003BFFDA06E3E04", "accountcapabilities": {"accountcapabilities/g42tslldmfya": -1, "accountcapabilities/g44tilldmfya": -1, "accountcapabilities/ge2dinbnmnqxa": -1, "accountcapabilities/ge2tkmznmnqxa": -1, "accountcapabilities/ge2tknznmnqxa": -1, "accountcapabilities/ge2tkobnmnqxa": -1, "accountcapabilities/ge3dgmjnmnqxa": -1, "accountcapabilities/ge3dgobnmnqxa": -1, "accountcapabilities/geydgnznmnqxa": -1, "accountcapabilities/geytcnbnmnqxa": -1, "accountcapabilities/gezdcnbnmnqxa": -1, "accountcapabilities/gezdsmbnmnqxa": -1, "accountcapabilities/geztenjnmnqxa": -1, "accountcapabilities/gi2tklldmfya": -1, "accountcapabilities/gu2dqlldmfya": -1, "accountcapabilities/gu4dmlldmfya": -1, "accountcapabilities/guydolldmfya": -1, "accountcapabilities/guzdslldmfya": -1, "accountcapabilities/haytqlldmfya": -1, "accountcapabilities/he4tolldmfya": -1}, "edge_account_age_group": 3, "edge_account_cid": "2b1da38654006967", "edge_account_environment": 0, "edge_account_environment_string": "login.microsoftonline.com", "edge_account_first_name": "<PERSON><PERSON><PERSON><PERSON>", "edge_account_is_test_on_premises_profile": false, "edge_account_last_name": "<PERSON>", "edge_account_location": "IN", "edge_account_oid": "", "edge_account_phone_number": "", "edge_account_puid": "0003BFFDA06E3E04", "edge_account_sovereignty": 0, "edge_account_tenant_id": "9188040d-6c67-4c5b-b112-36a304b66dad", "edge_account_type": 1, "edge_tenant_supports_msa_linking": false, "edge_wam_aad_for_app_account_type": 0, "email": "<EMAIL>", "full_name": "", "gaia": "0003BFFDA06E3E04", "given_name": "", "hd": "", "is_supervised_child": -1, "is_under_advanced_protection": false, "last_downloaded_image_url_with_size": "", "locale": "", "picture_url": ""}], "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "arbitration_using_experiment_config": false, "autocomplete": {"retention_policy_last_version": 136}, "autofill": {"last_version_deduped": 136}, "browser": {"available_dark_theme_options": "All", "chat_v2": {"ip_eligibility_status": {"last_checked_time": "*****************"}}, "edge_sidebar_visibility": {"_game_assist_": {"order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": **********}}, "_gaming_assist_": {"order": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": **********, "523b5ef3-0b10-4154-8b62-10b2ebd00921": **********, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": -**********, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": -**********, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": *********, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": -*********, "96defd79-4015-4a32-bd09-794ff72183ef": **********}}, "add_app_to_bottom": true, "order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": 858993458, "d304175e-bbd4-4b66-8839-9627e56f391f": 429496729}}, "edge_sidebar_visibility_debug": {"order_list": ["Search"], "order_raw_data": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"name": "Search", "pos": "858993458"}, "d304175e-bbd4-4b66-8839-9627e56f391f": {"name": "unknown", "pos": "429496729"}}}, "editor_proofing_languages": {"en": {"Grammar": false, "Spelling": false}, "en-IN": {"Grammar": false, "Spelling": false}, "en-US": {"Grammar": true, "Spelling": true}}, "enable_text_prediction_v2": true, "gamer_mode_asset_store_prefs": {"779d97ed-2254-4943-a1f3-c811fa709092": {"gamer_mode_modal_script_hash": "xie40asvhdbPXzggtqUJ4lfglpLAYbJeXpWhq51+U+s=", "gamer_mode_modal_script_url": "https://edgeassetservice.azureedge.net/assets/gamer_mode_modal_ux/1.1.69/asset?assetgroup=GamerModeModalUX"}}, "has_seen_welcome_page": false, "hub_app_non_synced_preferences": {"apps": {"06be1ebe-f23a-4bea-ae45-3120ad86cfea": {"last_path": ""}, "0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": {"last_path": ""}, "168a2510-04d5-473e-b6a0-828815a7ca5f": {"last_path": ""}, "1ec8a5a9-971c-4c82-a104-5e1a259456b8": {"last_path": ""}, "2354565a-f412-4654-b89c-f92eaa9dbd20": {"last_path": ""}, "25fe2d1d-e934-482a-a62f-ea1705db905d": {"last_path": ""}, "2caf0cf4-ea42-4083-b928-29b39da1182b": {"last_path": ""}, "3458dfd2-bf1b-4d00-a6dd-a74a59d523c7": {"last_path": ""}, "35a43603-bb38-4b53-ba20-932cb9117794": {"last_path": ""}, "380c71d3-10bf-4a5d-9a06-c932e4b7d1d8": {"last_path": ""}, "439642fc-998d-4a64-8bb6-940ecaf6b60b": {"last_path": ""}, "523b5ef3-0b10-4154-8b62-10b2ebd00921": {"last_path": ""}, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": {"last_path": ""}, "698b01b4-557a-4a3b-9af7-a7e8138e8372": {"last_path": ""}, "76b926d6-3738-46bf-82d7-2ab896ddf70b": {"last_path": ""}, "7b52ae05-ae84-4165-b083-98ba2031bc22": {"last_path": ""}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"last_path": ""}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"last_path": ""}, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": {"last_path": ""}, "96defd79-4015-4a32-bd09-794ff72183ef": {"last_path": ""}, "9ce3c9c2-462f-4cc9-bbd7-57d656445be0": {"last_path": ""}, "a1a78183-6db3-4789-9e7c-84d157846d55": {"last_path": ""}, "bacc3c12-ebff-44b4-a0f8-ce8b69c9e047": {"last_path": ""}, "c814ae4d-fa0a-4280-a444-cb8bd264828b": {"last_path": ""}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"last_path": ""}, "d3ff4c56-a2b8-4673-ad13-35e7706cc9d1": {"last_path": ""}, "da15ec1d-543d-41c9-94b8-eb2bd060f2c7": {"last_path": ""}, "dadd1f1c-380c-4871-9e09-7971b6b15069": {"last_path": ""}, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": {"last_path": ""}}}, "hub_app_preferences": {"2cb2db96-3bd0-403e-abe2-9269b3761041": {"auto_show": {"enabled": true}}, "439642fc-998d-4a64-8bb6-940ecaf6b60b": {"auto_show": {"enabled": true}}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"all_scenarios": {"auto_open": {"enabled": false}}, "auto_show": {"enabled": false}}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"auto_show": {"enabled": true}}, "apps": {"b0197d6d-a306-4106-8536-17bb5ecfd446": {"last_path": ""}}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"auto_show": {"enabled": true}}, "d304175e-bbd4-4b66-8839-9627e56f391f": {"auto_show": {"enabled": true}}, "default_on_apps_cleanup_state": 1}, "hub_app_usage_preferences": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": 2, "2caf0cf4-ea42-4083-b928-29b39da1182b": 2, "523b5ef3-0b10-4154-8b62-10b2ebd00921": 1, "529de4f7-b6c4-4c76-b36d-c511c9328ebe": 138, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 1, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": 1, "96defd79-4015-4a32-bd09-794ff72183ef": 5, "CleanupCounts": 1, "OpenFirstTime": 1684247859, "cd4688a9-e888-48ea-ad81-76193d56b1be": 13}, "hub_cleanup_candidate_list_for_debug": [{"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}], "hub_cleanup_context": {"cleanup_last_time_v3": 1724126881.971102, "show_days": "00000110101111111111101111011111", "sidebar_autohide_by_cleanup": true, "sidebar_show_last_time": 3712084}, "hub_cleanup_context_v2": {"cleanup_debug_info_v2_adjusted_engaged_app_count": 0, "cleanup_debug_info_v2_app_count_threshold": 1, "cleanup_debug_info_v2_current_sidebar_visibility": 0, "cleanup_debug_info_v2_discover_icon_enabled": true, "cleanup_debug_info_v2_dwell_time_in_secs": 10, "cleanup_debug_info_v2_engaged_app_count": 0, "cleanup_debug_info_v2_expected_sidebar_visibility": 0, "cleanup_debug_info_v2_is_tower_off_by_user": false, "cleanup_debug_info_v2_skip_user_generated_apps_for_threshold": true, "cleanup_debug_info_v2_user_generated_app_count": 0, "hub_app_cleanup_v2_done": true}, "recent_theme_color_list": [4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0], "show_edge_split_window_toolbar_button": false, "show_hub_app_in_sidebar_buttons": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": 3, "168a2510-04d5-473e-b6a0-828815a7ca5f": 1, "1ec8a5a9-971c-4c82-a104-5e1a259456b8": 1, "2354565a-f412-4654-b89c-f92eaa9dbd20": 0, "2caf0cf4-ea42-4083-b928-29b39da1182b": 3, "523b5ef3-0b10-4154-8b62-10b2ebd00921": 3, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": 3, "76b926d6-3738-46bf-82d7-2ab896ddf70b": 3, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": 3, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": 3, "96defd79-4015-4a32-bd09-794ff72183ef": 3, "9ce3c9c2-462f-4cc9-bbd7-57d656445be0": 3, "cd4688a9-e888-48ea-ad81-76193d56b1be": 0, "d304175e-bbd4-4b66-8839-9627e56f391f": 0, "d3ff4c56-a2b8-4673-ad13-35e7706cc9d1": 1, "da15ec1d-543d-41c9-94b8-eb2bd060f2c7": 1, "dadd1f1c-380c-4871-9e09-7971b6b15069": 3, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": 1, "ec843dfc-b28b-4030-9fb9-edcf38942a5e": 1}, "show_hub_app_in_sidebar_buttons_legacy": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": 3, "168a2510-04d5-473e-b6a0-828815a7ca5f": 1, "1ec8a5a9-971c-4c82-a104-5e1a259456b8": 1, "2354565a-f412-4654-b89c-f92eaa9dbd20": 0, "2caf0cf4-ea42-4083-b928-29b39da1182b": 3, "523b5ef3-0b10-4154-8b62-10b2ebd00921": 3, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": 3, "76b926d6-3738-46bf-82d7-2ab896ddf70b": 3, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": 3, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": 3, "96defd79-4015-4a32-bd09-794ff72183ef": 3, "9ce3c9c2-462f-4cc9-bbd7-57d656445be0": 3, "cd4688a9-e888-48ea-ad81-76193d56b1be": 0, "d3ff4c56-a2b8-4673-ad13-35e7706cc9d1": 1, "da15ec1d-543d-41c9-94b8-eb2bd060f2c7": 1, "dadd1f1c-380c-4871-9e09-7971b6b15069": 3, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": 1}, "show_hub_app_in_sidebar_buttons_legacy_update_time": "13392632082125534", "show_hub_apps_tower_pinned": false, "show_toolbar_collections_button": false, "time_of_last_normal_window_close": "13392632238519911", "toolbar_browser_essentials_button_pinned": false, "underside_chat_bing_signed_in_status": false, "underside_chat_consent": 0, "user_level_features_context": {}, "window_placement": {"bottom": 808, "left": 8, "maximized": true, "right": 1060, "top": 8, "work_area_bottom": 816, "work_area_left": 0, "work_area_right": 1536, "work_area_top": 0}}, "browser_content_container_height": 742, "browser_content_container_width": 1536, "browser_content_container_x": 0, "browser_content_container_y": 74, "browser_essentials": {"show_hub_fre": false, "show_safety_fre": false}, "collections": {"prism_collection": {"enroll": {"rule_version": 1, "state": 2}}, "prism_collections": {"enabled": 0, "migration": {"accepted": true, "completed": 2, "item_count": 0}, "policy": {"cached": 0}, "wns": {"last_subscribe_time": "13392628646051373", "subscription_id": "1;2288348822021839173"}}}, "commerce_daily_metrics_last_update_time": "13392644725275792", "copilot_vision": {"user_access": true}, "countryid_at_install": 18766, "credentials_enable_breachdetection": true, "custom_links": {"list": []}, "default_search_provider": {"guid": ""}, "devtools": {"preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "dual_engine": {"consumer_site_list_with_ie_entries": false, "consumer_sitelist_location": "", "consumer_sitelist_version": "", "external_consumer_shared_cookie_data": {}, "shared_cookie_data": {}, "sitelist_has_consumer_data": false, "sitelist_has_enterprise_data": false, "sitelist_location": "", "sitelist_source": 0, "sitelist_version": "", "user_list_data_1": {}}, "edge": {"account_type": 1, "bookmarks": {"last_dup_info_record_time": "*****************"}, "msa_sso_info": {"allow_for_non_msa_profile": false}, "profile_matches_os_primary_account": false, "profile_sso_info": {"aad_sso_algo_state": 1, "is_first_profile": true, "is_msa_first_profile": true, "msa_sso_algo_state": 2, "msa_sso_state_reached_by": 3}, "profile_sso_option": 1, "services": {"last_gaia_id": "0003BFFDA06E3E04", "signin_scoped_device_id": "d4cad404-a55b-4a72-bd0e-8e9e55ecfcc6"}, "workspaces": {"state": "{\"edgeWorkspacePrefsVersion\":2,\"enableFluid\":true,\"failedRestartSpaceId\":\"\",\"failedToConnectToFluid\":false,\"fluidStatus\":0,\"fre_shown\":false,\"fromCache\":false,\"isFluidPreferencesConnected\":false,\"isSpaceOpening\":false,\"openingSpaceId\":\"\",\"statusForScreenReaders\":\"\",\"workspacePopupMode\":0,\"workspacesForExternalLinks\":[]}", "storage": {"state": "{\"app_folder_path\":\"\",\"container_id\":\"\",\"drive_id\":\"\",\"prefs_item_id\":\"\",\"storage_endpoint\":\"\",\"version\":3}"}}}, "edge_cloud_messaging": {"cached_target_token": {"cv": "978920986306937600", "target_token": "B/2oeVdwkuiUJAmPH0IlYw==$JoapWqcyig8cf/pa7vRjDOs/p6xhPjnHgYoMdB7i8JYlr44YxDx5PEZ5/2EVQjbrn9uxdAYO6psvLw0k8cxfzWPaSsPCAsYL1KDnRFywIrAxduAcGxh+Kh+L6CrGeVCeOrbacyhx8jr5f1AoFfrH4IWg8mlYqOGFbYN2zk7OhfCyxnyUmylku3a6XlAsR+qM", "time": "13392631350309052"}}, "edge_rewards": {"cache_data": "CAEQ4AMYAEoCaW4=", "hva_promotions": [], "promotions": [{"attributes": {"State": "<PERSON><PERSON><PERSON>", "activityprogress": "0", "animated_icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/newEdgeLogo.json", "bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_small.png", "complete": "False", "description": "Search here for 3 days and earn an extra 3,100 points.", "destination": "", "edgebar_description": "", "edgebar_disclaimer": "Offer valid for 1 person/account within 7 days of joining the challenge", "edgebar_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Microsoft_giftcard_grey.png", "edgebar_link_text": "Get started", "edgebar_title": "Welcome to search bar powered by Microsoft Edge! Get a free gift card when you search here for 3 days.", "edgebar_type": "eligible", "give_eligible": "False", "icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/newEdgeLogo.json", "image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/278x180/Star-magenta-278x180px.png", "link_text": "Get started", "max": "0", "offerid": "eligibility_EdgeBarMicrosoft_202211_ML293H", "progress": "0", "promotional": "0", "sc_bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_medium.png", "sc_bg_large_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_large.png", "small_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Mobile/newEdgeLogo_75x75.png", "title": "Earn bonus Microsoft Rewards points", "type": "url<PERSON><PERSON>"}, "name": "ENIN_eligibility_EdgeBarMicrosoft_202211_ML293H_info", "priority": -1, "tags": ["exclude_give_pcparent", "non_global_config"]}], "refresh_status_muted_until": "13393233394995893"}, "edge_ux_config": {"assignmentcontext": "lq24GDr7K5A06i4TLYvRPLkgku1mJ5OELMNaXQm5ALQ=", "dataversion": "254461102", "experimentvariables": {"2f717976": {"edgeServerUX.sync.historyDataTypeEnabled": true}, "shop-75th99": {"edgeServerUX.shopping.aablockth": 75, "edgeServerUX.shopping.block99": true}, "shopppdismisstreatment": {"edgeServerUX.shopping.msEdgeShoppingCashbackDismissTimeout2s": true}, "shopprodinsights-c": {"edgeServerUX.shopping.enablePhInsightsV2": false}, "shoprevenuattributiont": {"edgeServerUX.shopping.disableCashbackOnCouponCopy": true}}, "flights": {"2f717976": "31213786", "shop-75th99": "31271456", "shopppdismisstreatment": "31004791", "shopprodinsights-c": "31303590", "shoprevenuattributiont": "31235887"}, "latestcorrelationid": "Ref A: 28F0E02984D94633BD82E6B11FD96427 Ref B: DEL01EDGE0408 Ref C: 2025-05-25T07:32:42Z"}, "edge_wallet": {"passwords": {"password_lost_report_date": "13392544797449328"}, "trigger_funnel": {"records": []}}, "enterprise_profile_guid": "503d0e75-522b-4d6c-b8d8-df8264dc6c53", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "136.0.7103.114", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": [], "ui": {"allow_chrome_webstore": true}}, "family_safety": {"activity_reporting_enabled": false, "web_filtering_enabled": false}, "fsd": {"retention_policy_last_version": 136}, "gaia_cookie": {"changed_time": **********.780307, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"consented_to_sync": false, "signin": {"LAST_SIGNIN_ACCESS_POINT": {"time": "2025-05-25T06:36:35.251Z", "value": "17"}}, "signin_scoped_device_id": "5a94eb7f-f17f-4109-8c97-abc01c2879c4"}}, "history": {"thumbnail_visibility": true, "thumbnail_visibility_per_usage": true}, "history_clusters": {"all_cache": {"all_keywords": {}, "all_timestamp": "0"}, "short_cache": {"short_keywords": {}, "short_timestamp": "0"}}, "import_items_failure_state": {"reimport": {"ie_react": 62432}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "13392554408117080", "recent_session_start_times": ["13392632267427467", "13392554408117080"], "session_last_active_time": "13392646301092756", "session_start_time": "13392632267427467"}, "intl": {"accept_languages": "en-US,en,en-IN", "selected_languages": "en-US,en,en-IN"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "local_browser_data_share": {"index_last_cleaned_time": "13392628655214087", "pin_recommendations_eligible": false}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "46cxZ5VEH/b4qrAG8zPc/ydWoQ0wV/59OcMa4Zb5DMRAAfCP1GLwLuyA8Q34NDEv/bvdRere47GmjBWdkhO2Ng=="}, "muid": {"last_sync": "13392628595145565", "values_seen": ["0B113725B07168B13C9D22D2B1776938", "34E76EA8905968523CC97B5F91AB6958", "361828B30A5E689400B33D440BAC69FA"]}, "ntp": {"background_image_type": "imageAndVideo", "feed_engagement_time": "13392628432745632", "layout_mode": 2, "news_feed_display": "always", "next_site_suggestions_available": false, "num_personal_suggestions": 2, "record_user_choices": [{"setting": "tscollapsed", "source": "tscollapsed_to_off", "timestamp": 1695212032291.0, "value": 0}, {"setting": "seen_interest_fre_count", "source": "ntp", "timestamp": 1702443747810.0, "value": 3}, {"setting": "breaking_news_dismissed", "source": "ntp", "timestamp": 1748154831981.0, "value": {}}], "show_greeting": true}, "nurturing": {"time_of_last_sync_consent_view": "13392628595262294"}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "13392646304071602", "last_fetch_success": "13392646304298112"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "EWALLET_MERCHANT_ALLOWLIST": true, "FORMS_ANNOTATIONS": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": true}, "personalization_data_consent": {"how_set": 4, "personalization_in_context_consent_can_prompt": false, "personalization_in_context_count": 0, "when_set": "*****************"}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:51894,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:52163,*": {"expiration": "13400421961152419", "last_modified": "13392645961152424", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:52355,*": {"expiration": "13400422269208598", "last_modified": "13392646269208605", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:52592,*": {"expiration": "13400422839127483", "last_modified": "13392646839127490", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:58801,*": {"expiration": "13400330471691441", "last_modified": "13392554471691449", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:60373,*": {"expiration": "13400405720948814", "last_modified": "13392629720948819", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:61485,*": {"expiration": "13400407763309333", "last_modified": "13392631763309338", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:61816,*": {"expiration": "13400408238516862", "last_modified": "13392632238516867", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62136,*": {"expiration": "13400410170353769", "last_modified": "13392634170353777", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62711,*": {"expiration": "13400407696879292", "last_modified": "13392631696879299", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62742,*": {"expiration": "13400420766009837", "last_modified": "13392644766009843", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63062,*": {"expiration": "13400421100224691", "last_modified": "13392645100224698", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63173,*": {"expiration": "13400421195640776", "last_modified": "13392645195640784", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {"https://ukpsc.net.in:443,*": {"expiration": "0", "last_modified": "13288105279776783", "model": 0, "setting": 1}}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"http://localhost:51894,*": {"last_modified": "13392645682353727", "setting": {"lastEngagementTime": 1.3392645682353684e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:52163,*": {"last_modified": "13392645927040324", "setting": {"lastEngagementTime": 1.3392645927040318e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:52355,*": {"last_modified": "13392646067696609", "setting": {"lastEngagementTime": 1.3392646067696604e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:52592,*": {"last_modified": "13392646828615596", "setting": {"lastEngagementTime": 1.339264682861558e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:58801,*": {"last_modified": "13392628595089495", "setting": {"lastEngagementTime": 1.3392599795089464e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.2}}, "http://localhost:60373,*": {"last_modified": "13392629693392970", "setting": {"lastEngagementTime": 1.3392629693392956e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.3999999999999995, "rawScore": 5.3999999999999995}}, "http://localhost:61485,*": {"last_modified": "13392631718367911", "setting": {"lastEngagementTime": 1.33926317183679e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:61816,*": {"last_modified": "13392631962210669", "setting": {"lastEngagementTime": 1.3392631962210652e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:62136,*": {"last_modified": "13392632267721660", "setting": {"lastEngagementTime": 1.339263226772165e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:62711,*": {"last_modified": "13392631349600025", "setting": {"lastEngagementTime": 1.339263134960001e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:62742,*": {"last_modified": "13392644737521240", "setting": {"lastEngagementTime": 1.339264473752122e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:63062,*": {"last_modified": "13392645086410705", "setting": {"lastEngagementTime": 1.3392645086410692e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.2, "rawScore": 4.2}}, "http://localhost:63173,*": {"last_modified": "13392645166064239", "setting": {"lastEngagementTime": 1.3392645166064226e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "136.0.7103.114", "creation_time": "13392554408030166", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_profile_id": "f5995894-a38b-4938-ab7d-4de0d633a99e", "edge_user_with_non_zero_passwords": true, "exit_type": "Normal", "family_member_role": "not_in_family", "hard_yes_password_monitor_consent": true, "is_relative_to_aad": false, "last_engagement_time": "13392646828615580", "last_time_obsolete_http_credentials_removed": 1748080868.097477, "last_time_password_monitor_consent_shown": "13285067729068742", "last_time_password_store_metrics_reported": 1748171155.16536, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chrome", "network_pbs": {"c2faa7c1": {"last_updated": "13392544777709469", "pb": 6}}, "number_password_monitor_consent_shown": 1, "observed_session_time": {"feedback_rating_in_product_help_observed_session_time_key_136.0.3240.92": 293.0}, "password_breach_last_scanned_error": 0, "password_breach_last_scanned_time": "13392632009130425", "password_breach_scan_trigger_last_reset_time": "13339771245395414", "password_breach_scan_triggered_count": 0, "password_breach_scan_triggered_password_count": 0, "password_breach_scanned": true, "password_hash_data_list": [], "using_default_avatar": false, "using_gaia_avatar": true, "were_old_google_logins_removed": true}, "reading_view": {"last_access_time": "13285333733832602"}, "reset_prepopulated_engines": false, "safebrowsing": {"advanced_protection_last_refresh": "13392631962065606", "event_timestamps": {}, "extension_telemetry_file_data": {}, "hash_real_time_ohttp_expiration_time": "13392813608557679", "hash_real_time_ohttp_key": "0QAgQZygJtLf6q/rDOcCngz5gv7t6Y52KvDoA00aTin0d0wABAABAAI=", "metrics_last_log_time": "13392644725", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "CuUCChFjcm9zc19kZXZpY2VfdXNlchLPAgrDAg0AAIA/EJaBhb6hkeUXGrACCqcCGqQCChkNAACAPxISTm9Dcm9zc0RldmljZVVzYWdlChgNAAAAQBIRQ3Jvc3NEZXZpY2VNb2JpbGUKGQ0AAEBAEhJDcm9zc0RldmljZURlc2t0b3AKGA0AAIBAEhFDcm9zc0RldmljZVRhYmxldAoiDQAAoEASG0Nyb3NzRGV2aWNlTW9iaWxlQW5kRGVza3RvcAohDQAAwEASGkNyb3NzRGV2aWNlTW9iaWxlQW5kVGFibGV0CiINAADgQBIbQ3Jvc3NEZXZpY2VEZXNrdG9wQW5kVGFibGV0CiANAAAAQRIZQ3Jvc3NEZXZpY2VBbGxEZXZpY2VUeXBlcwoXDQAAEEESEENyb3NzRGV2aWNlT3RoZXISEk5vQ3Jvc3NEZXZpY2VVc2FnZRIEEAcYBCACEKSChb6hkeUXClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQpqrIg9GO5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEOKqyIPRjuUX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13392518399000000", "uma_in_sql_start_time": "13392554408089400"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392631763301663", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392631961941867", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392632238510215", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392632267383138", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392634170348053", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392644725162725", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392644766003660", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392645065651872", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392645100219140", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392645134418494", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392645195635804", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392645636510858", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392645893618190", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392645926776262", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392645961147560", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392646067431385", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392646269202901", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "shopping": {"contextual_features_enabled": true, "dma_telemetry_expiration_time": "*****************", "last_pwilo_api_fetch_time": "*****************", "pcb_supported": true}, "should_read_incoming_syncing_theme_prefs": false, "signin": {"accounts_metadata_dict": {}, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true, "signin_with_explicit_browser_signin_on": true, "sync_paused_start_time": "*****************"}, "smart_explore": {"auto_cleanup": {"check_time": "*****************", "noengagement_since": "*****************"}, "auto_cleanup_date": "*****************"}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "surf_game": {"buoy_highscore": -1, "classic_highscore": 1599, "speed_highscore": -1}, "sync": {"apps": true, "autofill": true, "bookmarks": true, "collections": true, "collections_edge_re_evaluated": true, "collections_edge_supported": true, "data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "edge_account_type": 1, "edge_wallet": true, "edge_wallet_edge_supported": true, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": true, "extensions_edge_supported": true, "feature_status_for_sync_to_signin": 5, "has_been_enabled": true, "history_edge_supported": true, "keep_everything_synced": true, "keystore_encryption_key_state": "************************************************************************************************************************************************************************************************************************************************************************************", "local_device_guids_with_timestamp": [{"cache_guid": "guTtJfs/n3R5DDOOTGlngg==", "timestamp": 155007}], "passwords": true, "passwords_per_account_pref_migration_done": true, "preferences": true, "tabs": true, "tabs_edge_supported": true, "transport_data_per_account": {}, "typed_urls": true}, "sync_consent_recorded": true, "sync_profile_info": {"edge_ci_consent_last_modified_date": "*****************", "edge_ci_consent_last_shown_date": "*****************", "edge_ci_is_option_explicitly_selectedby_user": false, "edge_san_consent_last_modified_date": "*****************", "edge_san_consent_last_shown_date": "*****************", "edge_san_is_option_explicitly_selectedby_user": false}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "tab_groups": [], "tab_groups_migration_version": 3, "third_party_search": {"consented": true}, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "toolbar_declutter": {"new_user_cleanup_triggered": true, "undo": {"last_time": "*****************"}}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "tracking_prevention": {"strict_inprivate": false}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled": true, "personalization_data_consent_enabled_last_known_value": true}, "video_enhancement": {"mode": "Non-AI enhancement"}, "visual_search": {"dma_state": 1}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "136", "link_handling_info": {"enabled_for_installed_apps": true}}}