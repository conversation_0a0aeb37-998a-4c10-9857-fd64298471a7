import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../shared_kernel/theme/app_theme.dart';
import '../services/tts_service.dart';

/// Widget that displays the end of a story with options
class StoryEndScreen extends StatefulWidget {
  /// The moral lesson of the story
  final String? moralLesson;

  /// The TTS service
  final TTSService ttsService;

  /// Callback when the user wants to try the last choice again
  final VoidCallback onTryLastChoiceAgain;

  /// Callback when the user wants to read the story again
  final VoidCallback onReadStoryAgain;

  /// Callback when the user wants to find a new story
  final VoidCallback onFindNewStory;

  /// Constructor
  const StoryEndScreen({
    super.key,
    this.moralLesson,
    required this.ttsService,
    required this.onTryLastChoiceAgain,
    required this.onReadStoryAgain,
    required this.onFindNewStory,
  });

  @override
  State<StoryEndScreen> createState() => _StoryEndScreenState();
}

class _StoryEndScreenState extends State<StoryEndScreen> {
  @override
  void initState() {
    super.initState();
    _narrateEnding();
  }

  @override
  void dispose() {
    widget.ttsService.stop();
    super.dispose();
  }

  /// Narrate the ending
  Future<void> _narrateEnding() async {
    // Narrate "The End"
    await widget.ttsService.speak('The End');

    // If there's a moral lesson, narrate it after a pause
    if (widget.moralLesson != null && widget.moralLesson!.isNotEmpty) {
      await Future.delayed(const Duration(seconds: 1));
      await widget.ttsService.speak(widget.moralLesson!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 10,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // "The End" text
            Text(
              'The End',
              style: AppTheme.headingStyle.copyWith(
                fontSize: 32,
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            )
            .animate()
            .scale(
              duration: const Duration(milliseconds: 500),
              curve: Curves.elasticOut,
            )
            .shimmer(
              duration: const Duration(milliseconds: 1500),
              color: Colors.yellow.withValues(alpha: 0.5),
            ),

            const SizedBox(height: 16),

            // Moral lesson (if any)
            if (widget.moralLesson != null && widget.moralLesson!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Text(
                  widget.moralLesson!,
                  style: AppTheme.bodyStyle.copyWith(
                    fontSize: 18,
                    fontStyle: FontStyle.italic,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                )
                .animate()
                .fadeIn(
                  delay: const Duration(milliseconds: 1000),
                  duration: const Duration(milliseconds: 1000),
                ),
              ),

            const SizedBox(height: 32),

            // Options
            ..._buildOptions()
                .animate(interval: const Duration(milliseconds: 200))
                .fadeIn(
                  duration: const Duration(milliseconds: 500),
                  delay: const Duration(milliseconds: 1500),
                )
                .slideY(
                  begin: 0.2,
                  end: 0,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeOut,
                ),
          ],
        ),
      ),
    );
  }

  /// Build the options buttons
  List<Widget> _buildOptions() {
    return [
      // Try that choice again
      _buildOptionButton(
        icon: Icons.replay,
        text: 'Try That Choice Again',
        onPressed: widget.onTryLastChoiceAgain,
        color: Colors.orange,
      ),

      const SizedBox(height: 16),

      // Read this story again
      _buildOptionButton(
        icon: Icons.refresh,
        text: 'Read This Story Again',
        onPressed: widget.onReadStoryAgain,
        color: Colors.green,
      ),

      const SizedBox(height: 16),

      // Find a new story
      _buildOptionButton(
        icon: Icons.menu_book,
        text: 'Find a New Story',
        onPressed: widget.onFindNewStory,
        color: AppTheme.primaryColor,
      ),
    ];
  }

  /// Build an option button
  Widget _buildOptionButton({
    required IconData icon,
    required String text,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 24),
      label: Text(
        text,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(
          horizontal: 24,
          vertical: 16,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        minimumSize: const Size(double.infinity, 60),
      ),
    );
  }
}
