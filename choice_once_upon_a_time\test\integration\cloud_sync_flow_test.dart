import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../lib/main.dart' as app;
import '../helpers/test_helpers.dart';
import '../mocks/mock_services.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Cloud Sync Flow Integration Tests', () {
    late MockFirebaseAuthService mockAuthService;

    setUpAll(() {
      mockAuthService = MockFirebaseAuthService();
    });

    setUp(() {
      // Set up SharedPreferences mock
      SharedPreferences.setMockInitialValues({});
      
      // Clear Firestore mock
      MockFirestore.clear();
    });

    testWidgets('Initial sync after sign-in', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Initial sync after sign-in',
        () async {
          // Set up cloud data
          const childId = 'test-child-1';
          MockFirestore.setDocument(
            'users/test-user-id',
            TestDataFactory.createUserData(
              children: [TestDataFactory.createChildData(id: childId)],
            ),
          );

          MockFirestore.setDocument(
            'children/$childId',
            TestDataFactory.createChildData(
              id: childId,
              storyProgress: {
                'story1': TestDataFactory.createStoryProgressData(
                  storyId: 'story1',
                  currentSceneId: 'scene2',
                ),
              },
            ),
          );

          // Start with unauthenticated state
          mockAuthService.setAuthState(isSignedIn: false);

          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Should be at auth screen
          expect(find.text('Sign In'), findsOneWidget);

          // Sign in
          await tester.enterText(
            find.byKey(const Key('email_field')),
            '<EMAIL>',
          );
          await tester.enterText(
            find.byKey(const Key('password_field')),
            'password123',
          );

          await tester.tap(find.text('Sign In'));
          await tester.pumpAndSettle();

          // Select child
          await tester.tap(find.textContaining('test-child'));
          await tester.pumpAndSettle();

          // Should be at main menu with sync indicator
          expect(find.text('New Story'), findsOneWidget);
          expect(find.byIcon(Icons.cloud_done), findsOneWidget);

          // Should show continue story option (from synced progress)
          expect(find.text('Continue Story'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Offline progress sync to cloud', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Offline progress sync to cloud',
        () async {
          // Set up local progress data
          SharedPreferences.setMockInitialValues({
            'story_progress_test-child-1': '''
              {
                "storyId": "offline-story",
                "sceneId": "scene3",
                "timestamp": ${DateTime.now().millisecondsSinceEpoch},
                "choiceHistory": ["choice1", "choice2"]
              }
            ''',
          });

          // Set up authenticated state
          mockAuthService.setAuthState(
            isSignedIn: true,
            userEmail: '<EMAIL>',
            userId: 'test-user-id',
          );

          MockFirestore.setDocument(
            'users/test-user-id',
            TestDataFactory.createUserData(
              children: [TestDataFactory.createChildData(id: 'test-child-1')],
            ),
          );

          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Select child
          await tester.tap(find.textContaining('test-child'));
          await tester.pumpAndSettle();

          // Should show sync indicator
          expect(find.byIcon(Icons.cloud_done), findsOneWidget);

          // Trigger manual sync
          await tester.tap(find.byIcon(Icons.cloud_done));
          await tester.pumpAndSettle();

          // Should show sync success message
          expect(find.text('Sync completed successfully'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Conflict resolution during sync', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Conflict resolution during sync',
        () async {
          const childId = 'test-child-1';
          
          // Set up local progress (newer)
          final localTimestamp = DateTime.now().millisecondsSinceEpoch;
          SharedPreferences.setMockInitialValues({
            'story_progress_$childId': '''
              {
                "storyId": "conflict-story",
                "sceneId": "scene5",
                "timestamp": $localTimestamp,
                "choiceHistory": ["choice1", "choice2", "choice3"]
              }
            ''',
          });

          // Set up cloud progress (older)
          final cloudTimestamp = DateTime.now().subtract(const Duration(hours: 1)).millisecondsSinceEpoch;
          MockFirestore.setDocument(
            'users/test-user-id',
            TestDataFactory.createUserData(
              children: [TestDataFactory.createChildData(id: childId)],
            ),
          );

          MockFirestore.setDocument(
            'children/$childId',
            TestDataFactory.createChildData(
              id: childId,
              storyProgress: {
                'conflict-story': {
                  'storyId': 'conflict-story',
                  'currentSceneId': 'scene3',
                  'lastUpdated': DateTime.fromMillisecondsSinceEpoch(cloudTimestamp).toIso8601String(),
                  'choicesMade': {'choice1': 'choice1', 'choice2': 'choice2'},
                  'completedScenes': ['scene1', 'scene2'],
                  'isCompleted': false,
                  'totalTimeMinutes': 10,
                },
              },
            ),
          );

          // Set up authenticated state
          mockAuthService.setAuthState(
            isSignedIn: true,
            userEmail: '<EMAIL>',
            userId: 'test-user-id',
          );

          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Select child
          await tester.tap(find.textContaining('test-child'));
          await tester.pumpAndSettle();

          // Trigger sync
          await tester.tap(find.byIcon(Icons.cloud_done));
          await tester.pumpAndSettle();

          // Should resolve conflict (local should win as it's newer)
          expect(find.text('Sync completed successfully'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Sync failure handling', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Sync failure handling',
        () async {
          // Set up authenticated state
          mockAuthService.setAuthState(
            isSignedIn: true,
            userEmail: '<EMAIL>',
            userId: 'test-user-id',
          );

          MockFirestore.setDocument(
            'users/test-user-id',
            TestDataFactory.createUserData(
              children: [TestDataFactory.createChildData()],
            ),
          );

          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Select child
          await tester.tap(find.textContaining('test-child'));
          await tester.pumpAndSettle();

          // Simulate network error by clearing Firestore data
          MockFirestore.clear();

          // Trigger sync
          await tester.tap(find.byIcon(Icons.cloud_done));
          await tester.pumpAndSettle();

          // Should show error message
          expect(find.textContaining('Sync failed'), findsOneWidget);

          // Should show retry option
          expect(find.byIcon(Icons.cloud_off), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Automatic sync during story progress', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Automatic sync during story progress',
        () async {
          // Set up authenticated state
          mockAuthService.setAuthState(
            isSignedIn: true,
            userEmail: '<EMAIL>',
            userId: 'test-user-id',
          );

          MockFirestore.setDocument(
            'users/test-user-id',
            TestDataFactory.createUserData(
              children: [TestDataFactory.createChildData()],
            ),
          );

          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Select child
          await tester.tap(find.textContaining('test-child'));
          await tester.pumpAndSettle();

          // Start a story
          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.text('Test Adventure'));
          await tester.pumpAndSettle();

          // Make progress in story
          await tester.tap(find.text('Take the left path'));
          await tester.pumpAndSettle();

          // Should automatically sync progress
          expect(find.byIcon(Icons.cloud_done), findsOneWidget);

          // Exit story
          await tester.tap(find.byIcon(Icons.arrow_back));
          await tester.pumpAndSettle();

          // Should show continue story option
          expect(find.text('Continue Story'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Offline mode handling', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Offline mode handling',
        () async {
          // Set up authenticated state but simulate offline
          mockAuthService.setAuthState(
            isSignedIn: true,
            userEmail: '<EMAIL>',
            userId: 'test-user-id',
          );

          // Don't set up Firestore data to simulate offline
          
          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Should show offline indicator
          expect(find.byIcon(Icons.cloud_off), findsOneWidget);
          expect(find.text('Offline'), findsOneWidget);

          // Should still be able to use app
          expect(find.text('New Story'), findsOneWidget);

          // Start a story
          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          // Should work offline
          expect(find.textContaining('Test Adventure'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Migration from legacy data', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Migration from legacy data',
        () async {
          // Set up legacy local data
          SharedPreferences.setMockInitialValues({
            'story_progress_legacy-child': '''
              {
                "storyId": "legacy-story",
                "sceneId": "legacy-scene",
                "timestamp": ${DateTime.now().millisecondsSinceEpoch},
                "choiceHistory": ["legacy-choice1"]
              }
            ''',
            'migration_version': 0, // Indicates migration needed
          });

          // Set up authenticated state
          mockAuthService.setAuthState(
            isSignedIn: true,
            userEmail: '<EMAIL>',
            userId: 'test-user-id',
          );

          MockFirestore.setDocument(
            'users/test-user-id',
            TestDataFactory.createUserData(
              children: [TestDataFactory.createChildData(id: 'legacy-child')],
            ),
          );

          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Select child
          await tester.tap(find.textContaining('legacy-child'));
          await tester.pumpAndSettle();

          // Migration should happen automatically
          // Should show sync indicator after migration
          expect(find.byIcon(Icons.cloud_done), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Sync status indicator states', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Sync status indicator states',
        () async {
          // Set up authenticated state
          mockAuthService.setAuthState(
            isSignedIn: true,
            userEmail: '<EMAIL>',
            userId: 'test-user-id',
          );

          MockFirestore.setDocument(
            'users/test-user-id',
            TestDataFactory.createUserData(
              children: [TestDataFactory.createChildData()],
            ),
          );

          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Select child
          await tester.tap(find.textContaining('test-child'));
          await tester.pumpAndSettle();

          // Should show synced state
          expect(find.byIcon(Icons.cloud_done), findsOneWidget);
          expect(find.text('Synced'), findsOneWidget);

          // Trigger sync to see syncing state
          await tester.tap(find.byIcon(Icons.cloud_done));
          await tester.pump(); // Don't settle to catch intermediate state

          // Should show syncing state
          expect(find.byIcon(Icons.sync), findsOneWidget);
          expect(find.text('Syncing...'), findsOneWidget);

          // Wait for sync to complete
          await tester.pumpAndSettle();

          // Should return to synced state
          expect(find.byIcon(Icons.cloud_done), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Multiple device sync scenario', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Multiple device sync scenario',
        () async {
          const childId = 'multi-device-child';
          
          // Simulate progress from another device (cloud has newer data)
          final cloudTimestamp = DateTime.now().millisecondsSinceEpoch;
          MockFirestore.setDocument(
            'users/test-user-id',
            TestDataFactory.createUserData(
              children: [TestDataFactory.createChildData(id: childId)],
            ),
          );

          MockFirestore.setDocument(
            'children/$childId',
            TestDataFactory.createChildData(
              id: childId,
              storyProgress: {
                'multi-device-story': {
                  'storyId': 'multi-device-story',
                  'currentSceneId': 'scene10',
                  'lastUpdated': DateTime.fromMillisecondsSinceEpoch(cloudTimestamp).toIso8601String(),
                  'choicesMade': {
                    'choice1': 'choice1',
                    'choice2': 'choice2',
                    'choice3': 'choice3',
                  },
                  'completedScenes': ['scene1', 'scene2', 'scene3', 'scene4', 'scene5'],
                  'isCompleted': false,
                  'totalTimeMinutes': 25,
                },
              },
            ),
          );

          // Set up local progress (older)
          final localTimestamp = DateTime.now().subtract(const Duration(hours: 2)).millisecondsSinceEpoch;
          SharedPreferences.setMockInitialValues({
            'story_progress_$childId': '''
              {
                "storyId": "multi-device-story",
                "sceneId": "scene5",
                "timestamp": $localTimestamp,
                "choiceHistory": ["choice1", "choice2"]
              }
            ''',
          });

          // Set up authenticated state
          mockAuthService.setAuthState(
            isSignedIn: true,
            userEmail: '<EMAIL>',
            userId: 'test-user-id',
          );

          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Select child
          await tester.tap(find.textContaining('multi-device-child'));
          await tester.pumpAndSettle();

          // Should sync and use cloud data (newer)
          expect(find.byIcon(Icons.cloud_done), findsOneWidget);

          // Should show continue story with cloud progress
          expect(find.text('Continue Story'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Sync accessibility', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Sync accessibility',
        () async {
          // Set up authenticated state
          mockAuthService.setAuthState(
            isSignedIn: true,
            userEmail: '<EMAIL>',
            userId: 'test-user-id',
          );

          MockFirestore.setDocument(
            'users/test-user-id',
            TestDataFactory.createUserData(
              children: [TestDataFactory.createChildData()],
            ),
          );

          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Select child
          await tester.tap(find.textContaining('test-child'));
          await tester.pumpAndSettle();

          // Test accessibility of sync indicator
          await TestHelpers.verifyAccessibility(tester);

          // Test accessibility during sync
          await tester.tap(find.byIcon(Icons.cloud_done));
          await tester.pump();

          await TestHelpers.verifyAccessibility(tester);
        },
      );
    });
  });
}
