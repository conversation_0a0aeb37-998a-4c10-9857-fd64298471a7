import 'package:flutter/foundation.dart';

/// Provider for managing story interaction settings
class StorySettingsProvider extends ChangeNotifier {
  // Panel transparency (0.0 = fully transparent, 1.0 = fully opaque)
  double _panelOpacity = 0.8;
  
  // Font size for subtitle text
  double _fontSize = 18.0;
  
  // TTS speech rate (0.0 = very slow, 1.0 = very fast)
  double _speechRate = 0.5;
  
  // TTS volume (0.0 = silent, 1.0 = full volume)
  double _volume = 1.0;
  
  // Font family for subtitle text
  String _fontFamily = 'Comic Sans MS';
  
  // Auto-advance to next scene
  bool _autoAdvance = true;
  
  // Auto-advance delay in milliseconds
  int _autoAdvanceDelay = 1000;
  
  // Getters
  double get panelOpacity => _panelOpacity;
  double get fontSize => _fontSize;
  double get speechRate => _speechRate;
  double get volume => _volume;
  String get fontFamily => _fontFamily;
  bool get autoAdvance => _autoAdvance;
  int get autoAdvanceDelay => _autoAdvanceDelay;
  
  // Setters
  void setPanelOpacity(double opacity) {
    if (opacity >= 0.0 && opacity <= 1.0) {
      _panelOpacity = opacity;
      notifyListeners();
    }
  }
  
  void setFontSize(double size) {
    if (size >= 12.0 && size <= 32.0) {
      _fontSize = size;
      notifyListeners();
    }
  }
  
  void setSpeechRate(double rate) {
    if (rate >= 0.1 && rate <= 1.0) {
      _speechRate = rate;
      notifyListeners();
    }
  }
  
  void setVolume(double vol) {
    if (vol >= 0.0 && vol <= 1.0) {
      _volume = vol;
      notifyListeners();
    }
  }
  
  void setFontFamily(String family) {
    _fontFamily = family;
    notifyListeners();
  }
  
  void setAutoAdvance(bool enabled) {
    _autoAdvance = enabled;
    notifyListeners();
  }
  
  void setAutoAdvanceDelay(int delay) {
    if (delay >= 500 && delay <= 5000) {
      _autoAdvanceDelay = delay;
      notifyListeners();
    }
  }
  
  /// Reset all settings to defaults
  void resetToDefaults() {
    _panelOpacity = 0.8;
    _fontSize = 18.0;
    _speechRate = 0.5;
    _volume = 1.0;
    _fontFamily = 'Comic Sans MS';
    _autoAdvance = true;
    _autoAdvanceDelay = 1000;
    notifyListeners();
  }
}
