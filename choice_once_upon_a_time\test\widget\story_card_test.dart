import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../../lib/src/features/story_library/widgets/story_card.dart';
import '../../lib/src/features/story/models/story.dart';
import '../helpers/test_helpers.dart';
import '../mocks/mock_services.dart';

void main() {
  group('StoryCard Widget Tests', () {
    late MockPurchaseProvider mockPurchaseProvider;
    late Story testStory;

    setUp(() {
      mockPurchaseProvider = MockPurchaseProvider();
      TestHelpers.setupMockProviders(purchaseProvider: mockPurchaseProvider);
      
      testStory = TestHelpers.createMockStory(
        id: 'test-story-1',
        title: 'Test Story',
        description: 'A test story for widget testing',
        coverImagePath: 'assets/images/test_cover.jpg',
        isLocked: false,
      );
    });

    group('Basic Display', () {
      testWidgets('should display story information', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isStoryUnlocked('test-story-1')).thenReturn(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: testStory,
              onTap: () {},
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text('Test Story'), findsOneWidget);
        expect(find.text('A test story for widget testing'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should handle tap events', (tester) async {
        // Arrange
        bool wasTapped = false;
        when(mockPurchaseProvider.isStoryUnlocked('test-story-1')).thenReturn(true);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: testStory,
              onTap: () {
                wasTapped = true;
              },
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Act
        await tester.tap(find.byType(StoryCard));
        await tester.pump();

        // Assert
        expect(wasTapped, true);
      });

      testWidgets('should display cover image when available', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isStoryUnlocked('test-story-1')).thenReturn(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: testStory,
              onTap: () {},
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.byType(Image), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should handle missing cover image gracefully', (tester) async {
        // Arrange
        final storyWithoutImage = TestHelpers.createMockStory(
          id: 'no-image-story',
          title: 'No Image Story',
          coverImagePath: null,
        );
        when(mockPurchaseProvider.isStoryUnlocked('no-image-story')).thenReturn(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: storyWithoutImage,
              onTap: () {},
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text('No Image Story'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });
    });

    group('Lock/Unlock States', () {
      testWidgets('should show unlocked state for free stories', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isStoryUnlocked('test-story-1')).thenReturn(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: testStory,
              onTap: () {},
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.byIcon(Icons.lock), findsNothing);
        expect(find.text('Test Story'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show locked state for premium stories', (tester) async {
        // Arrange
        final lockedStory = TestHelpers.createMockStory(
          id: 'locked-story',
          title: 'Premium Story',
          isLocked: true,
        );
        when(mockPurchaseProvider.isStoryUnlocked('locked-story')).thenReturn(false);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: lockedStory,
              onTap: () {},
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.byIcon(Icons.lock), findsOneWidget);
        expect(find.text('Premium Story'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show premium badge for locked stories', (tester) async {
        // Arrange
        final premiumStory = TestHelpers.createMockStory(
          id: 'premium-story',
          title: 'Premium Story',
          isLocked: true,
        );
        when(mockPurchaseProvider.isStoryUnlocked('premium-story')).thenReturn(false);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: premiumStory,
              onTap: () {},
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text('PREMIUM'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should handle premium unlock correctly', (tester) async {
        // Arrange
        final premiumStory = TestHelpers.createMockStory(
          id: 'premium-story',
          title: 'Premium Story',
          isLocked: true,
        );
        
        // Initially locked
        when(mockPurchaseProvider.isStoryUnlocked('premium-story')).thenReturn(false);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: premiumStory,
              onTap: () {},
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Should show locked state
        expect(find.byIcon(Icons.lock), findsOneWidget);

        // Act - simulate premium unlock
        when(mockPurchaseProvider.isStoryUnlocked('premium-story')).thenReturn(true);
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: premiumStory,
              onTap: () {},
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert - should show unlocked state
        expect(find.byIcon(Icons.lock), findsNothing);
        TestHelpers.verifyNoOverflow(tester);
      });
    });

    group('Progress Indicators', () {
      testWidgets('should show progress indicator when story is in progress', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isStoryUnlocked('test-story-1')).thenReturn(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: testStory,
              onTap: () {},
              progress: 0.5, // 50% complete
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.byType(LinearProgressIndicator), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show completion badge when story is completed', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isStoryUnlocked('test-story-1')).thenReturn(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: testStory,
              onTap: () {},
              progress: 1.0, // 100% complete
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.byIcon(Icons.check_circle), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should not show progress for unstarted stories', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isStoryUnlocked('test-story-1')).thenReturn(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: testStory,
              onTap: () {},
              // No progress specified
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.byType(LinearProgressIndicator), findsNothing);
        expect(find.byIcon(Icons.check_circle), findsNothing);
        TestHelpers.verifyNoOverflow(tester);
      });
    });

    group('Responsive Design', () {
      testWidgets('should adapt to different screen sizes', (tester) async {
        await TestHelpers.testOnAllScreenSizes(
          tester,
          (screenSize) {
            when(mockPurchaseProvider.isStoryUnlocked('test-story-1')).thenReturn(true);
            return TestHelpers.createResponsiveTestWidget(
              child: StoryCard(
                story: testStory,
                onTap: () {},
              ),
              screenSize: screenSize,
              purchaseProvider: mockPurchaseProvider,
            );
          },
          (tester, screenSize) async {
            await tester.pump();
            
            // Should display without overflow
            expect(find.text('Test Story'), findsOneWidget);
          },
        );
      });

      testWidgets('should handle long titles gracefully', (tester) async {
        // Arrange
        final longTitleStory = TestHelpers.createMockStory(
          id: 'long-title-story',
          title: 'This is a very long story title that might cause text overflow issues',
          description: 'Short description',
        );
        when(mockPurchaseProvider.isStoryUnlocked('long-title-story')).thenReturn(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createResponsiveTestWidget(
            child: StoryCard(
              story: longTitleStory,
              onTap: () {},
            ),
            screenSize: TestHelpers.phonePortrait,
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.textContaining('This is a very long story title'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should handle long descriptions gracefully', (tester) async {
        // Arrange
        final longDescStory = TestHelpers.createMockStory(
          id: 'long-desc-story',
          title: 'Short Title',
          description: 'This is a very long story description that might cause text overflow issues in the card layout when displayed on smaller screens',
        );
        when(mockPurchaseProvider.isStoryUnlocked('long-desc-story')).thenReturn(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createResponsiveTestWidget(
            child: StoryCard(
              story: longDescStory,
              onTap: () {},
            ),
            screenSize: TestHelpers.phonePortrait,
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text('Short Title'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });
    });

    group('Visual States', () {
      testWidgets('should show visual feedback on press', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isStoryUnlocked('test-story-1')).thenReturn(true);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: testStory,
              onTap: () {},
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Act - start press gesture
        final gesture = await tester.startGesture(
          tester.getCenter(find.byType(StoryCard)),
        );
        await tester.pump();

        // Assert - card should show pressed state
        expect(find.byType(StoryCard), findsOneWidget);

        // Complete the gesture
        await gesture.up();
        await tester.pump();
      });

      testWidgets('should handle disabled state for locked stories', (tester) async {
        // Arrange
        final lockedStory = TestHelpers.createMockStory(
          id: 'locked-story',
          title: 'Locked Story',
          isLocked: true,
        );
        when(mockPurchaseProvider.isStoryUnlocked('locked-story')).thenReturn(false);

        bool wasTapped = false;

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: lockedStory,
              onTap: () {
                wasTapped = true;
              },
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Act
        await tester.tap(find.byType(StoryCard));
        await tester.pump();

        // Assert - should still be tappable (to show premium screen)
        expect(wasTapped, true);
        expect(find.byIcon(Icons.lock), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('should meet accessibility guidelines', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isStoryUnlocked('test-story-1')).thenReturn(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: testStory,
              onTap: () {},
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        await TestHelpers.verifyAccessibility(tester);
      });

      testWidgets('should have proper semantic labels', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isStoryUnlocked('test-story-1')).thenReturn(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: testStory,
              onTap: () {},
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.text('Test Story'), findsOneWidget);
        expect(find.text('A test story for widget testing'), findsOneWidget);
      });

      testWidgets('should indicate locked state to screen readers', (tester) async {
        // Arrange
        final lockedStory = TestHelpers.createMockStory(
          id: 'locked-story',
          title: 'Locked Story',
          isLocked: true,
        );
        when(mockPurchaseProvider.isStoryUnlocked('locked-story')).thenReturn(false);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: lockedStory,
              onTap: () {},
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Assert
        expect(find.byIcon(Icons.lock), findsOneWidget);
        expect(find.text('PREMIUM'), findsOneWidget);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle null story gracefully', (tester) async {
        // This test would be for error handling if the widget supports null stories
        // For now, we assume the widget requires a valid story
        expect(true, true); // Placeholder
      });

      testWidgets('should handle missing image assets', (tester) async {
        // Arrange
        final storyWithBadImage = TestHelpers.createMockStory(
          id: 'bad-image-story',
          title: 'Bad Image Story',
          coverImagePath: 'assets/images/non_existent.jpg',
        );
        when(mockPurchaseProvider.isStoryUnlocked('bad-image-story')).thenReturn(true);

        // Act & Assert - should not throw
        expect(() async {
          await tester.pumpWidget(
            TestHelpers.createTestWidget(
              child: StoryCard(
                story: storyWithBadImage,
                onTap: () {},
              ),
              purchaseProvider: mockPurchaseProvider,
            ),
          );
        }, returnsNormally);
      });

      testWidgets('should handle rapid taps', (tester) async {
        // Arrange
        when(mockPurchaseProvider.isStoryUnlocked('test-story-1')).thenReturn(true);
        int tapCount = 0;

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: StoryCard(
              story: testStory,
              onTap: () {
                tapCount++;
              },
            ),
            purchaseProvider: mockPurchaseProvider,
          ),
        );

        // Act - rapid taps
        for (int i = 0; i < 5; i++) {
          await tester.tap(find.byType(StoryCard));
          await tester.pump(const Duration(milliseconds: 10));
        }

        // Assert
        expect(tapCount, 5);
      });
    });
  });
}
