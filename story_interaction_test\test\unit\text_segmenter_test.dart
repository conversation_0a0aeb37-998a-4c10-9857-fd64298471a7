import 'package:flutter_test/flutter_test.dart';
import 'package:story_interaction_test/src/services/text_segmenter.dart';

void main() {
  group('TextSegmenter', () {
    group('splitIntoSentences', () {
      test('should split text by sentence terminators', () {
        const text = 'This is the first sentence. This is the second! And this is the third?';

        final sentences = TextSegmenter.splitIntoSentences(text);

        expect(sentences.length, equals(3));
        expect(sentences[0], equals('This is the first sentence.'));
        expect(sentences[1], equals('This is the second!'));
        expect(sentences[2], equals('And this is the third?'));
      });

      test('should handle empty text', () {
        final sentences = TextSegmenter.splitIntoSentences('');
        expect(sentences, isEmpty);
      });

      test('should handle text without punctuation', () {
        const text = 'This is text without punctuation';

        final sentences = TextSegmenter.splitIntoSentences(text);

        expect(sentences.length, equals(1));
        expect(sentences[0], equals('This is text without punctuation.'));
      });

      test('should handle multiple punctuation marks', () {
        const text = 'What?! Really... Yes!!! Amazing.';

        final sentences = TextSegmenter.splitIntoSentences(text);

        expect(sentences.length, equals(4));
        expect(sentences[0], equals('What?!'));
        expect(sentences[1], equals('Really...'));
        expect(sentences[2], equals('Yes!!!'));
        expect(sentences[3], equals('Amazing.'));
      });
    });

    group('splitIntoSegments', () {
      test('should return sentences as-is when they are short enough', () {
        const text = 'Short sentence. Another short one. Third short sentence.';

        final segments = TextSegmenter.splitIntoSegments(text, targetLength: 100);

        expect(segments.length, equals(3));
        expect(segments[0], equals('Short sentence.'));
        expect(segments[1], equals('Another short one.'));
        expect(segments[2], equals('Third short sentence.'));
      });

      test('should combine short sentences into segments', () {
        const text = 'One. Two. Three. Four. Five.';

        final segments = TextSegmenter.splitIntoSegments(text, targetLength: 20);

        expect(segments.length, greaterThan(1));
        expect(segments.length, lessThanOrEqualTo(5));

        // Each segment should be reasonably sized
        for (final segment in segments) {
          expect(segment.length, lessThanOrEqualTo(30)); // Allow some flexibility
        }
      });

      test('should handle empty text', () {
        final segments = TextSegmenter.splitIntoSegments('');
        expect(segments, isEmpty);
      });

      test('should handle single long sentence', () {
        const text = 'This is a very long sentence that should be split into smaller parts because it exceeds the target length significantly and would be difficult to process as a single unit.';

        final segments = TextSegmenter.splitIntoSegments(text, targetLength: 50);

        expect(segments.length, greaterThanOrEqualTo(1));

        // Verify all segments are present when joined
        final rejoined = segments.join(' ');
        expect(rejoined.replaceAll(RegExp(r'\s+'), ' '),
               equals(text.replaceAll(RegExp(r'\s+'), ' ')));
      });
    });

    group('splitIntoWords', () {
      test('should split text into individual words', () {
        const text = 'Hello world this is a test';

        final words = TextSegmenter.splitIntoWords(text);

        expect(words.length, equals(6));
        expect(words[0], equals('Hello'));
        expect(words[1], equals('world'));
        expect(words[5], equals('test'));
      });

      test('should handle multiple spaces', () {
        const text = 'Hello    world   with   spaces';

        final words = TextSegmenter.splitIntoWords(text);

        expect(words.length, equals(4));
        expect(words[0], equals('Hello'));
        expect(words[1], equals('world'));
        expect(words[2], equals('with'));
        expect(words[3], equals('spaces'));
      });

      test('should handle empty text', () {
        final words = TextSegmenter.splitIntoWords('');
        expect(words, isEmpty);
      });

      test('should handle text with punctuation', () {
        const text = 'Hello, world! How are you?';

        final words = TextSegmenter.splitIntoWords(text);

        expect(words.length, equals(5));
        expect(words[0], equals('Hello,'));
        expect(words[1], equals('world!'));
        expect(words[2], equals('How'));
        expect(words[3], equals('are'));
        expect(words[4], equals('you?'));
      });
    });
  });
}
