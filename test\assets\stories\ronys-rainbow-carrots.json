{"storyId": "ronys-rainbow-carrots", "storyTitle": "<PERSON><PERSON>'s Rainbow Carrots", "targetAge": "4-6 years old", "moralTheme": "Sharing", "characterList": [{"characterName": "<PERSON><PERSON>", "characterDescriptionForVisual": "<PERSON><PERSON> is a young, energetic rabbit with fluffy grey fur, long ears that stand up straight (one with a small floppy tip), bright blue eyes, and a twitchy pink nose. He consistently wears a pair of little blue overalls. His expressions should be very clear (e.g., excited, selfish, kind, happy, regretful)."}, {"characterName": "Squeaky", "characterDescriptionForVisual": "<PERSON>queaky is a small, nimble squirrel with bushy reddish-brown fur, a long, fluffy tail, and curious black eyes. He often looks hungry or hopeful initially, and later happy and energetic. He might be seen holding an acorn in some scenes."}, {"characterName": "<PERSON>", "characterDescriptionForVisual": "<PERSON> is a gentle and slightly shy bear cub with soft, honey-colored fur and big, kind brown eyes. She might look sad or wistful initially, and later joyful. She has a love for colorful flowers."}], "storyNodes": [{"sceneId": "rony_intro_carrots", "narrationText": "<PERSON><PERSON> the <PERSON> hopped excitedly in his garden. His special rainbow carrots – red, yellow, and even purple – were finally ready! 'Wow!' he whispered, 'All for me!'", "imageAssetPath": "assets/images/ronys-rainbow-carrots/rony_intro_carrots", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "meet_squeaky_hungry_choice1", "narrationText": "Just as <PERSON><PERSON> was about to pull out the biggest carrot, <PERSON>que<PERSON><PERSON> the Squirrel scampered by. 'Hi <PERSON><PERSON>! Your carrots look amazing!' <PERSON><PERSON><PERSON><PERSON> sighed, 'I haven't found many nuts today.'", "imageAssetPath": "assets/images/ronys-rainbow-carrots/meet_squeaky_hungry_choice1", "isChoicePoint": true, "choicePrompt": "What should <PERSON><PERSON> do?", "choices": [{"choiceText": "Share a carrot with Squeaky", "nextSceneId": "rony_shares_with_squeaky_A"}, {"choiceText": "Keep all carrots for himself", "nextSceneId": "rony_no_share_squeaky_sad_B"}], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "rony_shares_with_squeaky_A", "narrationText": "<PERSON><PERSON> looked at his colorful carrots, then at <PERSON><PERSON><PERSON><PERSON>. 'Here you go, <PERSON><PERSON><PERSON><PERSON>!' he said, pulling out a crunchy yellow carrot. 'You can have this one!'", "imageAssetPath": "assets/images/ronys-rainbow-carrots/rony_shares_with_squeaky_a", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "meet_bella_flowers_choice_A2", "narrationText": "Squeaky crunched happily on his carrot. Soon, they saw <PERSON> the Bear cub sitting by a stream. She looked sadly at some beautiful flowers on the other side. 'I can't reach them for Mama,' she sniffed.", "imageAssetPath": "assets/images/ronys-rainbow-carrots/meet_bella_flowers_choice_a2", "isChoicePoint": true, "choicePrompt": "What should <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> do?", "choices": [{"choiceText": "Help <PERSON> get the flowers", "nextSceneId": "rony_squeaky_team_up_bella_<PERSON>a"}, {"choiceText": "Offer Bella a carrot instead", "nextSceneId": "rony_<PERSON>_bella_carrot_A2b"}], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "rony_squeaky_team_up_bella_<PERSON>a", "narrationText": "'We can help!' chirped <PERSON><PERSON><PERSON><PERSON>, feeling brave after <PERSON><PERSON> shared. <PERSON><PERSON> nodded. Together, <PERSON><PERSON> helped steady a log while <PERSON><PERSON><PERSON><PERSON> carefully scampered across to pick the flowers.", "imageAssetPath": "assets/images/ronys-rainbow-carrots/rony_squeaky_team_up_bella_a2a", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "flowers_success_joy_ending_A2a", "narrationText": "Success! <PERSON><PERSON><PERSON><PERSON> returned with a pawful of beautiful flowers. <PERSON> was so happy, she gave them both a big hug! Sharing and helping felt wonderful, and <PERSON><PERSON> even got a shiny pebble from <PERSON><PERSON><PERSON><PERSON> as a thank you.", "imageAssetPath": "assets/images/ronys-rainbow-carrots/flowers_success_joy_ending_a2a", "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": true, "moralLessonReinforced": "Sharing your things and helping others makes everyone happy and can bring unexpected rewards and stronger friendships."}, {"sceneId": "rony_<PERSON>_bella_carrot_A2b", "narrationText": "<PERSON><PERSON> remembered how good his carrots were. 'Maybe a carrot will cheer you up, <PERSON>?' he offered, holding out a bright purple one. 'It’s a rainbow carrot!'", "imageAssetPath": null, "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "bella_cheered_rony_feels_good_ending_A2b", "narrationText": "<PERSON>’s eyes widened. 'A purple carrot? Wow!' She took a bite and smiled. 'Thank you, <PERSON><PERSON>! This is so special!' <PERSON><PERSON> felt a warm, happy feeling inside for sharing again.", "imageAssetPath": null, "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": true, "moralLessonReinforced": "Sharing special things with others can make them very happy and makes you feel good too."}, {"sceneId": "rony_no_share_squeaky_sad_B", "narrationText": "'Sorry, <PERSON><PERSON><PERSON><PERSON>,' <PERSON><PERSON> said, clutching a big red carrot. 'These are my special carrots, and I want to eat them all myself.' <PERSON><PERSON><PERSON><PERSON>’s whiskers drooped, and he sadly scampered away.", "imageAssetPath": null, "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "rony_alone_with_carrots_sees_others_choice_B2", "narrationText": "<PERSON><PERSON> munched on his carrots alone. They were tasty, but it wasn't as much fun as he thought. He still had a big pile left when he saw <PERSON><PERSON><PERSON><PERSON> sitting sadly under a tree and <PERSON> trying to reach those flowers.", "imageAssetPath": null, "isChoicePoint": true, "choicePrompt": "<PERSON><PERSON> has lots of carrots left. What should he do?", "choices": [{"choiceText": "Share carrots with <PERSON><PERSON><PERSON><PERSON> and <PERSON>", "nextSceneId": "rony_change_of_heart_shares_all_B2a"}, {"choiceText": "Hide the extra carrots for later", "nextSceneId": "rony_hides_carrots_secretly_B2b"}], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "rony_change_of_heart_shares_all_B2a", "narrationText": "<PERSON><PERSON> suddenly felt a bit silly having so many carrots to himself. 'Hey <PERSON><PERSON><PERSON><PERSON>! <PERSON>!' he called out. 'Come over! I have plenty of rainbow carrots to share!'", "imageAssetPath": null, "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "friends_share_joyfully_ending_B2a", "narrationText": "<PERSON><PERSON><PERSON><PERSON> and <PERSON> hurried over. Soon, all three friends were munching on colorful carrots and laughing. <PERSON><PERSON> discovered that sharing his treats made them taste even better!", "imageAssetPath": null, "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": true, "moralLessonReinforced": "Sharing with friends is much more fun than keeping things to yourself, and it's never too late to make that choice."}, {"sceneId": "rony_hides_carrots_secretly_B2b", "narrationText": "<PERSON><PERSON> decided he still wanted all the carrots. He quickly dug a hole and started hiding his extra rainbow carrots, hoping no one would see his secret stash.", "imageAssetPath": null, "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": false, "moralLessonReinforced": null}, {"sceneId": "carrots_spoiled_rony_regrets_ending_B2b", "narrationText": "Later, <PERSON><PERSON> went to get a hidden carrot, but some were soggy from the damp earth, and a cheeky bird had flown off with another! He saw <PERSON><PERSON><PERSON><PERSON> and <PERSON> playing happily in the distance and felt very lonely with his spoiled carrots.", "imageAssetPath": null, "isChoicePoint": false, "choicePrompt": null, "choices": [], "isEndingScene": true, "moralLessonReinforced": "If you don't share, you might lose what you have anyway, and you miss out on the joy of friendship and playing together."}]}