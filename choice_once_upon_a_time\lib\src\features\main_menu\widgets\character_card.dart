import 'package:flutter/material.dart';
import '../../../shared_kernel/theme/app_theme.dart';
import 'dart:math' as math;

/// Widget that displays the character card on the main menu
class CharacterCard extends StatelessWidget {
  /// The name of the active child profile
  final String? childName;

  /// Constructor
  const CharacterCard({
    super.key,
    this.childName,
  });

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions for responsive sizing
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    // Calculate responsive sizes
    final double minDimension = math.min(screenWidth, screenHeight);
    final double cardMargin = minDimension * 0.02; // 2% of min dimension
    final double cardPadding = minDimension * 0.02; // 2% of min dimension
    final double borderRadius = minDimension * 0.03; // 3% of min dimension
    final double imageSize = minDimension * 0.25; // 25% of min dimension
    final double iconSize = imageSize * 0.5; // 50% of image size
    final double fontSize = minDimension * 0.025; // 2.5% of min dimension

    return Card(
      margin: EdgeInsets.all(cardMargin),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      elevation: 8,
      child: Padding(
        padding: EdgeInsets.all(cardPadding),
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Character image placeholder
              Container(
                height: imageSize,
                width: imageSize,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(borderRadius * 0.7),
                ),
                child: Icon(
                  Icons.person,
                  size: iconSize,
                  color: Colors.grey,
                ),
              ),
              SizedBox(height: cardPadding),

              // Child name text
              Text(
                childName != null
                  ? '$childName\'s Adventures!'
                  : 'Your Adventures!',
                style: AppTheme.subheadingStyle.copyWith(
                  fontSize: fontSize,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
