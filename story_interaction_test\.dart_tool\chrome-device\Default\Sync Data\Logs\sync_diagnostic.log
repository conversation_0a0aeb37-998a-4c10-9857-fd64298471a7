2025-05-25 06:36:35.010: [INFO][Sync] Reset engine, reason: 8
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Bookmarks
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Preferences
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Passwords
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Autofill Profiles
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Autofill
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Extensions
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Sessions
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Extension settings
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: History Delete Directives
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Device Info
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: User Consents
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Send Tab To Self
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Web Apps
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: History
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Saved Tab Group
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Collection
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Edge E Drop
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Edge Hub App Usage
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Edge Tab Group
2025-05-25 06:36:35.010: [INFO][Sync] Stopped: Edge Wallet
2025-05-25 06:36:35.010: [INFO][Sync] SyncState after authenticated was: NotSignedIn
2025-05-25 06:36:35.147: [INFO][Sync] Reset engine, reason: 8
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Bookmarks
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Preferences
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Passwords
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Autofill Profiles
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Autofill
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Extensions
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Sessions
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Extension settings
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: History Delete Directives
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Device Info
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: User Consents
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Send Tab To Self
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Web Apps
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: History
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Saved Tab Group
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Collection
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Edge E Drop
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Edge Hub App Usage
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Edge Tab Group
2025-05-25 06:36:35.147: [INFO][Sync] Stopped: Edge Wallet
2025-05-25 06:36:35.265: [INFO][Sync] Try to start sync engine
2025-05-25 06:36:35.266: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Encryption Keys
2025-05-25 06:36:35.266: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-05-25 06:36:35.266: [INFO][SyncEngineBackend::DoInitialize] Control Types added: Encryption Keys
2025-05-25 06:36:35.266: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Encryption Keys with reason: 3
2025-05-25 06:36:35.266: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Configure job was blocked
2025-05-25 06:36:35.382: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-25 06:36:35.382: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-25 06:36:35.382: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-05-25 06:36:36.049: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-25 06:36:36.049: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-25 06:36:36.049: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-05-25 06:36:36.049: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-05-25 06:36:36.049: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-05-25 06:36:36.049: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Encryption Keys
2025-05-25 06:36:36.423: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-05-25 06:36:36.423: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 3, current state: 0
2025-05-25 06:36:36.423: [WARN][Sync] Crypto error data types: Passwords, Autofill Profiles, Autofill
2025-05-25 06:36:36.424: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys; Allow fail: Passwords, Autofill Profiles, Autofill
2025-05-25 06:36:36.424: [INFO][Sync] Loading: Bookmarks
2025-05-25 06:36:36.424: [INFO][Sync] Loading: Preferences
2025-05-25 06:36:36.424: [INFO][Sync] Loading: Extensions
2025-05-25 06:36:36.424: [INFO][Sync] Loading: Sessions
2025-05-25 06:36:36.424: [INFO][Sync] Loading: Extension settings
2025-05-25 06:36:36.424: [INFO][Sync] Loading: History Delete Directives
2025-05-25 06:36:36.424: [INFO][Sync] Loading: Device Info
2025-05-25 06:36:36.424: [INFO][Sync] Loading: User Consents
2025-05-25 06:36:36.424: [INFO][Sync] Loading: Send Tab To Self
2025-05-25 06:36:36.424: [INFO][Sync] Loading: Web Apps
2025-05-25 06:36:36.424: [INFO][Sync] Loading: History
2025-05-25 06:36:36.424: [INFO][Sync] Loading: Saved Tab Group
2025-05-25 06:36:36.424: [INFO][Sync] Loading: Collection
2025-05-25 06:36:36.424: [INFO][Sync] Loading: Edge E Drop
2025-05-25 06:36:36.424: [INFO][Sync] Loading: Edge Hub App Usage
2025-05-25 06:36:36.424: [INFO][Sync] Loading: Edge Tab Group
2025-05-25 06:36:36.424: [INFO][Sync] Loading: Edge Wallet
2025-05-25 06:36:36.425: [INFO][Sync] All data types are ready for configure.
2025-05-25 06:36:37.268: [INFO][Sync] Started DataTypeManager configuration, reason: 5
2025-05-25 06:36:37.268: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 5, current state: 1
2025-05-25 06:36:37.276: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Bookmarks
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Preferences
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Extensions
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Sessions
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Extension settings
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for History Delete Directives
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Device Info
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Send Tab To Self
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Web Apps
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for History
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Saved Tab Group
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Collection
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge E Drop
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge Hub App Usage
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge Tab Group
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge Wallet
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 3
2025-05-25 06:36:37.276: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 06:36:37.276: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-05-25 06:36:37.276: [WARN][Sync]     Reconfigure requested while configuration ongoing.
2025-05-25 06:36:37.276: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 5, current state: 2
2025-05-25 06:36:37.276: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys; Allow fail: 
2025-05-25 06:36:37.276: [INFO][Sync] Loading: Passwords
2025-05-25 06:36:37.276: [INFO][Sync] Loading: Autofill Profiles
2025-05-25 06:36:37.276: [INFO][Sync] Loading: Autofill
2025-05-25 06:36:37.276: [INFO][Sync] All data types are ready for configure.
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Passwords
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Autofill Profiles
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Autofill
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-25 06:36:37.276: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 06:36:37.276: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-05-25 06:36:37.276: [INFO][Sync] Prepare to configure types: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-05-25 06:36:37.276: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys with reason: 5
2025-05-25 06:36:37.276: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-05-25 06:36:37.721: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-05-25 06:36:37.799: [INFO][Sync] ConfigurationDone, failed: , succeeded: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys, remaining count: 3
2025-05-25 06:36:37.799: [INFO][Sync] Prepare to configure types: Bookmarks, Encryption Keys
2025-05-25 06:36:37.799: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Bookmarks, Encryption Keys with reason: 5
2025-05-25 06:36:37.799: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Bookmarks, Encryption Keys
2025-05-25 06:36:37.931: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Bookmarks, Encryption Keys
2025-05-25 06:36:37.937: [INFO][Sync] ConfigurationDone, failed: , succeeded: Bookmarks, Encryption Keys, remaining count: 2
2025-05-25 06:36:37.937: [INFO][Sync] Prepare to configure types: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys
2025-05-25 06:36:37.937: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys with reason: 5
2025-05-25 06:36:37.937: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys
2025-05-25 06:36:38.196: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys
2025-05-25 06:36:38.197: [INFO][Sync] ConfigurationDone, failed: , succeeded: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, remaining count: 1
2025-05-25 06:36:38.198: [INFO][Sync] Prepare to configure types: History, Encryption Keys
2025-05-25 06:36:38.198: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: History, Encryption Keys with reason: 5
2025-05-25 06:36:38.198: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: History, Encryption Keys
2025-05-25 06:36:38.448: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_CONNECTION_RESET)
2025-05-25 06:36:38.448: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Syncer error: Network error (ERR_CONNECTION_RESET)
2025-05-25 06:36:38.448: [WARN][Sync] NetworkError: Network error (ERR_CONNECTION_RESET)
2025-05-25 06:37:23.463: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: History, Encryption Keys
2025-05-25 06:37:23.798: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: History, Encryption Keys
2025-05-25 06:37:23.802: [INFO][Sync] ConfigurationDone, failed: , succeeded: History, Encryption Keys, remaining count: 0
2025-05-25 06:37:23.802: [INFO][Sync]     Configuration completed, state: 7
2025-05-25 06:37:23.803: [INFO][Sync] Configured DataTypeManager: Ok
2025-05-25 06:37:23.804: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-05-25 06:55:21.079: [INFO][Sync] Reset engine, reason: 0
2025-05-25 06:55:21.079: [INFO][Sync] Reset engine with reason: 0
2025-05-25 07:22:29.249: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-05-25 07:22:29.612: [INFO][Sync] Try to start sync engine
2025-05-25 07:22:29.613: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Encryption Keys
2025-05-25 07:22:29.613: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-05-25 07:22:29.613: [INFO][SyncEngineBackend::DoInitialize] Control Types added: Encryption Keys
2025-05-25 07:22:29.613: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Encryption Keys with reason: 3
2025-05-25 07:22:29.613: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Configure job was blocked
2025-05-25 07:22:29.643: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-25 07:22:29.643: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-25 07:22:29.643: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-05-25 07:22:30.992: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-25 07:22:30.992: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-25 07:22:30.992: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-05-25 07:22:30.992: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-05-25 07:22:30.992: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-05-25 07:22:30.992: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Encryption Keys
2025-05-25 07:22:31.436: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-05-25 07:22:31.436: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-05-25 07:22:31.436: [WARN][Sync] Crypto error data types: Passwords, Autofill Profiles, Autofill
2025-05-25 07:22:31.437: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys; Allow fail: Passwords, Autofill Profiles, Autofill
2025-05-25 07:22:31.437: [INFO][Sync] Loading: Bookmarks
2025-05-25 07:22:31.437: [INFO][Sync] Loading: Preferences
2025-05-25 07:22:31.437: [INFO][Sync] Loading: Extensions
2025-05-25 07:22:31.437: [INFO][Sync] Loading: Sessions
2025-05-25 07:22:31.437: [INFO][Sync] Loading: Extension settings
2025-05-25 07:22:31.437: [INFO][Sync] Loading: History Delete Directives
2025-05-25 07:22:31.437: [INFO][Sync] Loading: Device Info
2025-05-25 07:22:31.437: [INFO][Sync] Loading: User Consents
2025-05-25 07:22:31.437: [INFO][Sync] Loading: Send Tab To Self
2025-05-25 07:22:31.437: [INFO][Sync] Loading: Web Apps
2025-05-25 07:22:31.437: [INFO][Sync] Loading: History
2025-05-25 07:22:31.437: [INFO][Sync] Loading: Saved Tab Group
2025-05-25 07:22:31.437: [INFO][Sync] Loading: Collection
2025-05-25 07:22:31.437: [INFO][Sync] Loading: Edge E Drop
2025-05-25 07:22:31.437: [INFO][Sync] Loading: Edge Hub App Usage
2025-05-25 07:22:31.437: [INFO][Sync] Loading: Edge Tab Group
2025-05-25 07:22:31.437: [INFO][Sync] Loading: Edge Wallet
2025-05-25 07:22:31.441: [INFO][Sync] All data types are ready for configure.
2025-05-25 07:22:32.012: [INFO][Sync] Started DataTypeManager configuration, reason: 5
2025-05-25 07:22:32.012: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 5, current state: 1
2025-05-25 07:22:32.019: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-05-25 07:22:32.019: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-05-25 07:22:32.019: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 07:22:32.020: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-05-25 07:22:32.020: [WARN][Sync]     Reconfigure requested while configuration ongoing.
2025-05-25 07:22:32.020: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 5, current state: 2
2025-05-25 07:22:32.020: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys; Allow fail: 
2025-05-25 07:22:32.020: [INFO][Sync] Loading: Passwords
2025-05-25 07:22:32.020: [INFO][Sync] Loading: Autofill Profiles
2025-05-25 07:22:32.020: [INFO][Sync] Loading: Autofill
2025-05-25 07:22:32.021: [INFO][Sync] All data types are ready for configure.
2025-05-25 07:22:32.021: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Passwords
2025-05-25 07:22:32.021: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-25 07:22:32.021: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 07:22:32.021: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-05-25 07:22:32.021: [INFO][Sync] Prepare to configure types: Passwords, Encryption Keys
2025-05-25 07:22:32.021: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Passwords, Encryption Keys with reason: 5
2025-05-25 07:22:32.021: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Passwords, Encryption Keys
2025-05-25 07:22:32.174: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Passwords, Encryption Keys
2025-05-25 07:22:32.175: [INFO][Sync] ConfigurationDone, failed: , succeeded: Passwords, Encryption Keys, remaining count: 3
2025-05-25 07:22:32.175: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-25 07:22:32.175: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 07:22:32.175: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-05-25 07:22:32.175: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-25 07:22:32.175: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 07:22:32.177: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-05-25 07:22:32.178: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-25 07:22:32.178: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 07:22:32.178: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-05-25 07:22:32.179: [INFO][Sync]     Configuration completed, state: 7
2025-05-25 07:22:32.179: [INFO][Sync] Configured DataTypeManager: Ok
2025-05-25 07:22:32.180: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-05-25 07:28:17.005: [INFO][Sync] Reset engine, reason: 0
2025-05-25 07:28:17.005: [INFO][Sync] Reset engine with reason: 0
2025-05-25 07:28:38.086: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-05-25 07:28:38.380: [INFO][Sync] Try to start sync engine
2025-05-25 07:28:38.384: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Encryption Keys
2025-05-25 07:28:38.384: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-05-25 07:28:38.384: [INFO][SyncEngineBackend::DoInitialize] Control Types added: Encryption Keys
2025-05-25 07:28:38.384: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Encryption Keys with reason: 3
2025-05-25 07:28:38.384: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Configure job was blocked
2025-05-25 07:28:38.410: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-25 07:28:38.410: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-25 07:28:38.411: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-05-25 07:28:39.689: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-25 07:28:39.689: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-25 07:28:39.689: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-05-25 07:28:39.689: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-05-25 07:28:39.689: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-05-25 07:28:39.689: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Encryption Keys
2025-05-25 07:28:39.955: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-05-25 07:28:39.956: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-05-25 07:28:39.956: [WARN][Sync] Crypto error data types: Passwords, Autofill Profiles, Autofill
2025-05-25 07:28:39.956: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys; Allow fail: Passwords, Autofill Profiles, Autofill
2025-05-25 07:28:39.956: [INFO][Sync] Loading: Bookmarks
2025-05-25 07:28:39.956: [INFO][Sync] Loading: Preferences
2025-05-25 07:28:39.956: [INFO][Sync] Loading: Extensions
2025-05-25 07:28:39.956: [INFO][Sync] Loading: Sessions
2025-05-25 07:28:39.956: [INFO][Sync] Loading: Extension settings
2025-05-25 07:28:39.956: [INFO][Sync] Loading: History Delete Directives
2025-05-25 07:28:39.956: [INFO][Sync] Loading: Device Info
2025-05-25 07:28:39.956: [INFO][Sync] Loading: User Consents
2025-05-25 07:28:39.956: [INFO][Sync] Loading: Send Tab To Self
2025-05-25 07:28:39.956: [INFO][Sync] Loading: Web Apps
2025-05-25 07:28:39.956: [INFO][Sync] Loading: History
2025-05-25 07:28:39.956: [INFO][Sync] Loading: Saved Tab Group
2025-05-25 07:28:39.956: [INFO][Sync] Loading: Collection
2025-05-25 07:28:39.956: [INFO][Sync] Loading: Edge E Drop
2025-05-25 07:28:39.956: [INFO][Sync] Loading: Edge Hub App Usage
2025-05-25 07:28:39.956: [INFO][Sync] Loading: Edge Tab Group
2025-05-25 07:28:39.956: [INFO][Sync] Loading: Edge Wallet
2025-05-25 07:28:39.958: [INFO][Sync] All data types are ready for configure.
2025-05-25 07:28:40.606: [INFO][Sync] Started DataTypeManager configuration, reason: 5
2025-05-25 07:28:40.606: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 5, current state: 1
2025-05-25 07:28:40.613: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-05-25 07:28:40.613: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-05-25 07:28:40.613: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 07:28:40.613: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-05-25 07:28:40.613: [WARN][Sync]     Reconfigure requested while configuration ongoing.
2025-05-25 07:28:40.613: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 5, current state: 2
2025-05-25 07:28:40.613: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys; Allow fail: 
2025-05-25 07:28:40.613: [INFO][Sync] Loading: Passwords
2025-05-25 07:28:40.613: [INFO][Sync] Loading: Autofill Profiles
2025-05-25 07:28:40.613: [INFO][Sync] Loading: Autofill
2025-05-25 07:28:40.613: [INFO][Sync] All data types are ready for configure.
2025-05-25 07:28:40.613: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Passwords
2025-05-25 07:28:40.613: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-25 07:28:40.613: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 07:28:40.613: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-05-25 07:28:40.613: [INFO][Sync] Prepare to configure types: Passwords, Encryption Keys
2025-05-25 07:28:40.613: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Passwords, Encryption Keys with reason: 5
2025-05-25 07:28:40.613: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Passwords, Encryption Keys
2025-05-25 07:28:40.791: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Passwords, Encryption Keys
2025-05-25 07:28:40.792: [INFO][Sync] ConfigurationDone, failed: , succeeded: Passwords, Encryption Keys, remaining count: 3
2025-05-25 07:28:40.792: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-25 07:28:40.792: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 07:28:40.792: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-05-25 07:28:40.792: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-25 07:28:40.792: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 07:28:40.796: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-05-25 07:28:40.797: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-25 07:28:40.797: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 07:28:40.797: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-05-25 07:28:40.799: [INFO][Sync]     Configuration completed, state: 7
2025-05-25 07:28:40.799: [INFO][Sync] Configured DataTypeManager: Ok
2025-05-25 07:28:40.800: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-05-25 07:29:23.435: [INFO][Sync] Reset engine, reason: 0
2025-05-25 07:29:23.435: [INFO][Sync] Reset engine with reason: 0
2025-05-25 07:32:41.943: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-05-25 07:32:42.223: [INFO][Sync] Try to start sync engine
2025-05-25 07:32:42.225: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Encryption Keys
2025-05-25 07:32:42.225: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-05-25 07:32:42.225: [INFO][SyncEngineBackend::DoInitialize] Control Types added: Encryption Keys
2025-05-25 07:32:42.225: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Encryption Keys with reason: 3
2025-05-25 07:32:42.225: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Configure job was blocked
2025-05-25 07:32:42.256: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-25 07:32:42.256: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-25 07:32:42.256: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-05-25 07:32:43.421: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-25 07:32:43.421: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-25 07:32:43.421: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-05-25 07:32:43.421: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-05-25 07:32:43.421: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-05-25 07:32:43.421: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Encryption Keys
2025-05-25 07:32:43.646: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_CONNECTION_RESET)
2025-05-25 07:32:43.646: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Syncer error: Network error (ERR_CONNECTION_RESET)
2025-05-25 07:32:43.646: [WARN][Sync] NetworkError: Network error (ERR_CONNECTION_RESET)
2025-05-25 07:33:28.649: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Encryption Keys
2025-05-25 07:33:28.972: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-05-25 07:33:28.972: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-05-25 07:33:28.972: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-05-25 07:33:28.973: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys; Allow fail: 
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Bookmarks
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Preferences
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Passwords
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Autofill Profiles
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Autofill
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Extensions
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Sessions
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Extension settings
2025-05-25 07:33:28.973: [INFO][Sync] Loading: History Delete Directives
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Device Info
2025-05-25 07:33:28.973: [INFO][Sync] Loading: User Consents
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Send Tab To Self
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Web Apps
2025-05-25 07:33:28.973: [INFO][Sync] Loading: History
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Saved Tab Group
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Collection
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Edge E Drop
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Edge Hub App Usage
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Edge Tab Group
2025-05-25 07:33:28.973: [INFO][Sync] Loading: Edge Wallet
2025-05-25 07:33:28.976: [INFO][Sync] All data types are ready for configure.
2025-05-25 07:33:28.976: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Passwords
2025-05-25 07:33:28.976: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-05-25 07:33:28.976: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 07:33:28.978: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-05-25 07:33:28.978: [INFO][Sync] Prepare to configure types: Passwords, Encryption Keys
2025-05-25 07:33:28.978: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Passwords, Encryption Keys with reason: 4
2025-05-25 07:33:28.978: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Passwords, Encryption Keys
2025-05-25 07:33:29.119: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Passwords, Encryption Keys
2025-05-25 07:33:29.120: [INFO][Sync] ConfigurationDone, failed: , succeeded: Passwords, Encryption Keys, remaining count: 3
2025-05-25 07:33:29.120: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-05-25 07:33:29.120: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 07:33:29.120: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-05-25 07:33:29.120: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-05-25 07:33:29.120: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 07:33:29.122: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-05-25 07:33:29.123: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-05-25 07:33:29.123: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-25 07:33:29.123: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-05-25 07:33:29.124: [INFO][Sync]     Configuration completed, state: 3
2025-05-25 07:33:29.124: [INFO][Sync] Configured DataTypeManager: Ok
2025-05-25 07:33:29.125: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-05-25 07:37:18.651: [INFO][Sync] Reset engine, reason: 0
2025-05-25 07:37:18.651: [INFO][Sync] Reset engine with reason: 0
