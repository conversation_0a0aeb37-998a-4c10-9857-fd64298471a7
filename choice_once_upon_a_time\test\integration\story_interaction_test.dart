import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:choice_once_upon_a_time/src/features/story/models/story_model.dart';
import 'package:choice_once_upon_a_time/src/features/story/providers/story_provider.dart';
import 'package:choice_once_upon_a_time/src/features/story/providers/story_settings_provider.dart';
import 'package:choice_once_upon_a_time/src/features/story_interaction/screens/story_interaction_screen.dart';

void main() {
  group('StoryInteractionScreen Integration Tests', () {
    // Create a mock story for testing
    final mockStory = Story(
      id: 'test_story',
      title: 'Test Story',
      description: 'A test story for integration testing',
      coverImagePath: 'assets/images/test_story/cover.jpg',
      firstScene: StoryScene(
        id: 'scene1',
        narrationText: 'This is the first scene of the test story. It contains multiple sentences to test segmentation.',
        imagePath: 'assets/images/test_story/scene1.jpg',
        isChoicePoint: true,
        isEndingScene: false,
        choices: [
          StoryChoice(
            text: 'Go to scene 2',
            nextSceneId: 'scene2',
          ),
          StoryChoice(
            text: 'Go to scene 3',
            nextSceneId: 'scene3',
          ),
        ],
        choicePrompt: 'What would you like to do?',
        defaultNextSceneId: null,
        moralLesson: null,
      ),
      scenes: {
        'scene1': StoryScene(
          id: 'scene1',
          narrationText: 'This is the first scene of the test story. It contains multiple sentences to test segmentation.',
          imagePath: 'assets/images/test_story/scene1.jpg',
          isChoicePoint: true,
          isEndingScene: false,
          choices: [
            StoryChoice(
              text: 'Go to scene 2',
              nextSceneId: 'scene2',
            ),
            StoryChoice(
              text: 'Go to scene 3',
              nextSceneId: 'scene3',
            ),
          ],
          choicePrompt: 'What would you like to do?',
          defaultNextSceneId: null,
          moralLesson: null,
        ),
        'scene2': StoryScene(
          id: 'scene2',
          narrationText: 'This is scene 2. It is not a choice point, so it should auto-proceed.',
          imagePath: 'assets/images/test_story/scene2.jpg',
          isChoicePoint: false,
          isEndingScene: false,
          choices: [],
          choicePrompt: null,
          defaultNextSceneId: 'scene3',
          moralLesson: null,
        ),
        'scene3': StoryScene(
          id: 'scene3',
          narrationText: 'This is scene 3. It is an ending scene.',
          imagePath: 'assets/images/test_story/scene3.jpg',
          isChoicePoint: false,
          isEndingScene: true,
          choices: [],
          choicePrompt: null,
          defaultNextSceneId: null,
          moralLesson: 'This is the moral lesson of the story.',
        ),
      },
    );

    // Helper function to build the widget tree with necessary providers
    Widget buildTestableWidget() {
      return MultiProvider(
        providers: [
          ChangeNotifierProvider<StoryProvider>(
            create: (_) {
              final provider = StoryProvider();
              provider.setActiveStoryForTesting(mockStory);
              return provider;
            },
          ),
          ChangeNotifierProvider<StorySettingsProvider>(
            create: (_) => StorySettingsProvider(),
          ),
        ],
        child: const MaterialApp(
          home: StoryInteractionScreen(
            storyId: 'test_story',
          ),
        ),
      );
    }

    testWidgets('should display the story scene and controls', (WidgetTester tester) async {
      await tester.pumpWidget(buildTestableWidget());
      
      // Wait for the initial loading to complete
      await tester.pumpAndSettle();
      
      // Verify the story control panel is displayed
      expect(find.byType(StoryControlPanel), findsOneWidget);
      
      // Verify navigation buttons are displayed
      expect(find.byIcon(Icons.skip_previous_rounded), findsOneWidget);
      expect(find.byIcon(Icons.skip_next_rounded), findsOneWidget);
      
      // Verify the play/pause button is displayed
      expect(find.byIcon(Icons.pause_circle_filled_rounded), findsOneWidget);
      
      // Verify the settings button is displayed
      expect(find.byIcon(Icons.settings_rounded), findsOneWidget);
    });
    
    // Note: This test is a placeholder and would need to be expanded
    // with actual implementation details for a real integration test
    testWidgets('should show choice popup after narration completes', (WidgetTester tester) async {
      await tester.pumpWidget(buildTestableWidget());
      
      // Wait for the initial loading to complete
      await tester.pumpAndSettle();
      
      // In a real test, we would:
      // 1. Wait for narration to complete
      // 2. Verify the choice popup appears
      // 3. Select a choice
      // 4. Verify navigation to the next scene
      
      // For now, just verify the basic structure is in place
      expect(find.byType(StoryInteractionScreen), findsOneWidget);
    });
  });
}

// Add this extension method to StoryProvider for testing
extension StoryProviderTestExtension on StoryProvider {
  void setActiveStoryForTesting(Story story) {
    // This method would need to be implemented in the actual StoryProvider
    // to allow setting a story directly for testing purposes
  }
}
