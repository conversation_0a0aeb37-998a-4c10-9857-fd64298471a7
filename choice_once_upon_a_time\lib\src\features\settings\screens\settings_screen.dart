import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../features/auth/screens/parent_auth_screen.dart';
import '../../../features/auth/providers/parent_auth_provider.dart';
import '../../../features/profile/providers/active_child_provider.dart';
import '../../../features/profile/screens/child_profile_screen.dart';
import '../../../shared_kernel/theme/app_theme.dart';

/// Screen that displays the settings options
class SettingsScreen extends StatelessWidget {
  /// Constructor
  const SettingsScreen({super.key});

  /// Route name for navigation
  static const routeName = '/settings';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: Consumer<ParentAuthProvider>(
        builder: (context, authProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Authentication section
              if (authProvider.isAuthenticated) ...[
                _buildSettingItem(
                  context,
                  icon: Icons.account_circle,
                  title: 'Parent Account',
                  subtitle: authProvider.parentProfile?.email ?? 'Signed in',
                  onTap: () {
                    // Show account details
                    _showAccountDialog(context, authProvider);
                  },
                ),
                _buildSettingItem(
                  context,
                  icon: Icons.logout,
                  title: 'Sign Out',
                  onTap: () {
                    _showSignOutDialog(context, authProvider);
                  },
                ),
              ] else
                _buildSettingItem(
                  context,
                  icon: Icons.login,
                  title: 'Sign In',
                  subtitle: 'Access cloud sync and premium features',
                  onTap: () {
                    Navigator.pushNamed(context, ParentAuthScreen.routeName);
                  },
                ),

              // Child profile section
              Consumer<ActiveChildProvider>(
                builder: (context, provider, _) {
                  final activeProfile = provider.activeProfile;
                  return _buildSettingItem(
                    context,
                    icon: Icons.person,
                    title: 'Switch Adventurer',
                    subtitle: 'Current: ${activeProfile?.name ?? 'None'}',
                    onTap: () {
                      // Navigate to child profile management
                      Navigator.pushNamed(context, ChildProfileScreen.routeName);
                    },
                  );
                },
              ),

              // App settings
              _buildSettingItem(
                context,
                icon: Icons.palette,
                title: 'App Theme',
                onTap: () {
                  // Navigate to theme selection
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Theme Selection - Coming Soon')),
                  );
                },
              ),
              _buildSettingItem(
                context,
                icon: Icons.volume_up,
                title: 'Music & Sounds',
                onTap: () {
                  // Show sound settings dialog
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Sound Settings - Coming Soon')),
                  );
                },
              ),
              _buildSettingItem(
                context,
                icon: Icons.info,
                title: 'About Us',
                onTap: () {
                  // Show about dialog
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('About Us - Coming Soon')),
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }

  /// Helper method to build a setting item
  Widget _buildSettingItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 24,
          vertical: 12,
        ),
        leading: Icon(
          icon,
          size: 32,
          color: AppTheme.primaryColor,
        ),
        title: Text(
          title,
          style: AppTheme.subheadingStyle.copyWith(fontSize: 18),
        ),
        subtitle: subtitle != null
            ? Text(
                subtitle,
                style: AppTheme.bodyStyle,
              )
            : null,
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: AppTheme.primaryColor,
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  /// Show account details dialog
  void _showAccountDialog(BuildContext context, ParentAuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Account Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Email: ${authProvider.parentProfile?.email ?? 'Unknown'}'),
            const SizedBox(height: 8),
            Text('Name: ${authProvider.parentProfile?.displayName ?? 'Unknown'}'),
            const SizedBox(height: 8),
            Text('Children: ${authProvider.childrenCount}'),
            const SizedBox(height: 8),
            Text('Premium: ${authProvider.isPremium ? 'Active' : 'Not Active'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Show sign out confirmation dialog
  void _showSignOutDialog(BuildContext context, ParentAuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out? Your progress will be saved to the cloud.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              // Show loading
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const AlertDialog(
                  content: Row(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('Signing out...'),
                    ],
                  ),
                ),
              );

              try {
                await authProvider.signOut();
                if (context.mounted) {
                  Navigator.of(context).pop(); // Close loading dialog
                  Navigator.of(context).pop(); // Go back to previous screen
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.of(context).pop(); // Close loading dialog
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error signing out: $e')),
                  );
                }
              }
            },
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}
