import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../features/auth/screens/parent_auth_screen.dart';
import '../../../features/profile/providers/active_child_provider.dart';
import '../../../features/profile/screens/child_profile_screen.dart';
import '../../../shared_kernel/theme/app_theme.dart';

/// Screen that displays the settings options
class SettingsScreen extends StatelessWidget {
  /// Constructor
  const SettingsScreen({super.key});

  /// Route name for navigation
  static const routeName = '/settings';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSettingItem(
            context,
            icon: Icons.shield,
            title: 'Parent Zone',
            onTap: () {
              // Navigate to parent authentication
              Navigator.pushNamed(context, ParentAuthScreen.routeName);
            },
          ),
          Consumer<ActiveChildProvider>(
            builder: (context, provider, _) {
              final activeProfile = provider.activeProfile;
              return _buildSettingItem(
                context,
                icon: Icons.person,
                title: 'Switch Adventurer',
                subtitle: 'Current: ${activeProfile?.name ?? 'None'}',
                onTap: () {
                  // Navigate to child profile management
                  Navigator.pushNamed(context, ChildProfileScreen.routeName);
                },
              );
            },
          ),
          _buildSettingItem(
            context,
            icon: Icons.palette,
            title: 'App Theme',
            onTap: () {
              // Navigate to theme selection
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Theme Selection - Coming Soon')),
              );
            },
          ),
          _buildSettingItem(
            context,
            icon: Icons.volume_up,
            title: 'Music & Sounds',
            onTap: () {
              // Show sound settings dialog
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Sound Settings - Coming Soon')),
              );
            },
          ),
          _buildSettingItem(
            context,
            icon: Icons.info,
            title: 'About Us',
            onTap: () {
              // Show about dialog
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('About Us - Coming Soon')),
              );
            },
          ),
        ],
      ),
    );
  }

  /// Helper method to build a setting item
  Widget _buildSettingItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 24,
          vertical: 12,
        ),
        leading: Icon(
          icon,
          size: 32,
          color: AppTheme.primaryColor,
        ),
        title: Text(
          title,
          style: AppTheme.subheadingStyle.copyWith(fontSize: 18),
        ),
        subtitle: subtitle != null
            ? Text(
                subtitle,
                style: AppTheme.bodyStyle,
              )
            : null,
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: AppTheme.primaryColor,
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }
}
