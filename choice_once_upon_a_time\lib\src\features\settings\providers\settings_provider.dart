import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/config/app_config.dart';

/// Provider for managing app settings
class SettingsProvider extends ChangeNotifier {
  /// Shared preferences instance
  SharedPreferences? _prefs;

  /// App theme mode
  ThemeMode _themeMode = ThemeMode.system;

  /// Background music enabled
  bool _backgroundMusicEnabled = true;

  /// Background music volume (0.0 to 1.0)
  double _backgroundMusicVolume = 0.5;

  /// Sound effects enabled
  bool _soundEffectsEnabled = true;

  /// Sound effects volume (0.0 to 1.0)
  double _soundEffectsVolume = 0.7;

  /// TTS narration enabled
  bool _narrationEnabled = true;

  /// Narration speed (0.3 to 1.5)
  double _narrationSpeed = 0.5;

  /// Natural pauses in narration
  bool _naturalPauses = true;

  /// Push notifications enabled
  bool _pushNotificationsEnabled = true;

  /// Email notifications enabled
  bool _emailNotificationsEnabled = true;

  /// Preferred language
  String _preferredLanguage = 'en';

  /// Maximum screen time per session (minutes)
  int _maxScreenTimeMinutes = 30;

  /// Show reading progress to children
  bool _showProgressToChildren = true;

  /// Auto-save progress
  bool _autoSaveProgress = true;

  /// Getters
  ThemeMode get themeMode => _themeMode;
  bool get backgroundMusicEnabled => _backgroundMusicEnabled;
  double get backgroundMusicVolume => _backgroundMusicVolume;
  bool get soundEffectsEnabled => _soundEffectsEnabled;
  double get soundEffectsVolume => _soundEffectsVolume;
  bool get narrationEnabled => _narrationEnabled;
  double get narrationSpeed => _narrationSpeed;
  bool get naturalPauses => _naturalPauses;
  bool get pushNotificationsEnabled => _pushNotificationsEnabled;
  bool get emailNotificationsEnabled => _emailNotificationsEnabled;
  String get preferredLanguage => _preferredLanguage;
  int get maxScreenTimeMinutes => _maxScreenTimeMinutes;
  bool get showProgressToChildren => _showProgressToChildren;
  bool get autoSaveProgress => _autoSaveProgress;

  /// Initialize settings
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      AppConfig.logInfo('Settings initialized successfully');
    } catch (e) {
      AppConfig.logError('Failed to initialize settings: $e');
    }
  }

  /// Load settings from storage
  Future<void> _loadSettings() async {
    if (_prefs == null) return;

    try {
      // Theme
      final themeIndex = _prefs!.getInt('theme_mode') ?? ThemeMode.system.index;
      _themeMode = ThemeMode.values[themeIndex];

      // Audio settings
      _backgroundMusicEnabled = _prefs!.getBool('background_music_enabled') ?? true;
      _backgroundMusicVolume = _prefs!.getDouble('background_music_volume') ?? 0.5;
      _soundEffectsEnabled = _prefs!.getBool('sound_effects_enabled') ?? true;
      _soundEffectsVolume = _prefs!.getDouble('sound_effects_volume') ?? 0.7;

      // Narration settings
      _narrationEnabled = _prefs!.getBool('narration_enabled') ?? true;
      _narrationSpeed = _prefs!.getDouble('narration_speed') ?? 0.5;
      _naturalPauses = _prefs!.getBool('natural_pauses') ?? true;

      // Notification settings
      _pushNotificationsEnabled = _prefs!.getBool('push_notifications_enabled') ?? true;
      _emailNotificationsEnabled = _prefs!.getBool('email_notifications_enabled') ?? true;

      // General settings
      _preferredLanguage = _prefs!.getString('preferred_language') ?? 'en';
      _maxScreenTimeMinutes = _prefs!.getInt('max_screen_time_minutes') ?? 30;
      _showProgressToChildren = _prefs!.getBool('show_progress_to_children') ?? true;
      _autoSaveProgress = _prefs!.getBool('auto_save_progress') ?? true;

      notifyListeners();
    } catch (e) {
      AppConfig.logError('Failed to load settings: $e');
    }
  }

  /// Set theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;

    _themeMode = mode;
    await _prefs?.setInt('theme_mode', mode.index);
    notifyListeners();
  }

  /// Set background music enabled
  Future<void> setBackgroundMusicEnabled(bool enabled) async {
    if (_backgroundMusicEnabled == enabled) return;

    _backgroundMusicEnabled = enabled;
    await _prefs?.setBool('background_music_enabled', enabled);
    notifyListeners();
  }

  /// Set background music volume
  Future<void> setBackgroundMusicVolume(double volume) async {
    final clampedVolume = volume.clamp(0.0, 1.0);
    if (_backgroundMusicVolume == clampedVolume) return;

    _backgroundMusicVolume = clampedVolume;
    await _prefs?.setDouble('background_music_volume', clampedVolume);
    notifyListeners();
  }

  /// Set sound effects enabled
  Future<void> setSoundEffectsEnabled(bool enabled) async {
    if (_soundEffectsEnabled == enabled) return;

    _soundEffectsEnabled = enabled;
    await _prefs?.setBool('sound_effects_enabled', enabled);
    notifyListeners();
  }

  /// Set sound effects volume
  Future<void> setSoundEffectsVolume(double volume) async {
    final clampedVolume = volume.clamp(0.0, 1.0);
    if (_soundEffectsVolume == clampedVolume) return;

    _soundEffectsVolume = clampedVolume;
    await _prefs?.setDouble('sound_effects_volume', clampedVolume);
    notifyListeners();
  }

  /// Set narration enabled
  Future<void> setNarrationEnabled(bool enabled) async {
    if (_narrationEnabled == enabled) return;

    _narrationEnabled = enabled;
    await _prefs?.setBool('narration_enabled', enabled);
    notifyListeners();
  }

  /// Set narration speed
  Future<void> setNarrationSpeed(double speed) async {
    final clampedSpeed = speed.clamp(0.3, 1.5);
    if (_narrationSpeed == clampedSpeed) return;

    _narrationSpeed = clampedSpeed;
    await _prefs?.setDouble('narration_speed', clampedSpeed);
    notifyListeners();
  }

  /// Set natural pauses
  Future<void> setNaturalPauses(bool enabled) async {
    if (_naturalPauses == enabled) return;

    _naturalPauses = enabled;
    await _prefs?.setBool('natural_pauses', enabled);
    notifyListeners();
  }

  /// Set push notifications enabled
  Future<void> setPushNotificationsEnabled(bool enabled) async {
    if (_pushNotificationsEnabled == enabled) return;

    _pushNotificationsEnabled = enabled;
    await _prefs?.setBool('push_notifications_enabled', enabled);
    notifyListeners();
  }

  /// Set email notifications enabled
  Future<void> setEmailNotificationsEnabled(bool enabled) async {
    if (_emailNotificationsEnabled == enabled) return;

    _emailNotificationsEnabled = enabled;
    await _prefs?.setBool('email_notifications_enabled', enabled);
    notifyListeners();
  }

  /// Set preferred language
  Future<void> setPreferredLanguage(String language) async {
    if (_preferredLanguage == language) return;

    _preferredLanguage = language;
    await _prefs?.setString('preferred_language', language);
    notifyListeners();
  }

  /// Set maximum screen time
  Future<void> setMaxScreenTimeMinutes(int minutes) async {
    final clampedMinutes = minutes.clamp(5, 120); // 5 minutes to 2 hours
    if (_maxScreenTimeMinutes == clampedMinutes) return;

    _maxScreenTimeMinutes = clampedMinutes;
    await _prefs?.setInt('max_screen_time_minutes', clampedMinutes);
    notifyListeners();
  }

  /// Set show progress to children
  Future<void> setShowProgressToChildren(bool show) async {
    if (_showProgressToChildren == show) return;

    _showProgressToChildren = show;
    await _prefs?.setBool('show_progress_to_children', show);
    notifyListeners();
  }

  /// Set auto-save progress
  Future<void> setAutoSaveProgress(bool autoSave) async {
    if (_autoSaveProgress == autoSave) return;

    _autoSaveProgress = autoSave;
    await _prefs?.setBool('auto_save_progress', autoSave);
    notifyListeners();
  }

  /// Reset all settings to defaults
  Future<void> resetToDefaults() async {
    _themeMode = ThemeMode.system;
    _backgroundMusicEnabled = true;
    _backgroundMusicVolume = 0.5;
    _soundEffectsEnabled = true;
    _soundEffectsVolume = 0.7;
    _narrationEnabled = true;
    _narrationSpeed = 0.5;
    _naturalPauses = true;
    _pushNotificationsEnabled = true;
    _emailNotificationsEnabled = true;
    _preferredLanguage = 'en';
    _maxScreenTimeMinutes = 30;
    _showProgressToChildren = true;
    _autoSaveProgress = true;

    // Clear all stored settings
    await _prefs?.clear();

    notifyListeners();
  }

  /// Export settings as JSON
  Map<String, dynamic> exportSettings() {
    return {
      'themeMode': _themeMode.index,
      'backgroundMusicEnabled': _backgroundMusicEnabled,
      'backgroundMusicVolume': _backgroundMusicVolume,
      'soundEffectsEnabled': _soundEffectsEnabled,
      'soundEffectsVolume': _soundEffectsVolume,
      'narrationEnabled': _narrationEnabled,
      'narrationSpeed': _narrationSpeed,
      'naturalPauses': _naturalPauses,
      'pushNotificationsEnabled': _pushNotificationsEnabled,
      'emailNotificationsEnabled': _emailNotificationsEnabled,
      'preferredLanguage': _preferredLanguage,
      'maxScreenTimeMinutes': _maxScreenTimeMinutes,
      'showProgressToChildren': _showProgressToChildren,
      'autoSaveProgress': _autoSaveProgress,
    };
  }

  /// Import settings from JSON
  Future<void> importSettings(Map<String, dynamic> settings) async {
    try {
      _themeMode = ThemeMode.values[settings['themeMode'] ?? ThemeMode.system.index];
      _backgroundMusicEnabled = settings['backgroundMusicEnabled'] ?? true;
      _backgroundMusicVolume = (settings['backgroundMusicVolume'] ?? 0.5).toDouble();
      _soundEffectsEnabled = settings['soundEffectsEnabled'] ?? true;
      _soundEffectsVolume = (settings['soundEffectsVolume'] ?? 0.7).toDouble();
      _narrationEnabled = settings['narrationEnabled'] ?? true;
      _narrationSpeed = (settings['narrationSpeed'] ?? 0.5).toDouble();
      _naturalPauses = settings['naturalPauses'] ?? true;
      _pushNotificationsEnabled = settings['pushNotificationsEnabled'] ?? true;
      _emailNotificationsEnabled = settings['emailNotificationsEnabled'] ?? true;
      _preferredLanguage = settings['preferredLanguage'] ?? 'en';
      _maxScreenTimeMinutes = settings['maxScreenTimeMinutes'] ?? 30;
      _showProgressToChildren = settings['showProgressToChildren'] ?? true;
      _autoSaveProgress = settings['autoSaveProgress'] ?? true;

      // Save to storage
      await _saveAllSettings();
      notifyListeners();
    } catch (e) {
      AppConfig.logError('Failed to import settings: $e');
    }
  }

  /// Save all settings to storage
  Future<void> _saveAllSettings() async {
    if (_prefs == null) return;

    await Future.wait([
      _prefs!.setInt('theme_mode', _themeMode.index),
      _prefs!.setBool('background_music_enabled', _backgroundMusicEnabled),
      _prefs!.setDouble('background_music_volume', _backgroundMusicVolume),
      _prefs!.setBool('sound_effects_enabled', _soundEffectsEnabled),
      _prefs!.setDouble('sound_effects_volume', _soundEffectsVolume),
      _prefs!.setBool('narration_enabled', _narrationEnabled),
      _prefs!.setDouble('narration_speed', _narrationSpeed),
      _prefs!.setBool('natural_pauses', _naturalPauses),
      _prefs!.setBool('push_notifications_enabled', _pushNotificationsEnabled),
      _prefs!.setBool('email_notifications_enabled', _emailNotificationsEnabled),
      _prefs!.setString('preferred_language', _preferredLanguage),
      _prefs!.setInt('max_screen_time_minutes', _maxScreenTimeMinutes),
      _prefs!.setBool('show_progress_to_children', _showProgressToChildren),
      _prefs!.setBool('auto_save_progress', _autoSaveProgress),
    ]);
  }
}
