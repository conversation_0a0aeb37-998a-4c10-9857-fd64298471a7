

/// Service to segment text into smaller, manageable chunks
class TextSegmenter {
  /// Split text into sentences
  static List<String> splitIntoSentences(String text) {
    if (text.isEmpty) {
      return [];
    }

    // Split by common sentence terminators (., !, ?)
    // but keep the terminators with the sentence
    final RegExp sentencePattern = RegExp(r'[^.!?]+[.!?]');
    final Iterable<Match> matches = sentencePattern.allMatches(text);

    final List<String> sentences = matches
        .map((match) => match.group(0)?.trim() ?? '')
        .where((sentence) => sentence.isNotEmpty)
        .toList();

    // If no sentences were found (e.g., text without punctuation),
    // return the original text as a single segment
    if (sentences.isEmpty) {
      return [text];
    }

    return sentences;
  }

  /// Split text into segments of approximately equal length
  static List<String> splitIntoSegments(String text, {int targetLength = 150}) {
    if (text.isEmpty) {
      return [];
    }

    // First try to split by sentences
    final List<String> sentences = splitIntoSentences(text);

    // If we have only one sentence and it's longer than the target length,
    // split it into smaller chunks
    if (sentences.length == 1 && sentences[0].length > targetLength) {
      return _splitLongSentence(sentences[0], targetLength);
    }

    // If we have multiple sentences, combine them into segments
    // that are approximately the target length
    final List<String> segments = [];
    String currentSegment = '';

    for (final sentence in sentences) {
      // If adding this sentence would make the segment too long,
      // add the current segment to the list and start a new one
      if (currentSegment.isNotEmpty &&
          currentSegment.length + sentence.length > targetLength) {
        segments.add(currentSegment);
        currentSegment = sentence;
      } else {
        // Otherwise, add the sentence to the current segment
        currentSegment = currentSegment.isEmpty
            ? sentence
            : '$currentSegment $sentence';
      }
    }

    // Add the last segment if it's not empty
    if (currentSegment.isNotEmpty) {
      segments.add(currentSegment);
    }

    return segments;
  }

  /// Split a long sentence into smaller chunks
  static List<String> _splitLongSentence(String sentence, int targetLength) {
    final List<String> chunks = [];

    // Split by commas, semicolons, or other natural breaks
    final RegExp breakPattern = RegExp(r'[^,;:]+[,;:]');
    final Iterable<Match> matches = breakPattern.allMatches(sentence);

    if (matches.isNotEmpty) {
      // If we found natural breaks, use them
      String currentChunk = '';

      for (final match in matches) {
        final String part = match.group(0)?.trim() ?? '';

        if (part.isEmpty) {
          continue;
        }

        if (currentChunk.isNotEmpty &&
            currentChunk.length + part.length > targetLength) {
          chunks.add(currentChunk);
          currentChunk = part;
        } else {
          currentChunk = currentChunk.isEmpty
              ? part
              : '$currentChunk $part';
        }
      }

      if (currentChunk.isNotEmpty) {
        chunks.add(currentChunk);
      }
    } else {
      // If no natural breaks, split by words
      final List<String> words = sentence.split(' ');
      String currentChunk = '';

      for (final word in words) {
        if (currentChunk.isNotEmpty &&
            currentChunk.length + word.length + 1 > targetLength) {
          chunks.add(currentChunk);
          currentChunk = word;
        } else {
          currentChunk = currentChunk.isEmpty
              ? word
              : '$currentChunk $word';
        }
      }

      if (currentChunk.isNotEmpty) {
        chunks.add(currentChunk);
      }
    }

    return chunks;
  }
}
