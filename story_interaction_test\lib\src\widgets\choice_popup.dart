import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/story_model.dart';
import '../providers/story_provider.dart';
import '../services/tts_service.dart';

/// Popup widget for displaying story choices
class ChoicePopup extends StatefulWidget {
  const ChoicePopup({super.key});

  @override
  State<ChoicePopup> createState() => _ChoicePopupState();
}

class _ChoicePopupState extends State<ChoicePopup>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  
  final TTSService _ttsService = TTSService();
  int _currentNarratingChoiceIndex = -1;
  bool _hasNarratedPrompt = false;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _animationController.forward();
    
    // Start narrating the choices after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      _startChoiceNarration();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<StoryProvider>(
      builder: (context, storyProvider, child) {
        final currentScene = storyProvider.currentScene;
        if (currentScene == null || !currentScene.isChoicePoint) {
          return const SizedBox.shrink();
        }

        return Stack(
          children: [
            // Backdrop blur
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
              child: Container(
                color: Colors.black.withAlpha(128),
              ),
            ),
            
            // Choice popup
            Center(
              child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: Opacity(
                      opacity: _opacityAnimation.value,
                      child: _buildChoiceDialog(currentScene),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildChoiceDialog(StoryScene scene) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 40),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(77),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          const Icon(
            Icons.help_outline,
            size: 48,
            color: Colors.blue,
          ),
          const SizedBox(height: 16),
          
          // Prompt
          Text(
            scene.choicePrompt ?? 'What would you like to do?',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 24),
          
          // Choice buttons
          ...scene.choices.asMap().entries.map((entry) {
            final index = entry.key;
            final choice = entry.value;
            return _buildChoiceButton(choice, index);
          }),
          
          const SizedBox(height: 16),
          
          // Replay audio button
          TextButton.icon(
            onPressed: _replayChoiceAudio,
            icon: const Icon(Icons.replay),
            label: const Text('Replay Choices'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChoiceButton(StoryChoice choice, int index) {
    final isNarrating = _currentNarratingChoiceIndex == index;
    
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 6),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        transform: Matrix4.identity()
          ..scale(isNarrating ? 1.05 : 1.0),
        child: ElevatedButton(
          onPressed: () => _selectChoice(choice),
          style: ElevatedButton.styleFrom(
            backgroundColor: isNarrating ? Colors.blue[100] : Colors.blue,
            foregroundColor: isNarrating ? Colors.blue[800] : Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: isNarrating
                  ? BorderSide(color: Colors.blue[300]!, width: 2)
                  : BorderSide.none,
            ),
            elevation: isNarrating ? 8 : 2,
          ),
          child: Text(
            choice.text,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  void _selectChoice(StoryChoice choice) {
    final storyProvider = Provider.of<StoryProvider>(context, listen: false);
    
    // Stop any current narration
    _ttsService.stop();
    
    // Animate out and make choice
    _animationController.reverse().then((_) {
      storyProvider.makeChoice(choice);
    });
  }

  Future<void> _startChoiceNarration() async {
    final storyProvider = Provider.of<StoryProvider>(context, listen: false);
    final currentScene = storyProvider.currentScene;
    
    if (currentScene == null || !mounted) return;
    
    try {
      // First, narrate the prompt if not already done
      if (!_hasNarratedPrompt) {
        final prompt = currentScene.choicePrompt ?? 'What would you like to do?';
        await _ttsService.speak(prompt);
        _hasNarratedPrompt = true;
        
        if (!mounted) return;
        await Future.delayed(const Duration(milliseconds: 500));
      }
      
      // Then narrate each choice
      for (int i = 0; i < currentScene.choices.length; i++) {
        if (!mounted) break;
        
        setState(() {
          _currentNarratingChoiceIndex = i;
        });
        
        await _ttsService.speak(currentScene.choices[i].text);
        
        if (!mounted) break;
        await Future.delayed(const Duration(milliseconds: 300));
      }
      
      if (mounted) {
        setState(() {
          _currentNarratingChoiceIndex = -1;
        });
      }
    } catch (e) {
      debugPrint('Error during choice narration: $e');
    }
  }

  void _replayChoiceAudio() {
    _hasNarratedPrompt = false;
    _currentNarratingChoiceIndex = -1;
    _ttsService.stop();
    _startChoiceNarration();
  }
}
