﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{B903937C-8F8A-313B-82E2-78E4ED039F2A}"
	ProjectSection(ProjectDependencies) = postProject
		{0263BFF7-A462-35C6-8551-50C6425CEB40} = {0263BFF7-A462-35C6-8551-50C6425CEB40}
		{0B0E515B-4455-34CD-9BF0-EF082047E4AE} = {0B0E515B-4455-34CD-9BF0-EF082047E4AE}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{C31A1B3B-A5D1-3CEC-B2E6-9A1692133945}"
	ProjectSection(ProjectDependencies) = postProject
		{B903937C-8F8A-313B-82E2-78E4ED039F2A} = {B903937C-8F8A-313B-82E2-78E4ED039F2A}
		{0263BFF7-A462-35C6-8551-50C6425CEB40} = {0263BFF7-A462-35C6-8551-50C6425CEB40}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{0263BFF7-A462-35C6-8551-50C6425CEB40}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "firebase_core_plugin", "firebase_core_plugin.vcxproj", "{0B0E515B-4455-34CD-9BF0-EF082047E4AE}"
	ProjectSection(ProjectDependencies) = postProject
		{0263BFF7-A462-35C6-8551-50C6425CEB40} = {0263BFF7-A462-35C6-8551-50C6425CEB40}
		{9DAE3D34-D9E7-3F70-85EA-667DA31E1338} = {9DAE3D34-D9E7-3F70-85EA-667DA31E1338}
		{64136831-B088-3990-B5B2-59560F0BB042} = {64136831-B088-3990-B5B2-59560F0BB042}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{9DAE3D34-D9E7-3F70-85EA-667DA31E1338}"
	ProjectSection(ProjectDependencies) = postProject
		{0263BFF7-A462-35C6-8551-50C6425CEB40} = {0263BFF7-A462-35C6-8551-50C6425CEB40}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{64136831-B088-3990-B5B2-59560F0BB042}"
	ProjectSection(ProjectDependencies) = postProject
		{0263BFF7-A462-35C6-8551-50C6425CEB40} = {0263BFF7-A462-35C6-8551-50C6425CEB40}
		{9DAE3D34-D9E7-3F70-85EA-667DA31E1338} = {9DAE3D34-D9E7-3F70-85EA-667DA31E1338}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B903937C-8F8A-313B-82E2-78E4ED039F2A}.Debug|x64.ActiveCfg = Debug|x64
		{B903937C-8F8A-313B-82E2-78E4ED039F2A}.Debug|x64.Build.0 = Debug|x64
		{B903937C-8F8A-313B-82E2-78E4ED039F2A}.Profile|x64.ActiveCfg = Profile|x64
		{B903937C-8F8A-313B-82E2-78E4ED039F2A}.Profile|x64.Build.0 = Profile|x64
		{B903937C-8F8A-313B-82E2-78E4ED039F2A}.Release|x64.ActiveCfg = Release|x64
		{B903937C-8F8A-313B-82E2-78E4ED039F2A}.Release|x64.Build.0 = Release|x64
		{C31A1B3B-A5D1-3CEC-B2E6-9A1692133945}.Debug|x64.ActiveCfg = Debug|x64
		{C31A1B3B-A5D1-3CEC-B2E6-9A1692133945}.Profile|x64.ActiveCfg = Profile|x64
		{C31A1B3B-A5D1-3CEC-B2E6-9A1692133945}.Release|x64.ActiveCfg = Release|x64
		{0263BFF7-A462-35C6-8551-50C6425CEB40}.Debug|x64.ActiveCfg = Debug|x64
		{0263BFF7-A462-35C6-8551-50C6425CEB40}.Debug|x64.Build.0 = Debug|x64
		{0263BFF7-A462-35C6-8551-50C6425CEB40}.Profile|x64.ActiveCfg = Profile|x64
		{0263BFF7-A462-35C6-8551-50C6425CEB40}.Profile|x64.Build.0 = Profile|x64
		{0263BFF7-A462-35C6-8551-50C6425CEB40}.Release|x64.ActiveCfg = Release|x64
		{0263BFF7-A462-35C6-8551-50C6425CEB40}.Release|x64.Build.0 = Release|x64
		{0B0E515B-4455-34CD-9BF0-EF082047E4AE}.Debug|x64.ActiveCfg = Debug|x64
		{0B0E515B-4455-34CD-9BF0-EF082047E4AE}.Debug|x64.Build.0 = Debug|x64
		{0B0E515B-4455-34CD-9BF0-EF082047E4AE}.Profile|x64.ActiveCfg = Profile|x64
		{0B0E515B-4455-34CD-9BF0-EF082047E4AE}.Profile|x64.Build.0 = Profile|x64
		{0B0E515B-4455-34CD-9BF0-EF082047E4AE}.Release|x64.ActiveCfg = Release|x64
		{0B0E515B-4455-34CD-9BF0-EF082047E4AE}.Release|x64.Build.0 = Release|x64
		{9DAE3D34-D9E7-3F70-85EA-667DA31E1338}.Debug|x64.ActiveCfg = Debug|x64
		{9DAE3D34-D9E7-3F70-85EA-667DA31E1338}.Debug|x64.Build.0 = Debug|x64
		{9DAE3D34-D9E7-3F70-85EA-667DA31E1338}.Profile|x64.ActiveCfg = Profile|x64
		{9DAE3D34-D9E7-3F70-85EA-667DA31E1338}.Profile|x64.Build.0 = Profile|x64
		{9DAE3D34-D9E7-3F70-85EA-667DA31E1338}.Release|x64.ActiveCfg = Release|x64
		{9DAE3D34-D9E7-3F70-85EA-667DA31E1338}.Release|x64.Build.0 = Release|x64
		{64136831-B088-3990-B5B2-59560F0BB042}.Debug|x64.ActiveCfg = Debug|x64
		{64136831-B088-3990-B5B2-59560F0BB042}.Debug|x64.Build.0 = Debug|x64
		{64136831-B088-3990-B5B2-59560F0BB042}.Profile|x64.ActiveCfg = Profile|x64
		{64136831-B088-3990-B5B2-59560F0BB042}.Profile|x64.Build.0 = Profile|x64
		{64136831-B088-3990-B5B2-59560F0BB042}.Release|x64.ActiveCfg = Release|x64
		{64136831-B088-3990-B5B2-59560F0BB042}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E0010391-E33C-3451-8BD7-12E796F0EEE1}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
