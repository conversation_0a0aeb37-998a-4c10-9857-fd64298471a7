import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/story_provider.dart';
import '../providers/story_settings_provider.dart';
import '../services/text_segmenter.dart';
import 'sentence_progress_indicator.dart';
import 'highlighted_text.dart';

/// Bottom control panel for story interaction
class StoryControlPanel extends StatelessWidget {
  const StoryControlPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<StoryProvider, StorySettingsProvider>(
      builder: (context, storyProvider, settingsProvider, child) {
        final screenSize = MediaQuery.of(context).size;
        final panelHeight = screenSize.height * 0.25; // 25% of screen height

        return Container(
          height: panelHeight,
          decoration: BoxDecoration(
            color: Colors.black.withAlpha((settingsProvider.panelOpacity * 255).round()),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Top row: Subtitle text display
                Expanded(
                  flex: 3,
                  child: _buildSubtitleDisplay(storyProvider, settingsProvider),
                ),
                
                const SizedBox(height: 12),
                
                // Middle row: Progress indicator
                _buildProgressIndicator(storyProvider),
                
                const SizedBox(height: 12),
                
                // Bottom row: Control buttons
                _buildControlButtons(context, storyProvider),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSubtitleDisplay(StoryProvider storyProvider, StorySettingsProvider settingsProvider) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(230),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: SingleChildScrollView(
          child: HighlightedText(
            text: storyProvider.currentSegmentText,
            currentWordIndex: storyProvider.currentWordIndex,
            style: TextStyle(
              fontSize: settingsProvider.fontSize,
              fontFamily: settingsProvider.fontFamily,
              color: Colors.black87,
              height: 1.4,
            ),
            highlightColor: Colors.yellow.withAlpha(128),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(StoryProvider storyProvider) {
    return SentenceProgressIndicator(
      sentences: storyProvider.currentTextSegments,
      currentSentenceIndex: storyProvider.currentSegmentIndex,
      dotSize: 8.0,
      spacing: 4.0,
      activeDotColor: Colors.blue,
      inactiveDotColor: Colors.white.withAlpha(128),
    );
  }

  Widget _buildControlButtons(BuildContext context, StoryProvider storyProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Previous button
        _buildControlButton(
          icon: Icons.skip_previous_rounded,
          onPressed: storyProvider.canGoToPreviousSegment
              ? () => storyProvider.goToPreviousSegment()
              : null,
          tooltip: 'Previous',
        ),
        
        // Play/Pause button
        _buildControlButton(
          icon: storyProvider.isNarrating
              ? Icons.pause_circle_filled_rounded
              : Icons.play_circle_filled_rounded,
          onPressed: () => storyProvider.toggleNarration(),
          tooltip: storyProvider.isNarrating ? 'Pause' : 'Play',
          size: 56,
        ),
        
        // Next button
        _buildControlButton(
          icon: Icons.skip_next_rounded,
          onPressed: storyProvider.canGoToNextSegment
              ? () => storyProvider.goToNextSegment()
              : null,
          tooltip: 'Next',
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required String tooltip,
    double size = 48,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: onPressed != null ? Colors.white : Colors.grey[400],
          borderRadius: BorderRadius.circular(size / 2),
          boxShadow: onPressed != null
              ? [
                  BoxShadow(
                    color: Colors.black.withAlpha(51),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: IconButton(
          icon: Icon(
            icon,
            color: onPressed != null ? Colors.blue : Colors.grey[600],
            size: size * 0.6,
          ),
          onPressed: onPressed,
        ),
      ),
    );
  }
}
