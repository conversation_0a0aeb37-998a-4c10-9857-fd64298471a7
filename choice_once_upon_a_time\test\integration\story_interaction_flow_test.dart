import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../lib/main.dart' as app;
import '../../lib/src/features/story/models/story.dart';
import '../../lib/src/features/story/models/scene.dart';
import '../helpers/test_helpers.dart';
import '../mocks/mock_services.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Story Interaction Flow Integration Tests', () {
    late MockFirebaseAuthService mockAuthService;
    late Story testStory;

    setUpAll(() {
      mockAuthService = MockFirebaseAuthService();
      
      // Create a test story with multiple scenes and choices
      testStory = Story(
        id: 'test-story-1',
        title: 'Test Adventure',
        description: 'A test story for integration testing',
        coverImagePath: 'assets/images/test_cover.jpg',
        isLocked: false,
        scenes: [
          Scene(
            id: 'scene1',
            text: 'Welcome to the magical forest. You see two paths ahead.',
            backgroundImagePath: 'assets/images/forest.jpg',
            choices: [
              Choice(
                id: 'choice1',
                text: 'Take the left path',
                nextSceneId: 'scene2',
              ),
              Choice(
                id: 'choice2',
                text: 'Take the right path',
                nextSceneId: 'scene3',
              ),
            ],
          ),
          Scene(
            id: 'scene2',
            text: 'You find a friendly fairy who offers to help you.',
            backgroundImagePath: 'assets/images/fairy.jpg',
            choices: [
              Choice(
                id: 'choice3',
                text: 'Accept the fairy\'s help',
                nextSceneId: 'scene4',
              ),
              Choice(
                id: 'choice4',
                text: 'Politely decline',
                nextSceneId: 'scene5',
              ),
            ],
          ),
          Scene(
            id: 'scene3',
            text: 'You encounter a sleeping dragon blocking your path.',
            backgroundImagePath: 'assets/images/dragon.jpg',
            choices: [
              Choice(
                id: 'choice5',
                text: 'Try to sneak past',
                nextSceneId: 'scene6',
              ),
              Choice(
                id: 'choice6',
                text: 'Wake the dragon',
                nextSceneId: 'scene7',
              ),
            ],
          ),
          Scene(
            id: 'scene4',
            text: 'With the fairy\'s magic, you safely reach the treasure!',
            backgroundImagePath: 'assets/images/treasure.jpg',
            choices: [], // Ending scene
          ),
          Scene(
            id: 'scene5',
            text: 'You continue alone and find a hidden cave with treasure.',
            backgroundImagePath: 'assets/images/cave.jpg',
            choices: [], // Ending scene
          ),
          Scene(
            id: 'scene6',
            text: 'You successfully sneak past and find the treasure!',
            backgroundImagePath: 'assets/images/treasure.jpg',
            choices: [], // Ending scene
          ),
          Scene(
            id: 'scene7',
            text: 'The dragon wakes up but is friendly and shares its treasure!',
            backgroundImagePath: 'assets/images/friendly_dragon.jpg',
            choices: [], // Ending scene
          ),
        ],
      );
    });

    setUp(() {
      // Set up authenticated state with child profile
      mockAuthService.setAuthState(
        isSignedIn: true,
        userEmail: '<EMAIL>',
        userId: 'test-user-id',
      );

      MockFirestore.clear();
      MockFirestore.setDocument(
        'users/test-user-id',
        TestDataFactory.createUserData(
          children: [TestDataFactory.createChildData(name: 'Test Child')],
        ),
      );
    });

    testWidgets('Complete story playthrough - left path', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Complete story playthrough - left path',
        () async {
          // Launch app and navigate to story
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Should be authenticated and at main menu
          expect(find.text('New Story'), findsOneWidget);

          // Navigate to story library
          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          // Select test story
          await tester.tap(find.text('Test Adventure'));
          await tester.pumpAndSettle();

          // Should be at story interaction screen
          expect(find.text('Welcome to the magical forest'), findsOneWidget);
          expect(find.text('Take the left path'), findsOneWidget);
          expect(find.text('Take the right path'), findsOneWidget);

          // Test narration controls
          expect(find.byIcon(Icons.play_arrow), findsOneWidget);
          expect(find.byIcon(Icons.pause), findsNothing); // Not playing yet

          // Start narration
          await tester.tap(find.byIcon(Icons.play_arrow));
          await tester.pump();

          // Should show pause button
          expect(find.byIcon(Icons.pause), findsOneWidget);

          // Wait for narration to complete (mocked)
          await tester.pumpAndSettle();

          // Choices should be enabled after narration
          expect(find.text('Take the left path'), findsOneWidget);

          // Make first choice - left path
          await tester.tap(find.text('Take the left path'));
          await tester.pumpAndSettle();

          // Should advance to scene 2
          expect(find.text('You find a friendly fairy'), findsOneWidget);
          expect(find.text('Accept the fairy\'s help'), findsOneWidget);
          expect(find.text('Politely decline'), findsOneWidget);

          // Make second choice - accept help
          await tester.tap(find.text('Accept the fairy\'s help'));
          await tester.pumpAndSettle();

          // Should reach ending scene
          expect(find.text('With the fairy\'s magic, you safely reach the treasure!'), findsOneWidget);

          // Should show story completion
          expect(find.text('Story Complete'), findsOneWidget);
          expect(find.text('Return to Menu'), findsOneWidget);

          // Return to menu
          await tester.tap(find.text('Return to Menu'));
          await tester.pumpAndSettle();

          // Should be back at main menu
          expect(find.text('New Story'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Complete story playthrough - right path', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Complete story playthrough - right path',
        () async {
          // Launch app and navigate to story
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.text('Test Adventure'));
          await tester.pumpAndSettle();

          // Make first choice - right path
          await tester.tap(find.text('Take the right path'));
          await tester.pumpAndSettle();

          // Should advance to scene 3
          expect(find.text('You encounter a sleeping dragon'), findsOneWidget);
          expect(find.text('Try to sneak past'), findsOneWidget);
          expect(find.text('Wake the dragon'), findsOneWidget);

          // Make second choice - wake dragon
          await tester.tap(find.text('Wake the dragon'));
          await tester.pumpAndSettle();

          // Should reach different ending
          expect(find.text('The dragon wakes up but is friendly'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Story progress saving and resuming', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Story progress saving and resuming',
        () async {
          // Start story
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.text('Test Adventure'));
          await tester.pumpAndSettle();

          // Make first choice
          await tester.tap(find.text('Take the left path'));
          await tester.pumpAndSettle();

          // Exit story (go back to menu)
          await tester.tap(find.byIcon(Icons.arrow_back));
          await tester.pumpAndSettle();

          // Should be back at main menu
          expect(find.text('Continue Story'), findsOneWidget);

          // Resume story
          await tester.tap(find.text('Continue Story'));
          await tester.pumpAndSettle();

          // Should resume from scene 2
          expect(find.text('You find a friendly fairy'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Narration controls and settings', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Narration controls and settings',
        () async {
          // Launch story
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.text('Test Adventure'));
          await tester.pumpAndSettle();

          // Test play/pause controls
          await tester.tap(find.byIcon(Icons.play_arrow));
          await tester.pump();
          expect(find.byIcon(Icons.pause), findsOneWidget);

          await tester.tap(find.byIcon(Icons.pause));
          await tester.pump();
          expect(find.byIcon(Icons.play_arrow), findsOneWidget);

          // Test speed controls
          expect(find.byIcon(Icons.speed), findsOneWidget);
          await tester.tap(find.byIcon(Icons.speed));
          await tester.pumpAndSettle();

          // Should show speed options
          expect(find.text('0.5x'), findsOneWidget);
          expect(find.text('1.0x'), findsOneWidget);
          expect(find.text('1.5x'), findsOneWidget);

          // Select different speed
          await tester.tap(find.text('1.5x'));
          await tester.pumpAndSettle();

          // Test subtitle toggle
          expect(find.byIcon(Icons.subtitles), findsOneWidget);
          await tester.tap(find.byIcon(Icons.subtitles));
          await tester.pump();

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Progress indicators and scene navigation', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Progress indicators and scene navigation',
        () async {
          // Launch story
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.text('Test Adventure'));
          await tester.pumpAndSettle();

          // Should show progress indicators
          expect(find.byType(LinearProgressIndicator), findsOneWidget);

          // Should show sentence progress dots
          expect(find.byIcon(Icons.circle), findsWidgets);

          // Make choices and verify progress updates
          await tester.tap(find.text('Take the left path'));
          await tester.pumpAndSettle();

          // Progress should have advanced
          // This would require checking the actual progress value

          await tester.tap(find.text('Accept the fairy\'s help'));
          await tester.pumpAndSettle();

          // Should show completion progress
          expect(find.text('Story Complete'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Responsive design in story interaction', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Responsive design in story interaction',
        () async {
          for (final screenSize in TestHelpers.allScreenSizes) {
            await tester.binding.setSurfaceSize(screenSize);
            await tester.pumpAndSettle();

            // Launch story
            await tester.pumpWidget(app.MyApp());
            await tester.pumpAndSettle();

            await tester.tap(find.text('New Story'));
            await tester.pumpAndSettle();

            await tester.tap(find.text('Test Adventure'));
            await tester.pumpAndSettle();

            // Should display properly without overflow
            expect(find.text('Welcome to the magical forest'), findsOneWidget);
            expect(find.text('Take the left path'), findsOneWidget);
            expect(find.text('Take the right path'), findsOneWidget);

            TestHelpers.verifyNoOverflow(tester);

            // Test choice selection
            await tester.tap(find.text('Take the left path'));
            await tester.pumpAndSettle();

            TestHelpers.verifyNoOverflow(tester);
          }

          await tester.binding.setSurfaceSize(null);
        },
      );
    });

    testWidgets('Auto-advance and choice timing', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Auto-advance and choice timing',
        () async {
          // Launch story
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.text('Test Adventure'));
          await tester.pumpAndSettle();

          // Choices should not be immediately available
          // They should appear after narration completes
          
          // Start narration
          await tester.tap(find.byIcon(Icons.play_arrow));
          await tester.pump();

          // Wait for narration to complete
          await tester.pumpAndSettle();

          // Choices should now be available
          expect(find.text('Take the left path'), findsOneWidget);
          expect(find.text('Take the right path'), findsOneWidget);

          // Test that choices are properly enabled
          await tester.tap(find.text('Take the left path'));
          await tester.pumpAndSettle();

          // Should advance to next scene
          expect(find.text('You find a friendly fairy'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Error handling during story playback', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Error handling during story playback',
        () async {
          // Launch story
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.text('Test Adventure'));
          await tester.pumpAndSettle();

          // Test handling of missing scenes
          // This would require setting up a story with invalid scene references

          // Test handling of TTS errors
          // This would require mocking TTS service failures

          // Test handling of image loading errors
          // This would require setting up invalid image paths

          // For now, verify basic error recovery
          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Cloud sync during story progress', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Cloud sync during story progress',
        () async {
          // Launch story
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.text('Test Adventure'));
          await tester.pumpAndSettle();

          // Make progress in story
          await tester.tap(find.text('Take the left path'));
          await tester.pumpAndSettle();

          // Progress should be automatically saved
          // Verify sync indicator shows synced state
          expect(find.byIcon(Icons.cloud_done), findsOneWidget);

          // Continue story
          await tester.tap(find.text('Accept the fairy\'s help'));
          await tester.pumpAndSettle();

          // Final progress should also be synced
          expect(find.text('Story Complete'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Accessibility during story interaction', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Accessibility during story interaction',
        () async {
          // Launch story
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.text('Test Adventure'));
          await tester.pumpAndSettle();

          // Test accessibility of story interaction screen
          await TestHelpers.verifyAccessibility(tester);

          // Test accessibility of choice buttons
          expect(find.text('Take the left path'), findsOneWidget);
          expect(find.text('Take the right path'), findsOneWidget);

          // Make choice and test next screen
          await tester.tap(find.text('Take the left path'));
          await tester.pumpAndSettle();

          await TestHelpers.verifyAccessibility(tester);
        },
      );
    });
  });
}
