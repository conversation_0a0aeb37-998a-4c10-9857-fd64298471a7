﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{3DD962A7-2953-3417-A09C-C884FB986D0D}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>choice_once_upon_a_time</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\runner\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">choice_once_upon_a_time.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">choice_once_upon_a_time</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\runner\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">choice_once_upon_a_time.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">choice_once_upon_a_time</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\runner\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">choice_once_upon_a_time.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">choice_once_upon_a_time</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\flutter_tts\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\flutter_tts\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\flutter_tts\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Debug\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\cloud_firestore\Debug\cloud_firestore_plugin.lib;..\plugins\firebase_auth\Debug\firebase_auth_plugin.lib;..\plugins\firebase_core\Debug\firebase_core_plugin.lib;..\plugins\firebase_storage\Debug\firebase_storage_plugin.lib;..\plugins\flutter_tts\Debug\flutter_tts_plugin.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_firestore.lib;shell32.lib;Bcrypt.lib;DbgHelp.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_auth.lib;..\plugins\firebase_core\Debug\firebase_core_plugin.lib;advapi32.lib;ws2_32.lib;crypt32.lib;rpcrt4.lib;ole32.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_app.lib;icu.lib;..\flutter\Debug\flutter_wrapper_plugin.lib;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\flutter_windows.dll.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_storage.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64/runner/Debug/choice_once_upon_a_time.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64/runner/Debug/choice_once_upon_a_time.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\flutter_tts\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR="Profile"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR=\"Profile\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\flutter_tts\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\flutter_tts\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Profile\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\cloud_firestore\Profile\cloud_firestore_plugin.lib;..\plugins\firebase_auth\Profile\firebase_auth_plugin.lib;..\plugins\firebase_core\Profile\firebase_core_plugin.lib;..\plugins\firebase_storage\Profile\firebase_storage_plugin.lib;..\plugins\flutter_tts\Profile\flutter_tts_plugin.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_firestore.lib;shell32.lib;Bcrypt.lib;DbgHelp.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_auth.lib;..\plugins\firebase_core\Profile\firebase_core_plugin.lib;advapi32.lib;ws2_32.lib;crypt32.lib;rpcrt4.lib;ole32.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_app.lib;icu.lib;..\flutter\Profile\flutter_wrapper_plugin.lib;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\flutter_windows.dll.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_storage.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64/runner/Profile/choice_once_upon_a_time.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64/runner/Profile/choice_once_upon_a_time.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\flutter_tts\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\flutter_tts\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\flutter_tts\windows\include;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Release\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\cloud_firestore\Release\cloud_firestore_plugin.lib;..\plugins\firebase_auth\Release\firebase_auth_plugin.lib;..\plugins\firebase_core\Release\firebase_core_plugin.lib;..\plugins\firebase_storage\Release\firebase_storage_plugin.lib;..\plugins\flutter_tts\Release\flutter_tts_plugin.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Release\firebase_firestore.lib;shell32.lib;Bcrypt.lib;DbgHelp.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Release\firebase_auth.lib;..\plugins\firebase_core\Release\firebase_core_plugin.lib;advapi32.lib;ws2_32.lib;crypt32.lib;rpcrt4.lib;ole32.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Release\firebase_app.lib;icu.lib;..\flutter\Release\flutter_wrapper_plugin.lib;C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\flutter_windows.dll.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Release\firebase_storage.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64/runner/Release/choice_once_upon_a_time.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64/runner/Release/choice_once_upon_a_time.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\runner\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Install\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SC:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/windows" "-BC:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64" --check-stamp-file "C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64/runner/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"D:\Install\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SC:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/windows" "-BC:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64" --check-stamp-file "C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64/runner/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"D:\Install\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SC:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/windows" "-BC:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64" --check-stamp-file "C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64/runner/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\runner\flutter_window.cpp" />
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\runner\main.cpp" />
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\runner\utils.cpp" />
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\runner\win32_window.cpp" />
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\generated_plugin_registrant.cc" />
    <ResourceCompile Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\runner\Runner.rc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{0263BFF7-A462-35C6-8551-50C6425CEB40}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\plugins\cloud_firestore\cloud_firestore_plugin.vcxproj">
      <Project>{1ED4239D-2DAB-30E6-BECF-5662263E1E25}</Project>
      <Name>cloud_firestore_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\plugins\firebase_auth\firebase_auth_plugin.vcxproj">
      <Project>{E995D783-C446-3636-85B9-D55A4C9F6EE7}</Project>
      <Name>firebase_auth_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\plugins\firebase_core\firebase_core_plugin.vcxproj">
      <Project>{0B0E515B-4455-34CD-9BF0-EF082047E4AE}</Project>
      <Name>firebase_core_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\plugins\firebase_storage\firebase_storage_plugin.vcxproj">
      <Project>{306679D6-C9F5-3858-AEC0-F97AC5CA31EA}</Project>
      <Name>firebase_storage_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{9DAE3D34-D9E7-3F70-85EA-667DA31E1338}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\plugins\flutter_tts\flutter_tts_plugin.vcxproj">
      <Project>{6BEEA9DF-65F5-38F9-8979-F782E7806936}</Project>
      <Name>flutter_tts_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\flutter\flutter_wrapper_app.vcxproj">
      <Project>{43EFD897-06FC-3E52-B498-CFE5B0A002A0}</Project>
      <Name>flutter_wrapper_app</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\flutter\flutter_wrapper_plugin.vcxproj">
      <Project>{64136831-B088-3990-B5B2-59560F0BB042}</Project>
      <Name>flutter_wrapper_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>