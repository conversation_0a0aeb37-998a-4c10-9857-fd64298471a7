import 'package:mockito/mockito.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

import 'package:choice_once_upon_a_time/src/features/auth/services/firebase_auth_service.dart';
import 'package:choice_once_upon_a_time/src/features/monetization/services/iap_service.dart';
import 'package:choice_once_upon_a_time/src/features/monetization/providers/purchase_provider.dart';

// Simple mock classes without code generation
class MockUser extends Mock implements User {}
class MockUserCredential extends Mock implements UserCredential {}
class MockProductDetails extends Mock implements ProductDetails {}
class MockPurchaseDetails extends Mock implements PurchaseDetails {}

// Mock providers
class MockPurchaseProvider extends Mock implements PurchaseProvider {}

/// Mock Firebase Auth Service for testing
class MockFirebaseAuthService extends Mock implements FirebaseAuthService {
  bool _isSignedIn = false;
  String? _userEmail;
  String? _userId;
  String? _errorMessage;

  /// Set mock authentication state
  void setAuthState({
    required bool isSignedIn,
    String? userEmail,
    String? userId,
    String? errorMessage,
  }) {
    _isSignedIn = isSignedIn;
    _userEmail = userEmail;
    _userId = userId;
    _errorMessage = errorMessage;
  }

  @override
  Future<UserCredential?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    if (_errorMessage != null) {
      throw FirebaseAuthException(
        code: 'mock-error',
        message: _errorMessage,
      );
    }

    if (email == '<EMAIL>' && password == 'password123') {
      _isSignedIn = true;
      _userEmail = email;
      _userId = 'mock-user-id';

      final mockUser = MockUser();
      when(mockUser.email).thenReturn(email);
      when(mockUser.uid).thenReturn(_userId!);

      final mockCredential = MockUserCredential();
      when(mockCredential.user).thenReturn(mockUser);

      return mockCredential;
    }

    throw FirebaseAuthException(
      code: 'invalid-credentials',
      message: 'Invalid email or password',
    );
  }

  @override
  Future<UserCredential?> createUserWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    if (_errorMessage != null) {
      throw FirebaseAuthException(
        code: 'mock-error',
        message: _errorMessage,
      );
    }

    _isSignedIn = true;
    _userEmail = email;
    _userId = 'mock-new-user-id';

    final mockUser = MockUser();
    when(mockUser.email).thenReturn(email);
    when(mockUser.uid).thenReturn(_userId!);

    final mockCredential = MockUserCredential();
    when(mockCredential.user).thenReturn(mockUser);

    return mockCredential;
  }

  @override
  Future<void> signOut() async {
    _isSignedIn = false;
    _userEmail = null;
    _userId = null;
  }

  @override
  User? get currentUser {
    if (!_isSignedIn) return null;

    final mockUser = MockUser();
    when(mockUser.email).thenReturn(_userEmail);
    when(mockUser.uid).thenReturn(_userId!);
    return mockUser;
  }

  @override
  Stream<User?> get authStateChanges {
    return Stream.value(currentUser);
  }
}

/// Mock IAP Service for testing
class MockIAPService extends Mock implements IAPService {
  bool _isInitialized = false;
  bool _isPremiumUnlocked = false;
  List<ProductDetails> _products = [];
  String? _errorMessage;

  /// Set mock IAP state
  void setIAPState({
    required bool isInitialized,
    required bool isPremiumUnlocked,
    List<ProductDetails>? products,
    String? errorMessage,
  }) {
    _isInitialized = isInitialized;
    _isPremiumUnlocked = isPremiumUnlocked;
    _products = products ?? [];
    _errorMessage = errorMessage;
  }

  @override
  Future<bool> initialize() async {
    if (_errorMessage != null) {
      throw Exception(_errorMessage);
    }
    _isInitialized = true;
    return true;
  }

  @override
  Future<bool> isPremiumUnlocked() async {
    return _isPremiumUnlocked;
  }

  @override
  Future<bool> isContentUnlocked(String contentId) async {
    // Premium unlocks all content
    if (_isPremiumUnlocked) return true;

    // Free content is always unlocked
    return contentId.startsWith('free_');
  }

  @override
  List<ProductDetails> get premiumProducts => _products;

  @override
  Future<bool> purchaseProduct(String productId) async {
    if (_errorMessage != null) {
      throw Exception(_errorMessage);
    }

    // Simulate successful purchase
    if (productId.contains('premium')) {
      _isPremiumUnlocked = true;
      return true;
    }

    return false;
  }

  @override
  Future<bool> restorePurchases() async {
    if (_errorMessage != null) {
      throw Exception(_errorMessage);
    }

    // Simulate restored purchases
    return true;
  }

  @override
  void dispose() {
    _isInitialized = false;
  }
}

/// Mock Firestore for testing
class MockFirestore {
  static final Map<String, Map<String, dynamic>> _documents = {};
  static final Map<String, List<Map<String, dynamic>>> _collections = {};

  /// Set document data
  static void setDocument(String path, Map<String, dynamic> data) {
    _documents[path] = data;
  }

  /// Set collection data
  static void setCollection(String path, List<Map<String, dynamic>> data) {
    _collections[path] = data;
  }

  /// Clear all data
  static void clear() {
    _documents.clear();
    _collections.clear();
  }



}

/// Test data factory for creating consistent test data
class TestDataFactory {
  /// Create test user data
  static Map<String, dynamic> createUserData({
    String email = '<EMAIL>',
    String displayName = 'Test User',
    List<Map<String, dynamic>>? children,
  }) {
    return {
      'email': email,
      'displayName': displayName,
      'createdAt': DateTime.now().toIso8601String(),
      'children': children ?? [],
    };
  }

  /// Create test child profile data
  static Map<String, dynamic> createChildData({
    String id = 'test-child-1',
    String name = 'Test Child',
    int age = 8,
    String avatarId = 'avatar1',
    Map<String, dynamic>? storyProgress,
    Map<String, dynamic>? preferences,
  }) {
    return {
      'id': id,
      'name': name,
      'age': age,
      'avatarId': avatarId,
      'storyProgress': storyProgress ?? {},
      'preferences': preferences ?? {},
      'lastActive': DateTime.now().toIso8601String(),
    };
  }

  /// Create test story progress data
  static Map<String, dynamic> createStoryProgressData({
    String storyId = 'test-story-1',
    String currentSceneId = 'test-scene-1',
    List<String>? completedScenes,
    Map<String, String>? choicesMade,
    bool isCompleted = false,
    int totalTimeMinutes = 0,
  }) {
    return {
      'storyId': storyId,
      'currentSceneId': currentSceneId,
      'completedScenes': completedScenes ?? [],
      'choicesMade': choicesMade ?? {},
      'lastUpdated': DateTime.now().toIso8601String(),
      'isCompleted': isCompleted,
      'totalTimeMinutes': totalTimeMinutes,
    };
  }

  /// Create test product details
  static ProductDetails createProductDetails({
    String id = 'premium_monthly',
    String title = 'Premium Monthly',
    String description = 'Monthly premium subscription',
    String price = '\$4.99',
    String currencyCode = 'USD',
  }) {
    final mockProduct = MockProductDetails();
    when(mockProduct.id).thenReturn(id);
    when(mockProduct.title).thenReturn(title);
    when(mockProduct.description).thenReturn(description);
    when(mockProduct.price).thenReturn(price);
    when(mockProduct.currencyCode).thenReturn(currencyCode);
    return mockProduct;
  }
}
