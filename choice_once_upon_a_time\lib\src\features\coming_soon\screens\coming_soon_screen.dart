import 'package:flutter/material.dart';
import '../../../shared_kernel/theme/app_theme.dart';

/// Screen that displays a "Feature Coming Soon" message
class ComingSoonScreen extends StatelessWidget {
  /// The name of the feature that is coming soon
  final String featureName;

  /// Constructor
  const ComingSoonScreen({
    super.key,
    required this.featureName,
  });

  /// Route name for navigation
  static const routeName = '/coming-soon';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(featureName),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.construction,
              size: 80,
              color: AppTheme.secondaryColor,
            ),
            const SizedBox(height: 24),
            Text(
              'Coming Soon!',
              style: AppTheme.headingStyle,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                'We\'re working hard to bring you the $featureName feature. Check back soon!',
                style: AppTheme.bodyStyle,
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }
}
