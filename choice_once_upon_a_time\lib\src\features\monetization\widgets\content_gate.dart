import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/purchase_provider.dart';
import '../screens/premium_screen.dart';
import '../../../shared_kernel/theme/app_theme.dart';

/// Widget that gates content behind premium purchases
class ContentGate extends StatelessWidget {
  /// The content to show when unlocked
  final Widget child;

  /// The content ID to check for unlock status
  final String? contentId;

  /// Whether to check for premium status instead of specific content
  final bool requiresPremium;

  /// Custom unlock message
  final String? unlockMessage;

  /// Custom unlock button text
  final String? unlockButtonText;

  /// Constructor
  const ContentGate({
    super.key,
    required this.child,
    this.contentId,
    this.requiresPremium = false,
    this.unlockMessage,
    this.unlockButtonText,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<PurchaseProvider>(
      builder: (context, purchaseProvider, _) {
        // Check if content is unlocked
        bool isUnlocked;
        
        if (requiresPremium) {
          isUnlocked = purchaseProvider.isPremiumUnlocked;
        } else if (contentId != null) {
          isUnlocked = purchaseProvider.isContentUnlocked(contentId!);
        } else {
          // If no specific requirements, show content
          isUnlocked = true;
        }

        if (isUnlocked) {
          return child;
        }

        // Show lock screen
        return _buildLockScreen(context, purchaseProvider);
      },
    );
  }

  Widget _buildLockScreen(BuildContext context, PurchaseProvider purchaseProvider) {
    final screenSize = MediaQuery.of(context).size;
    final isLandscape = screenSize.width > screenSize.height;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.grey[100]!,
            Colors.grey[200]!,
          ],
        ),
      ),
      child: Center(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(isLandscape ? 32 : 24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Lock icon with animation
              TweenAnimationBuilder<double>(
                tween: Tween(begin: 0.8, end: 1.0),
                duration: const Duration(milliseconds: 1000),
                curve: Curves.elasticOut,
                builder: (context, scale, child) {
                  return Transform.scale(
                    scale: scale,
                    child: Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.amber[100],
                        border: Border.all(
                          color: Colors.amber,
                          width: 3,
                        ),
                      ),
                      child: Icon(
                        Icons.lock,
                        size: isLandscape ? 80 : 60,
                        color: Colors.amber[700],
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 32),

              // Premium badge
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.amber,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.star, color: Colors.white, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'PREMIUM CONTENT',
                      style: AppTheme.bodyStyle.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Unlock message
              Text(
                unlockMessage ?? 'Unlock this content with Premium',
                style: AppTheme.headingStyle.copyWith(
                  fontSize: isLandscape ? 28 : 24,
                  color: Colors.grey[800],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Benefits list
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Text(
                      'Premium Benefits',
                      style: AppTheme.subheadingStyle.copyWith(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ..._buildBenefitsList(),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // Unlock button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: purchaseProvider.isLoading
                      ? null
                      : () => Navigator.of(context).pushNamed(PremiumScreen.routeName),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.amber,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(
                      vertical: isLandscape ? 20 : 16,
                      horizontal: 32,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 4,
                  ),
                  child: purchaseProvider.isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : Text(
                          unlockButtonText ?? 'Unlock Premium',
                          style: TextStyle(
                            fontSize: isLandscape ? 20 : 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
              const SizedBox(height: 16),

              // Restore purchases button
              TextButton(
                onPressed: purchaseProvider.isLoading
                    ? null
                    : () => purchaseProvider.restorePurchases(),
                child: Text(
                  'Restore Purchases',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ),

              // Error message
              if (purchaseProvider.errorMessage != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red[200]!),
                  ),
                  child: Text(
                    purchaseProvider.errorMessage!,
                    style: TextStyle(color: Colors.red[700]),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildBenefitsList() {
    final benefits = [
      'Access to all premium stories',
      'New content added monthly',
      'Ad-free experience',
      'Offline downloads',
      'Advanced parental controls',
    ];

    return benefits.map((benefit) => Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green[600],
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              benefit,
              style: AppTheme.bodyStyle.copyWith(fontSize: 14),
            ),
          ),
        ],
      ),
    )).toList();
  }
}

/// Convenience widget for premium-gated content
class PremiumGate extends StatelessWidget {
  final Widget child;
  final String? unlockMessage;

  const PremiumGate({
    super.key,
    required this.child,
    this.unlockMessage,
  });

  @override
  Widget build(BuildContext context) {
    return ContentGate(
      requiresPremium: true,
      unlockMessage: unlockMessage,
      child: child,
    );
  }
}

/// Convenience widget for story-specific content gates
class StoryGate extends StatelessWidget {
  final Widget child;
  final String storyId;

  const StoryGate({
    super.key,
    required this.child,
    required this.storyId,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<PurchaseProvider>(
      builder: (context, purchaseProvider, _) {
        final isUnlocked = purchaseProvider.isStoryUnlocked(storyId);
        
        if (isUnlocked) {
          return child;
        }

        return ContentGate(
          requiresPremium: true,
          unlockMessage: 'Unlock this story with Premium',
          unlockButtonText: 'Get Premium Access',
          child: child,
        );
      },
    );
  }
}
