import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../shared_kernel/theme/app_theme.dart';
import '../models/story_model.dart';
import '../services/tts_service.dart';

/// Widget that displays a popup with choices
class ChoicePopup extends StatefulWidget {
  /// The prompt for the choice
  final String prompt;

  /// The list of choices
  final List<StoryChoice> choices;

  /// The TTS service
  final TTSService ttsService;

  /// Callback when a choice is selected
  final Function(StoryChoice choice) onChoiceSelected;

  /// Constructor
  const ChoicePopup({
    super.key,
    required this.prompt,
    required this.choices,
    required this.ttsService,
    required this.onChoiceSelected,
  });

  @override
  State<ChoicePopup> createState() => _ChoicePopupState();
}

class _ChoicePopupState extends State<ChoicePopup> {
  /// The currently highlighted choice index
  int _highlightedChoiceIndex = -1;

  /// Whether the prompt has been narrated
  bool _promptNarrated = false;



  @override
  void initState() {
    super.initState();
    _narratePrompt();
  }

  @override
  void dispose() {
    widget.ttsService.stop();
    super.dispose();
  }

  /// Narrate the prompt
  Future<void> _narratePrompt() async {
    // Narrate the prompt
    await widget.ttsService.speak(widget.prompt);

    setState(() {
      _promptNarrated = true;
    });

    // After prompt, narrate each choice
    await _narrateChoices();
  }

  /// Narrate each choice
  Future<void> _narrateChoices() async {
    if (widget.choices.isEmpty) {
      return;
    }

    // Narrate each choice with a pause between
    for (int i = 0; i < widget.choices.length; i++) {
      // Highlight the current choice
      setState(() {
        _highlightedChoiceIndex = i;
      });

      // Narrate the choice
      await widget.ttsService.speak(widget.choices[i].text);

      // Pause between choices
      await Future.delayed(const Duration(milliseconds: 500));
    }

    // Reset highlight
    setState(() {
      _highlightedChoiceIndex = -1;
    });
  }

  /// Replay the narration
  Future<void> _replayNarration() async {
    setState(() {
      _promptNarrated = false;
      _highlightedChoiceIndex = -1;
    });

    await _narratePrompt();
  }

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions for responsive sizing
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;
    final isLandscape = screenWidth > screenHeight;

    // Calculate responsive sizes
    final double margin = isLandscape ? screenWidth * 0.05 : screenWidth * 0.08;
    final double padding = isLandscape ? screenWidth * 0.03 : screenWidth * 0.06;
    final double borderRadius = isLandscape ? screenWidth * 0.02 : screenWidth * 0.04;
    final double promptFontSize = isLandscape ? screenWidth * 0.025 : screenWidth * 0.05;
    final double choiceFontSize = isLandscape ? screenWidth * 0.02 : screenWidth * 0.04;

    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isLandscape ? screenWidth * 0.7 : screenWidth * 0.9,
          maxHeight: screenHeight * 0.8,
        ),
        margin: EdgeInsets.all(margin),
        padding: EdgeInsets.all(padding),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 10,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Prompt
            Text(
              widget.prompt,
              style: AppTheme.subheadingStyle.copyWith(
                fontSize: promptFontSize,
                color: AppTheme.primaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: padding * 0.5),

            // Choices
            ...List.generate(widget.choices.length, (index) {
              final choice = widget.choices[index];
              final isHighlighted = index == _highlightedChoiceIndex;

              return Padding(
                padding: EdgeInsets.only(bottom: padding * 0.3),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  transform: isHighlighted
                      ? (Matrix4.identity()..scale(1.05))
                      : Matrix4.identity(),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => widget.onChoiceSelected(choice),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isHighlighted
                            ? AppTheme.secondaryColor
                            : AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          horizontal: padding * 0.5,
                          vertical: padding * 0.3,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(borderRadius * 0.5),
                          side: isHighlighted
                              ? const BorderSide(
                                  color: Colors.yellow,
                                  width: 3,
                                )
                              : BorderSide.none,
                        ),
                        elevation: isHighlighted ? 8 : 4,
                        minimumSize: Size(double.infinity, isLandscape ? screenHeight * 0.08 : screenHeight * 0.06),
                      ),
                      child: Text(
                        choice.text,
                        style: TextStyle(
                          fontSize: choiceFontSize,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )
                  .animate(target: isHighlighted ? 1 : 0)
                  .shimmer(
                    duration: const Duration(milliseconds: 1000),
                    color: const Color.fromRGBO(255, 255, 255, 0.3),
                  ),
                ),
              );
            }),

            SizedBox(height: padding * 0.3),

            // Replay button
            if (_promptNarrated)
              TextButton.icon(
                onPressed: _replayNarration,
                icon: Icon(Icons.replay, size: choiceFontSize),
                label: Text(
                  'Listen Again',
                  style: TextStyle(fontSize: choiceFontSize * 0.8),
                ),
              ),
          ],
        ),
      )
      .animate()
      .scale(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
        begin: const Offset(0.9, 0.9),
        end: const Offset(1.0, 1.0),
      )
      .fadeIn(
        duration: const Duration(milliseconds: 300),
      ),
    );
  }
}
