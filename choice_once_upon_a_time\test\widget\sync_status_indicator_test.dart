import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../../lib/src/features/profile/widgets/sync_status_indicator.dart';
import '../helpers/test_helpers.dart';
import '../mocks/mock_services.dart';

void main() {
  group('SyncStatusIndicator Widget Tests', () {
    late MockParentAuthProvider mockAuthProvider;
    late MockActiveChildProvider mockActiveChildProvider;

    setUp(() {
      mockAuthProvider = MockParentAuthProvider();
      mockActiveChildProvider = MockActiveChildProvider();
      
      TestHelpers.setupMockProviders(
        authProvider: mockAuthProvider,
        activeChildProvider: mockActiveChildProvider,
      );
    });

    group('Visibility', () {
      testWidgets('should not show when user is not authenticated', (tester) async {
        // Arrange
        when(mockAuthProvider.isAuthenticated).thenReturn(false);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.byType(SyncStatusIndicator), findsOneWidget);
        // Should render as SizedBox.shrink() - no visible content
        expect(find.byIcon(Icons.cloud_done), findsNothing);
        expect(find.byIcon(Icons.cloud_off), findsNothing);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should not show when no active child', (tester) async {
        // Arrange
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockActiveChildProvider.activeProfile).thenReturn(null);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.byIcon(Icons.cloud_done), findsNothing);
        expect(find.byIcon(Icons.cloud_off), findsNothing);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show when authenticated with active child', (tester) async {
        // Arrange
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockActiveChildProvider.activeProfile)
            .thenReturn(TestHelpers.createMockChildProfile());
        when(mockActiveChildProvider.isCloudSyncAvailable).thenReturn(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.byIcon(Icons.cloud_done), findsOneWidget);
        expect(find.text('Synced'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });
    });

    group('Sync States', () {
      setUp(() {
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockActiveChildProvider.activeProfile)
            .thenReturn(TestHelpers.createMockChildProfile());
      });

      testWidgets('should show synced state when cloud sync available', (tester) async {
        // Arrange
        when(mockActiveChildProvider.isCloudSyncAvailable).thenReturn(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(compact: true),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.byIcon(Icons.cloud_done), findsOneWidget);
        expect(find.text('Synced'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show offline state when cloud sync not available', (tester) async {
        // Arrange
        when(mockActiveChildProvider.isCloudSyncAvailable).thenReturn(false);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(compact: true),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.byIcon(Icons.cloud_off), findsOneWidget);
        expect(find.text('Offline'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show syncing state during sync', (tester) async {
        // Arrange
        when(mockActiveChildProvider.isCloudSyncAvailable).thenReturn(true);
        when(mockActiveChildProvider.syncWithCloud()).thenAnswer((_) async {
          // Simulate a delay
          await Future.delayed(const Duration(milliseconds: 100));
          return true;
        });

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(compact: true),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Act - trigger sync
        await tester.tap(find.byType(SyncStatusIndicator));
        await tester.pump(); // Start sync

        // Assert - should show syncing state
        expect(find.byIcon(Icons.sync), findsOneWidget);
        expect(find.text('Syncing...'), findsOneWidget);

        // Wait for sync to complete
        await tester.pumpAndSettle();

        // Should return to synced state
        expect(find.byIcon(Icons.cloud_done), findsOneWidget);
        expect(find.text('Synced'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });
    });

    group('Compact vs Full Display', () {
      setUp(() {
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockActiveChildProvider.activeProfile)
            .thenReturn(TestHelpers.createMockChildProfile());
        when(mockActiveChildProvider.isCloudSyncAvailable).thenReturn(true);
      });

      testWidgets('should show compact indicator', (tester) async {
        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(compact: true),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.byIcon(Icons.cloud_done), findsOneWidget);
        expect(find.text('Synced'), findsOneWidget);
        
        // Should not show full details
        expect(find.text('Cloud Sync'), findsNothing);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show full indicator', (tester) async {
        // Arrange
        when(mockActiveChildProvider.getLastSyncTimestamp())
            .thenAnswer((_) async => DateTime.now().subtract(const Duration(minutes: 5)));

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(compact: false),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.byIcon(Icons.cloud_done), findsOneWidget);
        expect(find.text('Cloud Sync'), findsOneWidget);
        expect(find.text('Synced'), findsOneWidget);
        expect(find.text('Sync Now'), findsOneWidget);
        expect(find.textContaining('Last synced:'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should show never synced message', (tester) async {
        // Arrange
        when(mockActiveChildProvider.getLastSyncTimestamp())
            .thenAnswer((_) async => null);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(compact: false),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.text('Never synced'), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });
    });

    group('Manual Sync', () {
      setUp(() {
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockActiveChildProvider.activeProfile)
            .thenReturn(TestHelpers.createMockChildProfile());
        when(mockActiveChildProvider.isCloudSyncAvailable).thenReturn(true);
      });

      testWidgets('should trigger sync when tapped (compact)', (tester) async {
        // Arrange
        when(mockActiveChildProvider.syncWithCloud()).thenAnswer((_) async => true);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(compact: true),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Act
        await tester.tap(find.byType(SyncStatusIndicator));
        await tester.pumpAndSettle();

        // Assert
        verify(mockActiveChildProvider.syncWithCloud()).called(1);
      });

      testWidgets('should trigger sync when Sync Now button tapped (full)', (tester) async {
        // Arrange
        when(mockActiveChildProvider.syncWithCloud()).thenAnswer((_) async => true);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(compact: false),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Act
        await tester.tap(find.text('Sync Now'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockActiveChildProvider.syncWithCloud()).called(1);
      });

      testWidgets('should not allow sync when offline', (tester) async {
        // Arrange
        when(mockActiveChildProvider.isCloudSyncAvailable).thenReturn(false);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(compact: true),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Act
        await tester.tap(find.byType(SyncStatusIndicator));
        await tester.pump();

        // Assert
        verifyNever(mockActiveChildProvider.syncWithCloud());
      });

      testWidgets('should show success message after sync', (tester) async {
        // Arrange
        when(mockActiveChildProvider.syncWithCloud()).thenAnswer((_) async => true);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(compact: true),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Act
        await tester.tap(find.byType(SyncStatusIndicator));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.text('Sync completed successfully'), findsOneWidget);
        expect(find.byIcon(Icons.check_circle), findsOneWidget);
      });

      testWidgets('should show error message on sync failure', (tester) async {
        // Arrange
        when(mockActiveChildProvider.syncWithCloud())
            .thenThrow(Exception('Network error'));

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(compact: true),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Act
        await tester.tap(find.byType(SyncStatusIndicator));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.textContaining('Sync failed:'), findsOneWidget);
        expect(find.byIcon(Icons.error), findsOneWidget);
      });
    });

    group('Animation', () {
      setUp(() {
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockActiveChildProvider.activeProfile)
            .thenReturn(TestHelpers.createMockChildProfile());
        when(mockActiveChildProvider.isCloudSyncAvailable).thenReturn(true);
      });

      testWidgets('should animate sync icon during sync', (tester) async {
        // Arrange
        when(mockActiveChildProvider.syncWithCloud()).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 500));
          return true;
        });

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(compact: true),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Act
        await tester.tap(find.byType(SyncStatusIndicator));
        await tester.pump(); // Start sync

        // Assert - should show rotating sync icon
        expect(find.byIcon(Icons.sync), findsOneWidget);

        // Pump a few frames to see animation
        await tester.pump(const Duration(milliseconds: 100));
        await tester.pump(const Duration(milliseconds: 100));
        await tester.pump(const Duration(milliseconds: 100));

        // Complete sync
        await tester.pumpAndSettle();

        // Should stop animating and show synced state
        expect(find.byIcon(Icons.cloud_done), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });
    });

    group('Responsive Design', () {
      setUp(() {
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockActiveChildProvider.activeProfile)
            .thenReturn(TestHelpers.createMockChildProfile());
        when(mockActiveChildProvider.isCloudSyncAvailable).thenReturn(true);
      });

      testWidgets('should handle different screen sizes', (tester) async {
        await TestHelpers.testOnAllScreenSizes(
          tester,
          (screenSize) => TestHelpers.createResponsiveTestWidget(
            child: const SyncStatusIndicator(compact: true),
            screenSize: screenSize,
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
          (tester, screenSize) async {
            await tester.pump();
            
            // Should show sync indicator without overflow
            expect(find.byIcon(Icons.cloud_done), findsOneWidget);
            expect(find.text('Synced'), findsOneWidget);
          },
        );
      });
    });

    group('FloatingSyncButton', () {
      setUp(() {
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockActiveChildProvider.isCloudSyncAvailable).thenReturn(true);
      });

      testWidgets('should show floating sync button when authenticated', (tester) async {
        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const Scaffold(
              floatingActionButton: FloatingSyncButton(),
            ),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.byType(FloatingActionButton), findsOneWidget);
        expect(find.byIcon(Icons.cloud_sync), findsOneWidget);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should not show when not authenticated', (tester) async {
        // Arrange
        when(mockAuthProvider.isAuthenticated).thenReturn(false);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const Scaffold(
              floatingActionButton: FloatingSyncButton(),
            ),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        expect(find.byType(FloatingActionButton), findsNothing);
        TestHelpers.verifyNoOverflow(tester);
      });

      testWidgets('should trigger sync when tapped', (tester) async {
        // Arrange
        when(mockActiveChildProvider.syncWithCloud()).thenAnswer((_) async => true);

        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const Scaffold(
              floatingActionButton: FloatingSyncButton(),
            ),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Act
        await tester.tap(find.byType(FloatingActionButton));
        await tester.pumpAndSettle();

        // Assert
        verify(mockActiveChildProvider.syncWithCloud()).called(1);
        expect(find.text('Sync completed'), findsOneWidget);
      });
    });

    group('Accessibility', () {
      setUp(() {
        when(mockAuthProvider.isAuthenticated).thenReturn(true);
        when(mockActiveChildProvider.activeProfile)
            .thenReturn(TestHelpers.createMockChildProfile());
        when(mockActiveChildProvider.isCloudSyncAvailable).thenReturn(true);
      });

      testWidgets('should meet accessibility guidelines', (tester) async {
        // Act
        await tester.pumpWidget(
          TestHelpers.createTestWidget(
            child: const SyncStatusIndicator(),
            authProvider: mockAuthProvider,
            activeChildProvider: mockActiveChildProvider,
          ),
        );

        // Assert
        await TestHelpers.verifyAccessibility(tester);
      });
    });
  });
}
