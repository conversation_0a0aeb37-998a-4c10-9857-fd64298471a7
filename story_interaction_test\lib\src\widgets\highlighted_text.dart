import 'package:flutter/material.dart';

import '../services/text_segmenter.dart';

/// Widget that displays text with word-by-word highlighting
class HighlightedText extends StatelessWidget {
  final String text;
  final int currentWordIndex;
  final TextStyle style;
  final Color highlightColor;

  const HighlightedText({
    super.key,
    required this.text,
    required this.currentWordIndex,
    required this.style,
    required this.highlightColor,
  });

  @override
  Widget build(BuildContext context) {
    if (text.isEmpty) {
      return const SizedBox.shrink();
    }

    final words = TextSegmenter.splitIntoWords(text);
    
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        children: _buildTextSpans(words),
      ),
    );
  }

  List<TextSpan> _buildTextSpans(List<String> words) {
    final spans = <TextSpan>[];
    
    for (int i = 0; i < words.length; i++) {
      final word = words[i];
      final isCurrentWord = i == currentWordIndex;
      
      spans.add(
        TextSpan(
          text: word,
          style: style.copyWith(
            backgroundColor: isCurrentWord ? highlightColor : null,
            fontWeight: isCurrentWord ? FontWeight.bold : style.fontWeight,
          ),
        ),
      );
      
      // Add space after word (except for the last word)
      if (i < words.length - 1) {
        spans.add(
          TextSpan(
            text: ' ',
            style: style,
          ),
        );
      }
    }
    
    return spans;
  }
}
