import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:story_interaction_test/src/widgets/sentence_progress_indicator.dart';

void main() {
  group('SentenceProgressIndicator Widget', () {
    testWidgets('should display dots for small number of sentences', (WidgetTester tester) async {
      final sentences = ['First sentence.', 'Second sentence.', 'Third sentence.'];
      const currentIndex = 1;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SentenceProgressIndicator(
              sentences: sentences,
              currentSentenceIndex: currentIndex,
              dotSize: 10.0,
              spacing: 5.0,
              activeDotColor: Colors.blue,
              inactiveDotColor: Colors.grey,
            ),
          ),
        ),
      );

      // Should display a Row with dots
      expect(find.byType(Row), findsOneWidget);
      
      // Should have 3 containers (one for each sentence)
      expect(find.byType(Container), findsNWidgets(3));

      // Check that dots are rendered
      final containers = tester.widgetList<Container>(find.byType(Container));
      expect(containers.length, equals(3));

      // Verify the active dot (index 1) is larger and has active color
      final activeDot = containers.elementAt(1);
      final activeDecoration = activeDot.decoration as BoxDecoration;
      expect(activeDecoration.color, equals(Colors.blue));
    });

    testWidgets('should display linear progress for many sentences', (WidgetTester tester) async {
      // Create a list with more than 20 sentences
      final sentences = List.generate(25, (index) => 'Sentence ${index + 1}.');
      const currentIndex = 10;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SentenceProgressIndicator(
              sentences: sentences,
              currentSentenceIndex: currentIndex,
              activeDotColor: Colors.blue,
              inactiveDotColor: Colors.grey,
            ),
          ),
        ),
      );

      // Should display a LinearProgressIndicator instead of dots
      expect(find.byType(LinearProgressIndicator), findsOneWidget);
      expect(find.byType(Row), findsNothing);

      // Check progress value
      final progressIndicator = tester.widget<LinearProgressIndicator>(
        find.byType(LinearProgressIndicator),
      );
      final expectedProgress = (currentIndex + 1) / sentences.length;
      expect(progressIndicator.value, closeTo(expectedProgress, 0.01));
    });

    testWidgets('should handle empty sentences list', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SentenceProgressIndicator(
              sentences: const [],
              currentSentenceIndex: 0,
              activeDotColor: Colors.blue,
              inactiveDotColor: Colors.grey,
            ),
          ),
        ),
      );

      // Should render a SizedBox.shrink() for empty list
      expect(find.byType(SizedBox), findsOneWidget);
      expect(find.byType(Row), findsNothing);
      expect(find.byType(LinearProgressIndicator), findsNothing);
    });

    testWidgets('should highlight completed dots', (WidgetTester tester) async {
      final sentences = ['First.', 'Second.', 'Third.', 'Fourth.'];
      const currentIndex = 2; // Third sentence (0-indexed)

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SentenceProgressIndicator(
              sentences: sentences,
              currentSentenceIndex: currentIndex,
              dotSize: 8.0,
              activeDotColor: Colors.blue,
              inactiveDotColor: Colors.grey,
            ),
          ),
        ),
      );

      final containers = tester.widgetList<Container>(find.byType(Container));
      
      // First dot (completed) should be active color
      final firstDot = containers.elementAt(0);
      final firstDecoration = firstDot.decoration as BoxDecoration;
      expect(firstDecoration.color, equals(Colors.blue));

      // Second dot (completed) should be active color
      final secondDot = containers.elementAt(1);
      final secondDecoration = secondDot.decoration as BoxDecoration;
      expect(secondDecoration.color, equals(Colors.blue));

      // Third dot (current) should be active color and larger
      final thirdDot = containers.elementAt(2);
      final thirdDecoration = thirdDot.decoration as BoxDecoration;
      expect(thirdDecoration.color, equals(Colors.blue));

      // Fourth dot (not reached) should be inactive color
      final fourthDot = containers.elementAt(3);
      final fourthDecoration = fourthDot.decoration as BoxDecoration;
      expect(fourthDecoration.color, equals(Colors.grey));
    });

    testWidgets('should animate dot size changes', (WidgetTester tester) async {
      final sentences = ['First.', 'Second.', 'Third.'];
      const dotSize = 8.0;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SentenceProgressIndicator(
              sentences: sentences,
              currentSentenceIndex: 0,
              dotSize: dotSize,
              activeDotColor: Colors.blue,
              inactiveDotColor: Colors.grey,
            ),
          ),
        ),
      );

      // Find the active dot (first one)
      final containers = tester.widgetList<Container>(find.byType(Container));
      final activeDot = containers.first;

      // Active dot should be larger than normal size
      expect(activeDot.constraints?.maxWidth, equals(dotSize * 1.5));
      expect(activeDot.constraints?.maxHeight, equals(dotSize * 1.5));

      // Inactive dots should be normal size
      final inactiveDot = containers.elementAt(1);
      expect(inactiveDot.constraints?.maxWidth, equals(dotSize));
      expect(inactiveDot.constraints?.maxHeight, equals(dotSize));
    });

    testWidgets('should use custom colors', (WidgetTester tester) async {
      final sentences = ['First.', 'Second.'];
      const customActiveColor = Colors.red;
      const customInactiveColor = Colors.yellow;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SentenceProgressIndicator(
              sentences: sentences,
              currentSentenceIndex: 0,
              activeDotColor: customActiveColor,
              inactiveDotColor: customInactiveColor,
            ),
          ),
        ),
      );

      final containers = tester.widgetList<Container>(find.byType(Container));
      
      // Active dot should use custom active color
      final activeDot = containers.first;
      final activeDecoration = activeDot.decoration as BoxDecoration;
      expect(activeDecoration.color, equals(customActiveColor));

      // Inactive dot should use custom inactive color
      final inactiveDot = containers.last;
      final inactiveDecoration = inactiveDot.decoration as BoxDecoration;
      expect(inactiveDecoration.color, equals(customInactiveColor));
    });
  });
}
