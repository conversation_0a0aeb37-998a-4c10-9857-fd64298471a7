// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in choice_once_upon_a_time/test/helpers/test_helpers.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;
import 'dart:ui' as _i6;

import 'package:choice_once_upon_a_time/src/features/auth/models/parent_profile.dart'
    as _i5;
import 'package:choice_once_upon_a_time/src/features/auth/providers/parent_auth_provider.dart'
    as _i2;
import 'package:choice_once_upon_a_time/src/features/monetization/providers/purchase_provider.dart'
    as _i7;
import 'package:choice_once_upon_a_time/src/features/profile/models/child_profile.dart'
    as _i3;
import 'package:choice_once_upon_a_time/src/features/profile/providers/active_child_provider.dart'
    as _i9;
import 'package:choice_once_upon_a_time/src/features/story/models/story_model.dart'
    as _i11;
import 'package:choice_once_upon_a_time/src/features/story/providers/story_provider.dart'
    as _i10;
import 'package:choice_once_upon_a_time/src/features/story/providers/story_settings_provider.dart'
    as _i13;
import 'package:in_app_purchase/in_app_purchase.dart' as _i8;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i12;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [ParentAuthProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockParentAuthProvider extends _i1.Mock
    implements _i2.ParentAuthProvider {
  MockParentAuthProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  List<_i3.ChildProfile> get childProfiles =>
      (super.noSuchMethod(
            Invocation.getter(#childProfiles),
            returnValue: <_i3.ChildProfile>[],
          )
          as List<_i3.ChildProfile>);

  @override
  bool get isLoading =>
      (super.noSuchMethod(Invocation.getter(#isLoading), returnValue: false)
          as bool);

  @override
  bool get isAuthenticated =>
      (super.noSuchMethod(
            Invocation.getter(#isAuthenticated),
            returnValue: false,
          )
          as bool);

  @override
  bool get isPremium =>
      (super.noSuchMethod(Invocation.getter(#isPremium), returnValue: false)
          as bool);

  @override
  bool get hasChildren =>
      (super.noSuchMethod(Invocation.getter(#hasChildren), returnValue: false)
          as bool);

  @override
  int get childrenCount =>
      (super.noSuchMethod(Invocation.getter(#childrenCount), returnValue: 0)
          as int);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  _i4.Future<bool> signUp({
    required String? email,
    required String? password,
    required String? displayName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signUp, [], {
              #email: email,
              #password: password,
              #displayName: displayName,
            }),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> signIn({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signIn, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> sendPasswordResetEmail(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#sendPasswordResetEmail, [email]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<void> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> deleteAccount() =>
      (super.noSuchMethod(
            Invocation.method(#deleteAccount, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> updateParentProfile(_i5.ParentProfile? profile) =>
      (super.noSuchMethod(
            Invocation.method(#updateParentProfile, [profile]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<_i3.ChildProfile?> createChildProfile({
    required String? name,
    String? avatarId,
    int? age,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createChildProfile, [], {
              #name: name,
              #avatarId: avatarId,
              #age: age,
            }),
            returnValue: _i4.Future<_i3.ChildProfile?>.value(),
          )
          as _i4.Future<_i3.ChildProfile?>);

  @override
  _i4.Future<bool> updateChildProfile(_i3.ChildProfile? profile) =>
      (super.noSuchMethod(
            Invocation.method(#updateChildProfile, [profile]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> deleteChildProfile(String? childId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteChildProfile, [childId]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<void> refreshData() =>
      (super.noSuchMethod(
            Invocation.method(#refreshData, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void clearError() => super.noSuchMethod(
    Invocation.method(#clearError, []),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<bool> updatePremiumStatus({
    required bool? isPremium,
    DateTime? expiryDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updatePremiumStatus, [], {
              #isPremium: isPremium,
              #expiryDate: expiryDate,
            }),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i3.ChildProfile? getChildProfile(String? childId) =>
      (super.noSuchMethod(Invocation.method(#getChildProfile, [childId]))
          as _i3.ChildProfile?);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i6.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i6.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [PurchaseProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockPurchaseProvider extends _i1.Mock implements _i7.PurchaseProvider {
  MockPurchaseProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  bool get isPremiumUnlocked =>
      (super.noSuchMethod(
            Invocation.getter(#isPremiumUnlocked),
            returnValue: false,
          )
          as bool);

  @override
  bool get isLoading =>
      (super.noSuchMethod(Invocation.getter(#isLoading), returnValue: false)
          as bool);

  @override
  List<_i8.ProductDetails> get products =>
      (super.noSuchMethod(
            Invocation.getter(#products),
            returnValue: <_i8.ProductDetails>[],
          )
          as List<_i8.ProductDetails>);

  @override
  List<_i8.ProductDetails> get premiumProducts =>
      (super.noSuchMethod(
            Invocation.getter(#premiumProducts),
            returnValue: <_i8.ProductDetails>[],
          )
          as List<_i8.ProductDetails>);

  @override
  List<_i8.ProductDetails> get contentPackProducts =>
      (super.noSuchMethod(
            Invocation.getter(#contentPackProducts),
            returnValue: <_i8.ProductDetails>[],
          )
          as List<_i8.ProductDetails>);

  @override
  Set<String> get unlockedContent =>
      (super.noSuchMethod(
            Invocation.getter(#unlockedContent),
            returnValue: <String>{},
          )
          as Set<String>);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  _i4.Future<bool> purchaseProduct(String? productId) =>
      (super.noSuchMethod(
            Invocation.method(#purchaseProduct, [productId]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> restorePurchases() =>
      (super.noSuchMethod(
            Invocation.method(#restorePurchases, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  bool isContentUnlocked(String? contentId) =>
      (super.noSuchMethod(
            Invocation.method(#isContentUnlocked, [contentId]),
            returnValue: false,
          )
          as bool);

  @override
  bool isStoryUnlocked(String? storyId) =>
      (super.noSuchMethod(
            Invocation.method(#isStoryUnlocked, [storyId]),
            returnValue: false,
          )
          as bool);

  @override
  _i8.ProductDetails? getProduct(String? productId) =>
      (super.noSuchMethod(Invocation.method(#getProduct, [productId]))
          as _i8.ProductDetails?);

  @override
  void clearError() => super.noSuchMethod(
    Invocation.method(#clearError, []),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<void> refresh() =>
      (super.noSuchMethod(
            Invocation.method(#refresh, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i6.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i6.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [ActiveChildProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockActiveChildProvider extends _i1.Mock
    implements _i9.ActiveChildProvider {
  MockActiveChildProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  List<_i3.ChildProfile> get profiles =>
      (super.noSuchMethod(
            Invocation.getter(#profiles),
            returnValue: <_i3.ChildProfile>[],
          )
          as List<_i3.ChildProfile>);

  @override
  bool get isCloudSyncAvailable =>
      (super.noSuchMethod(
            Invocation.getter(#isCloudSyncAvailable),
            returnValue: false,
          )
          as bool);

  @override
  bool get hasActiveProfile =>
      (super.noSuchMethod(
            Invocation.getter(#hasActiveProfile),
            returnValue: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  _i4.Future<void> setActiveProfile(String? profileId) =>
      (super.noSuchMethod(
            Invocation.method(#setActiveProfile, [profileId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void addProfile(_i3.ChildProfile? profile) => super.noSuchMethod(
    Invocation.method(#addProfile, [profile]),
    returnValueForMissingStub: null,
  );

  @override
  void updateProfile(_i3.ChildProfile? updatedProfile) => super.noSuchMethod(
    Invocation.method(#updateProfile, [updatedProfile]),
    returnValueForMissingStub: null,
  );

  @override
  void removeProfile(String? profileId) => super.noSuchMethod(
    Invocation.method(#removeProfile, [profileId]),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<void> updateLastStory(
    String? storyId,
    String? sceneId, {
    List<String>? choiceHistory,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #updateLastStory,
              [storyId, sceneId],
              {#choiceHistory: choiceHistory},
            ),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> hasStoryToResume() =>
      (super.noSuchMethod(
            Invocation.method(#hasStoryToResume, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<Map<String, dynamic>?> getLastStoryProgress() =>
      (super.noSuchMethod(
            Invocation.method(#getLastStoryProgress, []),
            returnValue: _i4.Future<Map<String, dynamic>?>.value(),
          )
          as _i4.Future<Map<String, dynamic>?>);

  @override
  _i4.Future<void> clearLastStoryProgress({String? storyId}) =>
      (super.noSuchMethod(
            Invocation.method(#clearLastStoryProgress, [], {#storyId: storyId}),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> syncWithCloud() =>
      (super.noSuchMethod(
            Invocation.method(#syncWithCloud, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> loadFromCloud(String? childId) =>
      (super.noSuchMethod(
            Invocation.method(#loadFromCloud, [childId]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<DateTime?> getLastSyncTimestamp() =>
      (super.noSuchMethod(
            Invocation.method(#getLastSyncTimestamp, []),
            returnValue: _i4.Future<DateTime?>.value(),
          )
          as _i4.Future<DateTime?>);

  @override
  void clearProfile() => super.noSuchMethod(
    Invocation.method(#clearProfile, []),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<bool> saveStoryProgress(_i3.StoryProgress? progress) =>
      (super.noSuchMethod(
            Invocation.method(#saveStoryProgress, [progress]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i3.StoryProgress? getStoryProgress(String? storyId) =>
      (super.noSuchMethod(Invocation.method(#getStoryProgress, [storyId]))
          as _i3.StoryProgress?);

  @override
  _i4.Future<bool> updatePreferences(Map<String, dynamic>? preferences) =>
      (super.noSuchMethod(
            Invocation.method(#updatePreferences, [preferences]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  int getCompletedStoriesCount() =>
      (super.noSuchMethod(
            Invocation.method(#getCompletedStoriesCount, []),
            returnValue: 0,
          )
          as int);

  @override
  int getTotalReadingTimeMinutes() =>
      (super.noSuchMethod(
            Invocation.method(#getTotalReadingTimeMinutes, []),
            returnValue: 0,
          )
          as int);

  @override
  List<_i3.StoryProgress> getStoriesInProgress() =>
      (super.noSuchMethod(
            Invocation.method(#getStoriesInProgress, []),
            returnValue: <_i3.StoryProgress>[],
          )
          as List<_i3.StoryProgress>);

  @override
  _i4.Future<bool> loadFromLocalStorage(String? childId) =>
      (super.noSuchMethod(
            Invocation.method(#loadFromLocalStorage, [childId]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> saveToLocalStorage() =>
      (super.noSuchMethod(
            Invocation.method(#saveToLocalStorage, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> clearLocalStorage() =>
      (super.noSuchMethod(
            Invocation.method(#clearLocalStorage, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  void addListener(_i6.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i6.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [StoryProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockStoryProvider extends _i1.Mock implements _i10.StoryProvider {
  MockStoryProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  List<_i11.Story> get stories =>
      (super.noSuchMethod(
            Invocation.getter(#stories),
            returnValue: <_i11.Story>[],
          )
          as List<_i11.Story>);

  @override
  List<String> get sceneHistory =>
      (super.noSuchMethod(
            Invocation.getter(#sceneHistory),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  List<Map<String, dynamic>> get choiceHistory =>
      (super.noSuchMethod(
            Invocation.getter(#choiceHistory),
            returnValue: <Map<String, dynamic>>[],
          )
          as List<Map<String, dynamic>>);

  @override
  bool get isLoading =>
      (super.noSuchMethod(Invocation.getter(#isLoading), returnValue: false)
          as bool);

  @override
  bool get isNarrationComplete =>
      (super.noSuchMethod(
            Invocation.getter(#isNarrationComplete),
            returnValue: false,
          )
          as bool);

  @override
  bool get showChoices =>
      (super.noSuchMethod(Invocation.getter(#showChoices), returnValue: false)
          as bool);

  @override
  bool get isSceneNarrationComplete =>
      (super.noSuchMethod(
            Invocation.getter(#isSceneNarrationComplete),
            returnValue: false,
          )
          as bool);

  @override
  bool get isNavigationBlocked =>
      (super.noSuchMethod(
            Invocation.getter(#isNavigationBlocked),
            returnValue: false,
          )
          as bool);

  @override
  List<String> get currentTextSegments =>
      (super.noSuchMethod(
            Invocation.getter(#currentTextSegments),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  int get currentSegmentIndex =>
      (super.noSuchMethod(
            Invocation.getter(#currentSegmentIndex),
            returnValue: 0,
          )
          as int);

  @override
  String get currentTextSegment =>
      (super.noSuchMethod(
            Invocation.getter(#currentTextSegment),
            returnValue: _i12.dummyValue<String>(
              this,
              Invocation.getter(#currentTextSegment),
            ),
          )
          as String);

  @override
  bool get autoProceedEnabled =>
      (super.noSuchMethod(
            Invocation.getter(#autoProceedEnabled),
            returnValue: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  _i10.ValidatedImageInfo? getValidatedImageInfo(String? sceneId) =>
      (super.noSuchMethod(Invocation.method(#getValidatedImageInfo, [sceneId]))
          as _i10.ValidatedImageInfo?);

  @override
  String? getValidatedStoryCoverPath(String? storyId) =>
      (super.noSuchMethod(
            Invocation.method(#getValidatedStoryCoverPath, [storyId]),
          )
          as String?);

  @override
  bool isImageLoading(String? imagePath) =>
      (super.noSuchMethod(
            Invocation.method(#isImageLoading, [imagePath]),
            returnValue: false,
          )
          as bool);

  @override
  bool isImageCached(String? sceneId) =>
      (super.noSuchMethod(
            Invocation.method(#isImageCached, [sceneId]),
            returnValue: false,
          )
          as bool);

  @override
  _i4.Future<void> loadAllStories() =>
      (super.noSuchMethod(
            Invocation.method(#loadAllStories, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void setActiveStory(_i11.Story story) =>
      super.noSuchMethod(
        Invocation.method(#setActiveStory, [story]),
        returnValueForMissingStub: null,
      );

  @override
  bool navigateToScene(String sceneId) =>
      (super.noSuchMethod(
            Invocation.method(#navigateToScene, [sceneId]),
            returnValue: false,
          )
          as bool);

  @override
  bool makeChoice(String choiceId) =>
      (super.noSuchMethod(
            Invocation.method(#makeChoice, [choiceId]),
            returnValue: false,
          )
          as bool);

  @override
  _i4.Future<void> goBackToPreviousChoice() =>
      (super.noSuchMethod(
            Invocation.method(#goBackToPreviousChoice, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> restartStory() =>
      (super.noSuchMethod(
            Invocation.method(#restartStory, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void completeNarration() => super.noSuchMethod(
    Invocation.method(#completeNarration, []),
    returnValueForMissingStub: null,
  );

  @override
  void setAutoProceedEnabled(bool? enabled) => super.noSuchMethod(
    Invocation.method(#setAutoProceedEnabled, [enabled]),
    returnValueForMissingStub: null,
  );

  @override
  bool navigateToNextSegment() =>
      (super.noSuchMethod(
            Invocation.method(#navigateToNextSegment, []),
            returnValue: false,
          )
          as bool);

  @override
  bool navigateToPreviousSegment() =>
      (super.noSuchMethod(
            Invocation.method(#navigateToPreviousSegment, []),
            returnValue: false,
          )
          as bool);

  @override
  void setShowChoices(bool? show) => super.noSuchMethod(
    Invocation.method(#setShowChoices, [show]),
    returnValueForMissingStub: null,
  );

  @override
  void markImageAsLoading(String? imagePath) => super.noSuchMethod(
    Invocation.method(#markImageAsLoading, [imagePath]),
    returnValueForMissingStub: null,
  );

  @override
  void markImageAsLoaded(String? imagePath, String? sceneId) =>
      super.noSuchMethod(
        Invocation.method(#markImageAsLoaded, [imagePath, sceneId]),
        returnValueForMissingStub: null,
      );

  @override
  void clearAllStoryData() => super.noSuchMethod(
    Invocation.method(#clearAllStoryData, []),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<void> setActiveStoryForTesting(_i11.Story story) =>
      (super.noSuchMethod(
            Invocation.method(#setActiveStoryForTesting, [story]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void clearError() => super.noSuchMethod(
    Invocation.method(#clearError, []),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i6.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i6.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [StorySettingsProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockStorySettingsProvider extends _i1.Mock
    implements _i13.StorySettingsProvider {
  MockStorySettingsProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  double get panelOpacity =>
      (super.noSuchMethod(Invocation.getter(#panelOpacity), returnValue: 0.0)
          as double);

  @override
  double get fontSize =>
      (super.noSuchMethod(Invocation.getter(#fontSize), returnValue: 0.0)
          as double);

  @override
  double get narrationSpeed =>
      (super.noSuchMethod(Invocation.getter(#narrationSpeed), returnValue: 0.0)
          as double);

  @override
  String get fontFamily =>
      (super.noSuchMethod(
            Invocation.getter(#fontFamily),
            returnValue: _i12.dummyValue<String>(
              this,
              Invocation.getter(#fontFamily),
            ),
          )
          as String);

  @override
  bool get useNaturalPauses =>
      (super.noSuchMethod(
            Invocation.getter(#useNaturalPauses),
            returnValue: false,
          )
          as bool);

  @override
  int get sentencePauseDuration =>
      (super.noSuchMethod(
            Invocation.getter(#sentencePauseDuration),
            returnValue: 0,
          )
          as int);

  @override
  int get segmentPauseDuration =>
      (super.noSuchMethod(
            Invocation.getter(#segmentPauseDuration),
            returnValue: 0,
          )
          as int);

  @override
  List<String> get availableFontFamilies =>
      (super.noSuchMethod(
            Invocation.getter(#availableFontFamilies),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  void setPanelOpacity(double? opacity) => super.noSuchMethod(
    Invocation.method(#setPanelOpacity, [opacity]),
    returnValueForMissingStub: null,
  );

  @override
  void setFontSize(double? size) => super.noSuchMethod(
    Invocation.method(#setFontSize, [size]),
    returnValueForMissingStub: null,
  );

  @override
  void setNarrationSpeed(double? speed) => super.noSuchMethod(
    Invocation.method(#setNarrationSpeed, [speed]),
    returnValueForMissingStub: null,
  );

  @override
  void setFontFamily(String? family) => super.noSuchMethod(
    Invocation.method(#setFontFamily, [family]),
    returnValueForMissingStub: null,
  );

  @override
  void setUseNaturalPauses(bool? enabled) => super.noSuchMethod(
    Invocation.method(#setUseNaturalPauses, [enabled]),
    returnValueForMissingStub: null,
  );

  @override
  void setSentencePauseDuration(int? duration) => super.noSuchMethod(
    Invocation.method(#setSentencePauseDuration, [duration]),
    returnValueForMissingStub: null,
  );

  @override
  void setSegmentPauseDuration(int? duration) => super.noSuchMethod(
    Invocation.method(#setSegmentPauseDuration, [duration]),
    returnValueForMissingStub: null,
  );

  @override
  void resetToDefaults() => super.noSuchMethod(
    Invocation.method(#resetToDefaults, []),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i6.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i6.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}
