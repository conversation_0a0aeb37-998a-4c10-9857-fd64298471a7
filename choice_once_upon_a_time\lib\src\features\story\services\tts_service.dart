import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_tts/flutter_tts.dart';

/// Service to handle text-to-speech functionality
class TTSService {
  /// The Flutter TTS instance
  final FlutterTts _flutterTts = FlutterTts();

  /// Whether T<PERSON> is currently speaking
  bool _isSpeaking = false;

  /// Whether TTS is enabled
  bool _isEnabled = true;

  /// The current word being spoken
  String _currentWord = '';

  /// The current word index
  int _currentWordIndex = 0;

  /// The list of words in the current text
  List<String> _words = [];

  /// Stream controller for word progress
  final StreamController<Map<String, dynamic>> _wordProgressController =
      StreamController<Map<String, dynamic>>.broadcast();

  /// Stream of word progress events
  Stream<Map<String, dynamic>> get wordProgressStream => _wordProgressController.stream;

  /// Whether TTS is currently speaking
  bool get isSpeaking => _isSpeaking;

  /// Whether TTS is enabled
  bool get isEnabled => _isEnabled;

  /// Get the current word being spoken
  String get currentWord => _currentWord;

  /// Get the current word index
  int get currentWordIndex => _currentWordIndex;

  /// Get the list of words in the current text
  List<String> get words => List.unmodifiable(_words);

  /// Constructor
  TTSService() {
    _initTTS();
  }

  /// Initialize TTS
  Future<void> _initTTS() async {
    try {
      // Set up TTS
      await _flutterTts.setLanguage('en-US');
      await _flutterTts.setSpeechRate(0.5); // Default: slower for children
      await _flutterTts.setVolume(1.0);
      await _flutterTts.setPitch(1.0);

      // Set up callbacks
      _flutterTts.setStartHandler(() {
        _isSpeaking = true;
        debugPrint('TTS started');
      });

      _flutterTts.setCompletionHandler(() {
        _isSpeaking = false;
        _currentWord = '';
        _currentWordIndex = 0;
        _wordProgressController.add({
          'type': 'completion',
          'word': '',
          'index': -1,
        });
        debugPrint('TTS completed');
      });

      _flutterTts.setErrorHandler((error) {
        _isSpeaking = false;
        debugPrint('TTS error: $error');
      });

      _flutterTts.setCancelHandler(() {
        _isSpeaking = false;
        debugPrint('TTS cancelled');
      });

      _flutterTts.setPauseHandler(() {
        _isSpeaking = false;
        debugPrint('TTS paused');
      });

      _flutterTts.setContinueHandler(() {
        _isSpeaking = true;
        debugPrint('TTS continued');
      });

      // Set up word boundary callback if supported
      if (await _flutterTts.isLanguageAvailable('en-US') ?? false) {
        _flutterTts.setProgressHandler(
          (String text, int startOffset, int endOffset, String word) {
            _currentWord = word;

            // Find the word index in the original text
            int wordIndex = _words.indexOf(word);
            if (wordIndex != -1) {
              _currentWordIndex = wordIndex;
              _wordProgressController.add({
                'type': 'progress',
                'word': word,
                'index': wordIndex,
              });
            }

            debugPrint('TTS progress: $word ($startOffset, $endOffset)');
          },
        );
      }
    } catch (e) {
      debugPrint('Error initializing TTS: $e');
    }
  }

  /// Speak the given text
  Future<void> speak(String text) async {
    if (!_isEnabled || text.isEmpty) {
      debugPrint('TTS not enabled or text is empty');
      return;
    }

    try {
      // Stop any current speech
      if (_isSpeaking) {
        await stop();
      }

      // Log the text being spoken for debugging
      debugPrint('Speaking text: "$text"');

      // Normalize the text to ensure proper processing
      // Remove any problematic characters or excessive whitespace
      final normalizedText = _normalizeText(text);
      debugPrint('Normalized text: "$normalizedText"');

      // Split text into words for tracking
      _words = normalizedText.split(' ').where((word) => word.isNotEmpty).toList();
      _currentWordIndex = 0;

      debugPrint('Word count: ${_words.length}');
      if (_words.isNotEmpty) {
        debugPrint('First word: "${_words.first}", Last word: "${_words.last}"');
      }

      // Start speaking
      final result = await _flutterTts.speak(normalizedText);
      debugPrint('TTS speak result: $result');
    } catch (e) {
      debugPrint('Error speaking: $e');
    }
  }

  /// Normalize text for TTS processing
  String _normalizeText(String text) {
    // Remove any problematic characters or control characters
    String normalized = text.replaceAll(RegExp(r'[\u0000-\u001F\u007F-\u009F]'), ' ');

    // Replace multiple spaces with a single space
    normalized = normalized.replaceAll(RegExp(r'\s+'), ' ').trim();

    return normalized;
  }

  /// Stop speaking
  Future<void> stop() async {
    if (_isSpeaking) {
      await _flutterTts.stop();
      _isSpeaking = false;
    }
  }

  /// Pause speaking
  Future<void> pause() async {
    if (_isSpeaking) {
      await _flutterTts.pause();
    }
  }

  /// Resume speaking
  Future<void> resume() async {
    if (!_isSpeaking) {
      // Just speak the current text again since continue_ is not available
      if (_words.isNotEmpty) {
        // If we have a current word, start from there
        if (_currentWordIndex >= 0 && _currentWordIndex < _words.length) {
          final remainingText = _words.sublist(_currentWordIndex).join(' ');
          debugPrint('Resuming speech from word $_currentWordIndex: "$remainingText"');
          await _flutterTts.speak(remainingText);
        } else {
          // If current word index is invalid, start from the beginning
          debugPrint('Invalid word index ($_currentWordIndex), starting from beginning');
          _currentWordIndex = 0;
          await _flutterTts.speak(_words.join(' '));
        }
      } else {
        debugPrint('Cannot resume speech: no words available');
      }
    } else {
      debugPrint('Already speaking, no need to resume');
    }
  }

  /// Enable or disable TTS
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    if (!enabled && _isSpeaking) {
      stop();
    }
  }

  /// Set the speech rate
  Future<void> setSpeechRate(double rate) async {
    if (rate >= 0.3 && rate <= 1.5) {
      await _flutterTts.setSpeechRate(rate);

      // If currently speaking, restart with new rate
      if (_isSpeaking && _currentWord.isNotEmpty && _words.isNotEmpty) {
        final currentText = _words.join(' ');
        await stop();
        await speak(currentText);
      }
    }
  }

  /// Dispose of resources
  void dispose() {
    _flutterTts.stop();
    _wordProgressController.close();
  }
}
