import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_tts/flutter_tts.dart';

/// Service to handle text-to-speech functionality
class TTSService {
  /// The Flutter TTS instance
  final FlutterTts _flutterTts = FlutterTts();

  /// Whether T<PERSON> is currently speaking
  bool _isSpeaking = false;

  /// Whether TTS is enabled
  bool _isEnabled = true;

  /// The current word being spoken
  String _currentWord = '';

  /// The current word index
  int _currentWordIndex = 0;

  /// The list of words in the current text
  List<String> _words = [];

  /// The list of sentences in the current text
  List<String> _sentences = [];

  /// The current sentence index
  int _currentSentenceIndex = 0;

  /// Stream controller for word progress
  final StreamController<Map<String, dynamic>> _wordProgressController =
      StreamController<Map<String, dynamic>>.broadcast();

  /// Completer for tracking speech completion
  Completer<void>? _speechCompleter;

  /// Current narration speed (0.3 to 1.5)
  double _narrationSpeed = 0.5;

  /// Whether to use natural pauses between sentences
  bool _useNaturalPauses = true;

  /// Pause duration between sentences (milliseconds)
  int _sentencePauseDuration = 650;

  /// Pause duration between segments (milliseconds)
  int _segmentPauseDuration = 1000;

  /// Stream of word progress events
  Stream<Map<String, dynamic>> get wordProgressStream => _wordProgressController.stream;

  /// Whether TTS is currently playing
  bool get isPlaying => _isSpeaking;

  /// Whether TTS is currently speaking
  bool get isSpeaking => _isSpeaking;

  /// Whether TTS is enabled
  bool get isEnabled => _isEnabled;

  /// Current word being spoken
  String get currentWord => _currentWord;

  /// Current word index
  int get currentWordIndex => _currentWordIndex;

  /// List of words in current text
  List<String> get words => List.unmodifiable(_words);

  /// Current sentence index
  int get currentSentenceIndex => _currentSentenceIndex;

  /// List of sentences in current text
  List<String> get sentences => List.unmodifiable(_sentences);

  /// Current narration speed
  double get narrationSpeed => _narrationSpeed;

  /// Whether natural pauses are enabled
  bool get useNaturalPauses => _useNaturalPauses;

  /// Sentence pause duration in milliseconds
  int get sentencePauseDuration => _sentencePauseDuration;

  /// Segment pause duration in milliseconds
  int get segmentPauseDuration => _segmentPauseDuration;

  /// Constructor
  TTSService() {
    _initTTS();
  }

  /// Initialize TTS
  Future<void> _initTTS() async {
    try {
      // Set up TTS
      await _flutterTts.setLanguage('en-US');
      await _flutterTts.setSpeechRate(_narrationSpeed); // Use configurable speed
      await _flutterTts.setVolume(1.0);
      await _flutterTts.setPitch(1.0);

      // Set up callbacks
      _flutterTts.setStartHandler(() {
        _isSpeaking = true;
        debugPrint('TTS started');
      });

      _flutterTts.setCompletionHandler(() {
        _isSpeaking = false;
        _currentWord = '';
        _currentWordIndex = 0;
        _wordProgressController.add({
          'type': 'completion',
          'word': '',
          'index': -1,
        });
        debugPrint('TTS completed');

        // Complete the speech future
        if (_speechCompleter != null && !_speechCompleter!.isCompleted) {
          _speechCompleter!.complete();
        }
      });

      _flutterTts.setErrorHandler((error) {
        _isSpeaking = false;
        debugPrint('TTS error: $error');

        // Complete the speech future with error
        if (_speechCompleter != null && !_speechCompleter!.isCompleted) {
          _speechCompleter!.completeError(error);
        }
      });

      _flutterTts.setCancelHandler(() {
        _isSpeaking = false;
        debugPrint('TTS cancelled');

        // Complete the speech future
        if (_speechCompleter != null && !_speechCompleter!.isCompleted) {
          _speechCompleter!.complete();
        }
      });

      _flutterTts.setPauseHandler(() {
        _isSpeaking = false;
        debugPrint('TTS paused');
      });

      _flutterTts.setContinueHandler(() {
        _isSpeaking = true;
        debugPrint('TTS continued');
      });

      // Set up word boundary callback if supported
      if (await _flutterTts.isLanguageAvailable('en-US') ?? false) {
        _flutterTts.setProgressHandler(
          (String text, int startOffset, int endOffset, String word) {
            _currentWord = word;

            // Find the word index in the original text
            int wordIndex = _words.indexOf(word);
            if (wordIndex != -1) {
              _currentWordIndex = wordIndex;
              _wordProgressController.add({
                'type': 'progress',
                'word': word,
                'index': wordIndex,
              });
            }

            debugPrint('TTS progress: $word ($startOffset, $endOffset)');
          },
        );
      }
    } catch (e) {
      debugPrint('Error initializing TTS: $e');
    }
  }

  /// Speak the given text with natural pauses and flow control
  Future<void> speak(String text) async {
    if (!_isEnabled || text.isEmpty) {
      debugPrint('TTS not enabled or text is empty');
      return;
    }

    try {
      // Stop any current speech
      if (_isSpeaking) {
        await stop();
      }

      // Log the text being spoken for debugging
      debugPrint('Speaking text: "$text"');

      // Normalize the text to ensure proper processing
      final normalizedText = _normalizeText(text);
      debugPrint('Normalized text: "$normalizedText"');

      // Split text into sentences and words for tracking
      _sentences = _splitIntoSentences(normalizedText);
      _words = normalizedText.split(' ').where((word) => word.isNotEmpty).toList();
      _currentWordIndex = 0;
      _currentSentenceIndex = 0;

      debugPrint('Sentence count: ${_sentences.length}, Word count: ${_words.length}');
      if (_sentences.isNotEmpty) {
        debugPrint('First sentence: "${_sentences.first}"');
        if (_sentences.length > 1) {
          debugPrint('Last sentence: "${_sentences.last}"');
        }
      }

      // Create a new completer for this speech operation
      _speechCompleter = Completer<void>();

      // Start natural speech delivery
      if (_useNaturalPauses && _sentences.length > 1) {
        await _speakWithNaturalPauses();
      } else {
        // Fallback to direct speech without pauses
        final result = await _flutterTts.speak(normalizedText);
        debugPrint('TTS speak initiated with result: $result');
      }

      // Wait for the actual speech completion
      if (_speechCompleter != null && !_speechCompleter!.isCompleted) {
        await _speechCompleter!.future;
        debugPrint('TTS speech actually completed for: "$normalizedText"');
      }
    } catch (e) {
      debugPrint('Error speaking: $e');
      // Ensure completer is completed even on error
      if (_speechCompleter != null && !_speechCompleter!.isCompleted) {
        _speechCompleter!.completeError(e);
      }
      rethrow;
    }
  }

  /// Split text into sentences for natural pause injection
  List<String> _splitIntoSentences(String text) {
    // Split by sentence terminators while preserving them
    final sentences = <String>[];
    final sentencePattern = RegExp(r'[.!?]+');

    int lastEnd = 0;
    for (final match in sentencePattern.allMatches(text)) {
      final sentence = text.substring(lastEnd, match.end).trim();
      if (sentence.isNotEmpty) {
        sentences.add(sentence);
      }
      lastEnd = match.end;
    }

    // Add any remaining text as the last sentence
    if (lastEnd < text.length) {
      final remaining = text.substring(lastEnd).trim();
      if (remaining.isNotEmpty) {
        sentences.add(remaining);
      }
    }

    // If no sentences found, return the whole text as one sentence
    if (sentences.isEmpty && text.trim().isNotEmpty) {
      sentences.add(text.trim());
    }

    return sentences;
  }

  /// Speak text with natural pauses between sentences
  Future<void> _speakWithNaturalPauses() async {
    debugPrint('Starting natural speech delivery for ${_sentences.length} sentences');

    for (int i = 0; i < _sentences.length; i++) {
      if (!_isSpeaking && _speechCompleter != null && _speechCompleter!.isCompleted) {
        // Speech was stopped, exit early
        debugPrint('Speech stopped during natural pause delivery');
        return;
      }

      _currentSentenceIndex = i;
      final sentence = _sentences[i];

      debugPrint('Speaking sentence $i: "$sentence"');

      // Create a temporary completer for this sentence
      final sentenceCompleter = Completer<void>();

      // Set up temporary completion handler for this sentence
      void tempCompletionHandler() {
        if (!sentenceCompleter.isCompleted) {
          sentenceCompleter.complete();
        }
      }

      // Store original handler and set temporary one
      _flutterTts.setCompletionHandler(tempCompletionHandler);

      // Speak the sentence
      await _flutterTts.speak(sentence);

      // Wait for this sentence to complete
      await sentenceCompleter.future;

      // Add pause between sentences (except after the last one)
      if (i < _sentences.length - 1) {
        debugPrint('Pausing for ${_sentencePauseDuration}ms between sentences');
        await Future.delayed(Duration(milliseconds: _sentencePauseDuration));
      }
    }

    // Restore original completion handler and complete the main speech
    _flutterTts.setCompletionHandler(() {
      _isSpeaking = false;
      _currentWord = '';
      _currentWordIndex = 0;
      _wordProgressController.add({
        'type': 'completion',
        'word': '',
        'index': -1,
      });
      debugPrint('TTS completed');

      // Complete the speech future
      if (_speechCompleter != null && !_speechCompleter!.isCompleted) {
        _speechCompleter!.complete();
      }
    });

    debugPrint('Natural speech delivery completed');
  }

  /// Normalize text for TTS processing
  String _normalizeText(String text) {
    // Remove any problematic characters or control characters
    String normalized = text.replaceAll(RegExp(r'[\u0000-\u001F\u007F-\u009F]'), ' ');

    // Replace multiple spaces with a single space
    normalized = normalized.replaceAll(RegExp(r'\s+'), ' ').trim();

    return normalized;
  }

  /// Stop speaking
  Future<void> stop() async {
    if (_isSpeaking) {
      await _flutterTts.stop();
      _isSpeaking = false;
    }

    // Complete any pending speech completer
    if (_speechCompleter != null && !_speechCompleter!.isCompleted) {
      _speechCompleter!.complete();
    }
  }

  /// Pause speaking
  Future<void> pause() async {
    if (_isSpeaking) {
      await _flutterTts.pause();
      _isSpeaking = false;
    }
  }

  /// Resume speaking
  Future<void> resume() async {
    if (!_isSpeaking && _words.isNotEmpty && _currentWordIndex < _words.length) {
      _isSpeaking = true;
      // Resume from current word
      final remainingText = _words.skip(_currentWordIndex).join(' ');
      debugPrint('Resuming speech from word $_currentWordIndex: "$remainingText"');

      // Create a new completer for the resumed speech
      _speechCompleter = Completer<void>();

      await _flutterTts.speak(remainingText);

      // Wait for completion
      if (_speechCompleter != null) {
        await _speechCompleter!.future;
      }
    }
  }

  /// Enable or disable TTS
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    if (!enabled && _isSpeaking) {
      stop();
    }
  }

  /// Set the speech rate (narration speed)
  Future<void> setSpeechRate(double rate) async {
    if (rate >= 0.3 && rate <= 1.5) {
      _narrationSpeed = rate;
      await _flutterTts.setSpeechRate(rate);
      debugPrint('Narration speed set to: $rate');

      // If currently speaking, restart with new rate
      if (_isSpeaking && _currentWord.isNotEmpty && _words.isNotEmpty) {
        final currentText = _words.join(' ');
        await stop();
        await speak(currentText);
      }
    }
  }

  /// Set natural pause settings
  void setNaturalPauseSettings({
    bool? useNaturalPauses,
    int? sentencePauseDuration,
    int? segmentPauseDuration,
  }) {
    if (useNaturalPauses != null) {
      _useNaturalPauses = useNaturalPauses;
      debugPrint('Natural pauses ${useNaturalPauses ? 'enabled' : 'disabled'}');
    }

    if (sentencePauseDuration != null && sentencePauseDuration >= 200 && sentencePauseDuration <= 2000) {
      _sentencePauseDuration = sentencePauseDuration;
      debugPrint('Sentence pause duration set to: ${sentencePauseDuration}ms');
    }

    if (segmentPauseDuration != null && segmentPauseDuration >= 500 && segmentPauseDuration <= 3000) {
      _segmentPauseDuration = segmentPauseDuration;
      debugPrint('Segment pause duration set to: ${segmentPauseDuration}ms');
    }
  }

  /// Get current narration settings
  Map<String, dynamic> getNarrationSettings() {
    return {
      'narrationSpeed': _narrationSpeed,
      'useNaturalPauses': _useNaturalPauses,
      'sentencePauseDuration': _sentencePauseDuration,
      'segmentPauseDuration': _segmentPauseDuration,
    };
  }

  /// Dispose of resources
  void dispose() {
    _flutterTts.stop();
    _wordProgressController.close();
  }
}
