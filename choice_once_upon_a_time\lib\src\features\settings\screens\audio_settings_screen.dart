import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../../audio/providers/audio_provider.dart';
import '../../../shared_kernel/theme/app_theme.dart';

/// Screen for audio settings
class AudioSettingsScreen extends StatelessWidget {
  /// Constructor
  const AudioSettingsScreen({super.key});

  /// Route name for navigation
  static const routeName = '/settings/audio';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Audio Settings'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Consumer2<SettingsProvider, AudioProvider>(
        builder: (context, settingsProvider, audioProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Background Music Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.music_note,
                            color: AppTheme.primaryColor,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Background Music',
                            style: AppTheme.subheadingStyle.copyWith(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Music enabled toggle
                      SwitchListTile(
                        title: const Text('Enable Background Music'),
                        subtitle: const Text('Play gentle music during stories'),
                        value: settingsProvider.backgroundMusicEnabled,
                        onChanged: (value) async {
                          await settingsProvider.setBackgroundMusicEnabled(value);
                          await audioProvider.updateSettings(
                            musicEnabled: value,
                            musicVolume: settingsProvider.backgroundMusicVolume,
                            sfxEnabled: settingsProvider.soundEffectsEnabled,
                            sfxVolume: settingsProvider.soundEffectsVolume,
                          );
                        },
                        activeColor: AppTheme.primaryColor,
                      ),
                      
                      // Music volume slider
                      if (settingsProvider.backgroundMusicEnabled) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Music Volume: ${(settingsProvider.backgroundMusicVolume * 100).round()}%',
                          style: AppTheme.bodyStyle.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Slider(
                          value: settingsProvider.backgroundMusicVolume,
                          onChanged: (value) async {
                            await settingsProvider.setBackgroundMusicVolume(value);
                            await audioProvider.updateSettings(
                              musicEnabled: settingsProvider.backgroundMusicEnabled,
                              musicVolume: value,
                              sfxEnabled: settingsProvider.soundEffectsEnabled,
                              sfxVolume: settingsProvider.soundEffectsVolume,
                            );
                          },
                          activeColor: AppTheme.primaryColor,
                          divisions: 10,
                          label: '${(settingsProvider.backgroundMusicVolume * 100).round()}%',
                        ),
                        
                        // Test music button
                        Center(
                          child: ElevatedButton.icon(
                            onPressed: audioProvider.isMusicPlaying
                              ? () => audioProvider.stopBackgroundMusic()
                              : () => audioProvider.playStoryMusic(),
                            icon: Icon(
                              audioProvider.isMusicPlaying ? Icons.stop : Icons.play_arrow,
                            ),
                            label: Text(
                              audioProvider.isMusicPlaying ? 'Stop Music' : 'Test Music',
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Sound Effects Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.volume_up,
                            color: AppTheme.primaryColor,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Sound Effects',
                            style: AppTheme.subheadingStyle.copyWith(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Sound effects enabled toggle
                      SwitchListTile(
                        title: const Text('Enable Sound Effects'),
                        subtitle: const Text('Play sounds for interactions'),
                        value: settingsProvider.soundEffectsEnabled,
                        onChanged: (value) async {
                          await settingsProvider.setSoundEffectsEnabled(value);
                          await audioProvider.updateSettings(
                            musicEnabled: settingsProvider.backgroundMusicEnabled,
                            musicVolume: settingsProvider.backgroundMusicVolume,
                            sfxEnabled: value,
                            sfxVolume: settingsProvider.soundEffectsVolume,
                          );
                        },
                        activeColor: AppTheme.primaryColor,
                      ),
                      
                      // Sound effects volume slider
                      if (settingsProvider.soundEffectsEnabled) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Effects Volume: ${(settingsProvider.soundEffectsVolume * 100).round()}%',
                          style: AppTheme.bodyStyle.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Slider(
                          value: settingsProvider.soundEffectsVolume,
                          onChanged: (value) async {
                            await settingsProvider.setSoundEffectsVolume(value);
                            await audioProvider.updateSettings(
                              musicEnabled: settingsProvider.backgroundMusicEnabled,
                              musicVolume: settingsProvider.backgroundMusicVolume,
                              sfxEnabled: settingsProvider.soundEffectsEnabled,
                              sfxVolume: value,
                            );
                          },
                          activeColor: AppTheme.primaryColor,
                          divisions: 10,
                          label: '${(settingsProvider.soundEffectsVolume * 100).round()}%',
                        ),
                        
                        // Test sound effects buttons
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: [
                            ElevatedButton(
                              onPressed: () => audioProvider.playButtonClickSound(),
                              child: const Text('Button Click'),
                            ),
                            ElevatedButton(
                              onPressed: () => audioProvider.playPageTurnSound(),
                              child: const Text('Page Turn'),
                            ),
                            ElevatedButton(
                              onPressed: () => audioProvider.playMagicSparkleSound(),
                              child: const Text('Magic Sparkle'),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Narration Settings Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.record_voice_over,
                            color: AppTheme.primaryColor,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Narration',
                            style: AppTheme.subheadingStyle.copyWith(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Narration enabled toggle
                      SwitchListTile(
                        title: const Text('Enable Narration'),
                        subtitle: const Text('Read stories aloud automatically'),
                        value: settingsProvider.narrationEnabled,
                        onChanged: settingsProvider.setNarrationEnabled,
                        activeColor: AppTheme.primaryColor,
                      ),
                      
                      if (settingsProvider.narrationEnabled) ...[
                        const SizedBox(height: 16),
                        
                        // Narration speed slider
                        Text(
                          'Reading Speed: ${(settingsProvider.narrationSpeed * 100).round()}%',
                          style: AppTheme.bodyStyle.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Slider(
                          value: settingsProvider.narrationSpeed,
                          min: 0.3,
                          max: 1.5,
                          onChanged: settingsProvider.setNarrationSpeed,
                          activeColor: AppTheme.primaryColor,
                          divisions: 12,
                          label: '${(settingsProvider.narrationSpeed * 100).round()}%',
                        ),
                        
                        // Natural pauses toggle
                        SwitchListTile(
                          title: const Text('Natural Pauses'),
                          subtitle: const Text('Add pauses between sentences'),
                          value: settingsProvider.naturalPauses,
                          onChanged: settingsProvider.setNaturalPauses,
                          activeColor: AppTheme.primaryColor,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
