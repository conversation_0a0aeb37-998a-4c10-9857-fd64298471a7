import 'dart:async';
import 'package:flutter/material.dart';
import '../../../shared_kernel/theme/app_theme.dart';
import '../services/tts_service.dart';

/// Widget that displays text with highlighting for the current word being spoken
class HighlightedText extends StatefulWidget {
  /// The text to display
  final String text;
  
  /// The TTS service
  final TTSService ttsService;
  
  /// The text style
  final TextStyle? style;
  
  /// The highlight color
  final Color highlightColor;
  
  /// Constructor
  const HighlightedText({
    super.key,
    required this.text,
    required this.ttsService,
    this.style,
    this.highlightColor = Colors.yellow,
  });

  @override
  State<HighlightedText> createState() => _HighlightedTextState();
}

class _HighlightedTextState extends State<HighlightedText> {
  /// The currently highlighted word
  String _highlightedWord = '';
  
  /// The index of the currently highlighted word
  int _highlightedWordIndex = -1;
  
  /// The list of words in the text
  late List<String> _words;
  
  /// Subscription to the TTS word progress stream
  StreamSubscription? _progressSubscription;
  
  @override
  void initState() {
    super.initState();
    _words = widget.text.split(' ');
    _setupTTSListener();
  }
  
  @override
  void didUpdateWidget(HighlightedText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text) {
      _words = widget.text.split(' ');
      _highlightedWord = '';
      _highlightedWordIndex = -1;
    }
    
    if (oldWidget.ttsService != widget.ttsService) {
      _progressSubscription?.cancel();
      _setupTTSListener();
    }
  }
  
  @override
  void dispose() {
    _progressSubscription?.cancel();
    super.dispose();
  }
  
  /// Set up the TTS listener
  void _setupTTSListener() {
    _progressSubscription = widget.ttsService.wordProgressStream.listen((event) {
      if (event['type'] == 'progress') {
        setState(() {
          _highlightedWord = event['word'];
          _highlightedWordIndex = event['index'];
        });
      } else if (event['type'] == 'completion') {
        setState(() {
          _highlightedWord = '';
          _highlightedWordIndex = -1;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // If no word is highlighted, just show the text normally
    if (_highlightedWord.isEmpty || _highlightedWordIndex == -1) {
      return Text(
        widget.text,
        style: widget.style ?? AppTheme.bodyStyle.copyWith(fontSize: 20),
      );
    }
    
    // Build the text with highlighting
    return RichText(
      text: TextSpan(
        style: widget.style ?? AppTheme.bodyStyle.copyWith(fontSize: 20),
        children: _buildTextSpans(),
      ),
    );
  }
  
  /// Build the text spans with highlighting
  List<TextSpan> _buildTextSpans() {
    final List<TextSpan> spans = [];
    
    for (int i = 0; i < _words.length; i++) {
      final word = _words[i];
      final isHighlighted = i == _highlightedWordIndex;
      
      // Add a space before each word except the first
      if (i > 0) {
        spans.add(const TextSpan(text: ' '));
      }
      
      // Add the word with or without highlighting
      spans.add(
        TextSpan(
          text: word,
          style: isHighlighted
              ? TextStyle(
                  backgroundColor: widget.highlightColor,
                  fontWeight: FontWeight.bold,
                )
              : null,
        ),
      );
    }
    
    return spans;
  }
}
