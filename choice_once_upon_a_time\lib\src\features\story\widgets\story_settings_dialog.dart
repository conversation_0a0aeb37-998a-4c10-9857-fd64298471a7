import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../shared_kernel/theme/app_theme.dart';
import '../providers/story_settings_provider.dart';
import '../services/tts_service.dart';

/// Dialog for story-specific settings
class StorySettingsDialog extends StatelessWidget {
  /// The TTS service
  final TTSService ttsService;
  
  /// Constructor
  const StorySettingsDialog({
    super.key,
    required this.ttsService,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<StorySettingsProvider>(
      builder: (context, settingsProvider, child) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                const Text(
                  'Story Settings',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 24),
                
                // Panel transparency
                _buildSettingSection(
                  title: 'Panel Transparency',
                  child: Slider(
                    value: settingsProvider.panelOpacity,
                    min: 0.3,
                    max: 1.0,
                    divisions: 7,
                    label: _getOpacityLabel(settingsProvider.panelOpacity),
                    onChanged: (value) {
                      settingsProvider.setPanelOpacity(value);
                    },
                  ),
                ),
                
                // Font size
                _buildSettingSection(
                  title: 'Font Size',
                  child: Slider(
                    value: settingsProvider.fontSize,
                    min: 16.0,
                    max: 32.0,
                    divisions: 8,
                    label: '${settingsProvider.fontSize.toInt()}',
                    onChanged: (value) {
                      settingsProvider.setFontSize(value);
                    },
                  ),
                ),
                
                // Narration speed
                _buildSettingSection(
                  title: 'Narration Speed',
                  child: Slider(
                    value: settingsProvider.narrationSpeed,
                    min: 0.3,
                    max: 1.5,
                    divisions: 6,
                    label: _getSpeedLabel(settingsProvider.narrationSpeed),
                    onChanged: (value) {
                      settingsProvider.setNarrationSpeed(value);
                      ttsService.setSpeechRate(value);
                    },
                  ),
                ),
                
                // Font style
                _buildSettingSection(
                  title: 'Font Style',
                  child: DropdownButton<String>(
                    value: settingsProvider.fontFamily,
                    isExpanded: true,
                    items: settingsProvider.availableFontFamilies
                        .map((family) => DropdownMenuItem<String>(
                              value: family,
                              child: Text(
                                family,
                                style: TextStyle(fontFamily: family),
                              ),
                            ))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        settingsProvider.setFontFamily(value);
                      }
                    },
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // Reset button
                    TextButton(
                      onPressed: () {
                        settingsProvider.resetToDefaults();
                        ttsService.setSpeechRate(0.5);
                      },
                      child: const Text('Reset to Defaults'),
                    ),
                    const SizedBox(width: 16),
                    
                    // Close button
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text('Close'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  /// Build a setting section with a title and child widget
  Widget _buildSettingSection({
    required String title,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        child,
        const SizedBox(height: 16),
      ],
    );
  }
  
  /// Get a label for the opacity value
  String _getOpacityLabel(double opacity) {
    if (opacity <= 0.4) return 'Low';
    if (opacity <= 0.7) return 'Medium';
    return 'High';
  }
  
  /// Get a label for the speed value
  String _getSpeedLabel(double speed) {
    if (speed <= 0.4) return 'Very Slow';
    if (speed <= 0.6) return 'Slow';
    if (speed <= 0.8) return 'Normal';
    if (speed <= 1.0) return 'Fast';
    return 'Very Fast';
  }
}
