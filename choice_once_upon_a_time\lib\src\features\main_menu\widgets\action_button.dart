import 'package:flutter/material.dart';
import '../../../shared_kernel/theme/app_theme.dart';
import 'dart:math' as math;

/// Widget for the action buttons on the main menu
class ActionButton extends StatelessWidget {
  /// The icon to display
  final IconData icon;

  /// The text to display
  final String text;

  /// The action to perform when tapped
  final VoidCallback onTap;

  /// Whether the button is enabled
  final bool isEnabled;

  /// Constructor
  const ActionButton({
    super.key,
    required this.icon,
    required this.text,
    required this.onTap,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions for responsive sizing
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    // Calculate responsive sizes
    // Use the smaller dimension to ensure proper sizing on all devices
    final double minDimension = math.min(screenWidth, screenHeight);
    final double iconSize = minDimension * 0.06; // 6% of min dimension
    final double fontSize = minDimension * 0.02; // 2% of min dimension
    final double padding = minDimension * 0.02; // 2% of min dimension
    final double borderRadius = minDimension * 0.02; // 2% of min dimension

    return Card(
      elevation: isEnabled ? 4 : 1,
      margin: EdgeInsets.all(minDimension * 0.01), // 1% of min dimension
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      color: isEnabled
          ? AppTheme.cardColor
          : Colors.grey[300],
      child: InkWell(
        onTap: isEnabled ? onTap : null,
        borderRadius: BorderRadius.circular(borderRadius),
        child: Padding(
          padding: EdgeInsets.all(padding),
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: iconSize,
                  color: isEnabled
                      ? AppTheme.primaryColor
                      : Colors.grey,
                ),
                SizedBox(height: padding / 2),
                Text(
                  text,
                  style: AppTheme.bodyStyle.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: fontSize,
                    color: isEnabled
                        ? AppTheme.textColor
                        : Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
