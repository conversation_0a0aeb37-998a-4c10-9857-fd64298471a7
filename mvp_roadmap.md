MVP Roadmap for "Choice: Once Upon A Time"
This roadmap outlines the key phases and tasks to develop the Minimum Viable Product (MVP) for the "Choice: Once Upon A Time" mobile application.

Phase 1: Project Setup & Foundation ⚙️
Task 1.1: Development Environment Setup
Description: Install/update Flutter SDK, Dart SDK, Android Studio (with Flutter plugin), and any other necessary IDEs or tools (e.g., VS Code with Flutter extension). Set up emulators/simulators.
Task 1.2: Flutter Project Initialization
Description: Create a new Flutter project. Choose an appropriate package name.
Task 1.3: Version Control Setup (Git)
Description: Initialize a Git repository. Create an initial commit. Set up a remote repository.
Task 1.4: Firebase Project Creation & Initial Setup
Description: Create a new project in the Firebase console. Configure it for Android. Download and add google-services.json.
Task 1.5: Define Core App Structure & Navigation Shell
Description: Plan basic folder structure. Implement basic navigation shell.
Task 1.6: Basic Asset Management Strategy
Description: Create folders for assets. Add initial placeholders. Update pubspec.yaml.
Phase 2: Core Content Design & Prototyping 📖🎨
Task 2.1: Define Story JSON Structure
Description: Design JSON format for interactive stories (scenes, text, choices, paths, illustration references).
Task 2.2: Write/Adapt Initial 1-2 Stories (Text Only)
Description: Draft narrative for 1-2 simple stories based on JSON structure.
Task 2.3: Design Static Illustration Style & Create/Source Placeholders
Description: Decide on art style. Create/find placeholder images for initial stories.
Task 2.4: TTS Integration - Basic Implementation & Voice Selection
Description: Research and integrate a Flutter Text-to-Speech (TTS) plugin. Implement basic functionality. Select voice.
Phase 3: Child-Facing UI/UX Development (Core Story Experience) 👧🏾👦🏻🎮
Task 3.1: Story Listing Screen UI
Description: Develop UI for displaying available stories. Visual cues for free/locked stories.
Task 3.2: Story Reading/Interaction Screen UI
Description: Create screen for story presentation (text display, choice buttons).
Task 3.3: Illustration Display Component
Description: Develop a widget to display static illustrations for story scenes.
Task 3.4: Basic Navigation between Story List and Story Reader
Description: Implement navigation flow from story list to story interaction screen.
Task 3.5: Implement TTS Playback Controls (Optional for MVP)
Description: Consider simple play/pause or auto-play per scene.
Phase 4: Story Logic & State Management 🧠⚙️
Task 4.1: Implement Story Parsing Logic (from JSON)
Description: Write Dart code to parse story JSON files into data models.
Task 4.2: Develop Choice Handling Mechanism
Description: Implement logic to process child's choice and determine next scene.
Task 4.3: State Management for Story Progress
Description: Implement state management (e.g., Provider) for current story, scene, choices.
Task 4.4: Ensure Story Path Branching Logic Works Correctly
Description: Test branching logic for correct story paths.
Phase 5: Parent-Facing Features & Firebase Integration (Part 1) 👨‍👩‍👧‍👦🔒
Task 5.1: Firebase Authentication Setup (Email/Password for Parents)
Description: Integrate Firebase Authentication. Implement parent sign-up/login.
Task 5.2: Parent Profile Creation UI (Simple login/signup)
Description: Design and develop UI screens for parent registration and login.
Task 5.3: Firestore Database Setup: Data Models
Description: Set up Firestore collections for Parent and Child profiles.
Task 5.4: Basic Child Profile Management (Local or Simple Firestore)
Description: Allow authenticated parents to create simple child profiles.
Task 5.5: Firebase Security Rules (Initial Draft)
Description: Write initial Firestore security rules for authentication and parent-child data.
Phase 6: Monetization (MVP) 💰🛒
Task 6.1: Research Flutter In-App Purchase (IAP) Plugin
Description: Select and research a reliable Flutter IAP plugin (e.g., in_app_purchase).
Task 6.2: Configure Non-Consumable IAP Product
Description: Set up non-consumable product in Google Play Console (and App Store Connect placeholder).
Task 6.3: Implement IAP Logic
Description: Integrate IAP plugin to fetch products, initiate purchase, verify purchase, unlock content.
Task 6.4: UI for "Unlock Full Version" Prompt/Button
Description: Design and implement UI elements for purchase prompt.
Task 6.5: Logic to Differentiate Free vs. Paid Content
Description: Implement logic to check purchase status and show stories as free/locked.
Phase 7: Content Expansion & Refinement (MVP) 📚✨
Task 7.1: Finalize 3-5 Interactive Stories
Description: Complete writing, structuring (JSON), and testing all MVP stories.
Task 7.2: Create/Source Final Static Illustrations
Description: Create/acquire final static illustrations for all MVP stories.
Task 7.3: Integrate Final Content and Illustrations into the App
Description: Add all finalized story JSON files and illustrations to app assets.
Phase 8: Backend Integration (Part 2) & Analytics 📊📡
Task 8.1: Integrate Firebase Analytics
Description: Add Firebase Analytics for basic user interaction tracking.
Task 8.2: Integrate Firebase Crashlytics
Description: Add Firebase Crashlytics for automatic crash reporting.
Task 8.3: Refine Firestore Security Rules
Description: Review and refine Firestore security rules.
Phase 9: Testing & Quality Assurance 🐞✔️
Task 9.1: Unit Testing
Description: Write unit tests for critical logic.
Task 9.2: Widget Testing
Description: Write widget tests for key UI components.
Task 9.3: Manual End-to-End Testing
Description: Perform comprehensive manual testing of all features.
Task 9.4: Usability Testing (Informal)
Description: If possible, get feedback from target age group and parents.
Task 9.5: Cross-Platform Testing (Android Phone/TV - Initial Focus)
Description: Test on various Android phone screen sizes/resolutions and Android TV.
Task 9.6: Bug Fixing and Iteration
Description: Address bugs and issues identified during testing.
Phase 10: Pre-Launch & Deployment (Android) 🚀📲
Task 10.1: App Icon & Splash Screen Creation
Description: Design and implement app icon and splash screen.
Task 10.2: Create App Store Listings (Google Play Store)
Description: Prepare Google Play Store listing materials (title, description, screenshots, privacy policy).
Task 10.3: Build Release Version of the App (Android)
Description: Configure build settings for release. Generate signed AAB/APK.
Task 10.4: Internal Testing / Closed Alpha/Beta on Google Play
Description: Upload release build for internal/closed testing.
Task 10.5: Final Checks and Submission to Google Play Store
Description: Perform final checks and submit to Google Play Store.
