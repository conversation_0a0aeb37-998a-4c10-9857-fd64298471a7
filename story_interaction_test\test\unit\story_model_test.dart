import 'package:flutter_test/flutter_test.dart';
import 'package:story_interaction_test/src/models/story_model.dart';

void main() {
  group('StoryChoice', () {
    test('should create from JSON correctly', () {
      final json = {
        'choiceText': 'Help Shelly gather kelp',
        'nextSceneId': 'finn_helps_shelly_team_up_A',
      };

      final choice = StoryChoice.fromJson(json);

      expect(choice.text, equals('Help Shelly gather kelp'));
      expect(choice.nextSceneId, equals('finn_helps_shelly_team_up_A'));
    });
  });

  group('StoryScene', () {
    test('should create from <PERSON><PERSON><PERSON> correctly', () {
      final json = {
        'sceneId': 'test_scene',
        'narrationText': 'This is a test scene.',
        'imageAssetPath': 'assets/images/test/scene.png',
        'isChoicePoint': true,
        'choicePrompt': 'What do you want to do?',
        'choices': [
          {
            'choiceText': 'Option 1',
            'nextSceneId': 'scene_1',
          },
          {
            'choiceText': 'Option 2',
            'nextSceneId': 'scene_2',
          },
        ],
        'isEndingScene': false,
        'moralLessonReinforced': null,
      };

      final scene = StoryScene.fromJson(json);

      expect(scene.id, equals('test_scene'));
      expect(scene.narrationText, equals('This is a test scene.'));
      expect(scene.imagePath, equals('assets/images/test/scene.png'));
      expect(scene.isChoicePoint, isTrue);
      expect(scene.choicePrompt, equals('What do you want to do?'));
      expect(scene.choices.length, equals(2));
      expect(scene.choices[0].text, equals('Option 1'));
      expect(scene.choices[1].nextSceneId, equals('scene_2'));
      expect(scene.isEndingScene, isFalse);
      expect(scene.moralLesson, isNull);
    });

    test('should handle scene without choices', () {
      final json = {
        'sceneId': 'simple_scene',
        'narrationText': 'A simple scene.',
        'imageAssetPath': 'assets/images/test/simple.png',
        'isChoicePoint': false,
        'choices': [],
        'isEndingScene': false,
      };

      final scene = StoryScene.fromJson(json);

      expect(scene.isChoicePoint, isFalse);
      expect(scene.choices, isEmpty);
      expect(scene.choicePrompt, isNull);
    });

    test('should handle ending scene', () {
      final json = {
        'sceneId': 'ending_scene',
        'narrationText': 'The end of the story.',
        'imageAssetPath': 'assets/images/test/ending.png',
        'isChoicePoint': false,
        'choices': [],
        'isEndingScene': true,
        'moralLessonReinforced': 'Always be kind to others.',
      };

      final scene = StoryScene.fromJson(json);

      expect(scene.isEndingScene, isTrue);
      expect(scene.moralLesson, equals('Always be kind to others.'));
    });
  });

  group('Story', () {
    test('should create from JSON correctly', () {
      final json = {
        'storyId': 'test_story',
        'storyTitle': 'Test Story',
        'targetAge': '4-6 years old',
        'moralTheme': 'Friendship',
        'storyNodes': [
          {
            'sceneId': 'scene_1',
            'narrationText': 'First scene.',
            'imageAssetPath': 'assets/images/test/scene1.png',
            'isChoicePoint': false,
            'choices': [],
            'isEndingScene': false,
          },
          {
            'sceneId': 'scene_2',
            'narrationText': 'Second scene.',
            'imageAssetPath': 'assets/images/test/scene2.png',
            'isChoicePoint': false,
            'choices': [],
            'isEndingScene': true,
          },
        ],
      };

      final story = Story.fromJson(json);

      expect(story.id, equals('test_story'));
      expect(story.title, equals('Test Story'));
      expect(story.targetAge, equals('4-6 years old'));
      expect(story.moralTheme, equals('Friendship'));
      expect(story.scenes.length, equals(2));
      expect(story.firstScene.id, equals('scene_1'));
    });

    test('should find scene by ID', () {
      final json = {
        'storyId': 'test_story',
        'storyTitle': 'Test Story',
        'storyNodes': [
          {
            'sceneId': 'scene_1',
            'narrationText': 'First scene.',
            'imageAssetPath': 'assets/images/test/scene1.png',
            'isChoicePoint': false,
            'choices': [],
            'isEndingScene': false,
          },
          {
            'sceneId': 'scene_2',
            'narrationText': 'Second scene.',
            'imageAssetPath': 'assets/images/test/scene2.png',
            'isChoicePoint': false,
            'choices': [],
            'isEndingScene': true,
          },
        ],
      };

      final story = Story.fromJson(json);

      final scene1 = story.getSceneById('scene_1');
      final scene2 = story.getSceneById('scene_2');
      final nonExistent = story.getSceneById('scene_3');

      expect(scene1, isNotNull);
      expect(scene1!.id, equals('scene_1'));
      expect(scene2, isNotNull);
      expect(scene2!.id, equals('scene_2'));
      expect(nonExistent, isNull);
    });
  });
}
