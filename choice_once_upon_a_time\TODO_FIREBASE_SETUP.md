# Firebase Setup Instructions for Choice: Once Upon A Time

## Overview
This document provides step-by-step instructions for setting up Firebase Authentication and Firestore for the Choice: Once Upon A Time Flutter app.

## Prerequisites
- Google account
- Flutter development environment set up
- Choice: Once Upon A Time project cloned locally

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter project name: `choice-once-upon-a-time` (or your preferred name)
4. Choose whether to enable Google Analytics (recommended: Yes)
5. Select or create a Google Analytics account
6. Click "Create project"

## Step 2: Enable Authentication

1. In the Firebase Console, navigate to **Authentication** > **Sign-in method**
2. Enable the following sign-in providers:
   - **Email/Password**: Click to enable, toggle "Enable" and save
   - **Anonymous**: Click to enable, toggle "Enable" and save (for guest access)

## Step 3: Create Firestore Database

1. Navigate to **Firestore Database** in the Firebase Console
2. Click "Create database"
3. <PERSON><PERSON> "Start in test mode" (we'll configure security rules later)
4. Select a location closest to your users (e.g., us-central1)
5. Click "Done"

## Step 4: Configure Firestore Security Rules

Replace the default Firestore security rules with the following:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read/write their own parent profile
    match /parents/{parentId} {
      allow read, write: if request.auth != null && request.auth.uid == parentId;
    }
    
    // Allow authenticated parents to read/write their children's profiles
    match /children/{childId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.parentId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.parentId;
    }
    
    // Allow authenticated users to read/write story progress for their children
    match /story_progress/{progressId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.parentId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.parentId;
    }
    
    // Allow all users to read stories (public content)
    match /stories/{storyId} {
      allow read: if true;
      allow write: if false; // Only admins can modify stories
    }
    
    // Allow all users to read app settings
    match /app_settings/{settingId} {
      allow read: if true;
      allow write: if false; // Only admins can modify settings
    }
  }
}
```

## Step 5: Add Flutter App to Firebase Project

### For Android:
1. In Firebase Console, click "Add app" and select Android
2. Enter Android package name: `com.example.choice_once_upon_a_time`
3. Enter app nickname: `Choice Once Upon A Time`
4. Download `google-services.json`
5. Place the file in `android/app/` directory

### For iOS:
1. In Firebase Console, click "Add app" and select iOS
2. Enter iOS bundle ID: `com.example.choiceOnceUponATime`
3. Enter app nickname: `Choice Once Upon A Time`
4. Download `GoogleService-Info.plist`
5. Place the file in `ios/Runner/` directory

### For Web:
1. In Firebase Console, click "Add app" and select Web
2. Enter app nickname: `Choice Once Upon A Time`
3. Copy the Firebase configuration object
4. Update `web/index.html` with the Firebase configuration

## Step 6: Update Flutter Dependencies

Ensure your `pubspec.yaml` includes the required Firebase dependencies:

```yaml
dependencies:
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.5.6
```

## Step 7: Initialize Firebase in Flutter

The app should already have Firebase initialization code in `lib/main.dart`. Verify it looks like this:

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  runApp(const MyApp());
}
```

## Step 8: Test Firebase Connection

1. Run the app: `flutter run`
2. Try creating a parent account
3. Try creating a child profile
4. Check Firebase Console to see if data appears in Firestore

## Troubleshooting

### Common Issues:

1. **Permission Denied Error**:
   - Verify Firestore security rules are correctly configured
   - Ensure user is authenticated before accessing Firestore
   - Check that parentId matches the authenticated user's UID

2. **App Not Connecting to Firebase**:
   - Verify `google-services.json` (Android) or `GoogleService-Info.plist` (iOS) is in the correct location
   - Ensure package name/bundle ID matches Firebase configuration
   - Check that Firebase is initialized in `main.dart`

3. **Authentication Issues**:
   - Verify Email/Password authentication is enabled in Firebase Console
   - Check that authentication rules allow the sign-in method being used

## Development vs Production

### Development Environment:
- Use test mode Firestore rules initially
- Enable debug logging for Firebase
- Use Firebase Emulator Suite for local development (optional)

### Production Environment:
- Review and tighten Firestore security rules
- Disable debug logging
- Set up proper backup and monitoring
- Configure proper authentication domain restrictions

## Security Best Practices

1. **Never expose Firebase configuration secrets** in client-side code
2. **Always validate data** on both client and server side
3. **Use least-privilege principle** in Firestore rules
4. **Regularly audit** Firestore security rules
5. **Monitor usage** and set up alerts for unusual activity

## Support

If you encounter issues:
1. Check the [Firebase Documentation](https://firebase.google.com/docs)
2. Review the [FlutterFire Documentation](https://firebase.flutter.dev/)
3. Check the app's error logs and Firebase Console logs
4. Ensure all dependencies are up to date

## Alternative: Local Testing Mode

If Firebase setup is not immediately available, the app includes a local testing mode that bypasses Firebase authentication. See the next section for enabling this mode.
