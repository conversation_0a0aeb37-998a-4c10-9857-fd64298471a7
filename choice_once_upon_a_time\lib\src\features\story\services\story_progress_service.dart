import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service to handle saving and loading story progress
class StoryProgressService {
  /// Key for storing story progress in shared preferences
  static const String _progressKey = 'story_progress';
  
  /// Key for storing the active child ID in shared preferences
  static const String _activeChildKey = 'active_child_id';
  
  /// Save story progress for a child
  static Future<bool> saveProgress({
    required String childId,
    required String storyId,
    required String sceneId,
    List<String>? choiceHistory,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Get existing progress data or create new
      final String? progressJson = prefs.getString(_progressKey);
      final Map<String, dynamic> progressData = progressJson != null
          ? json.decode(progressJson) as Map<String, dynamic>
          : {};
      
      // Create or update child's progress
      if (!progressData.containsKey(childId)) {
        progressData[childId] = {};
      }
      
      // Save story progress
      progressData[childId][storyId] = {
        'sceneId': sceneId,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        if (choiceHistory != null) 'choiceHistory': choiceHistory,
      };
      
      // Save updated progress data
      await prefs.setString(_progressKey, json.encode(progressData));
      
      return true;
    } catch (e) {
      debugPrint('Error saving story progress: $e');
      return false;
    }
  }
  
  /// Load story progress for a child
  static Future<Map<String, dynamic>?> loadProgress({
    required String childId,
    String? storyId,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Get existing progress data
      final String? progressJson = prefs.getString(_progressKey);
      if (progressJson == null) {
        return null;
      }
      
      final Map<String, dynamic> progressData = 
          json.decode(progressJson) as Map<String, dynamic>;
      
      // Check if child has any progress
      if (!progressData.containsKey(childId)) {
        return null;
      }
      
      // If storyId is provided, return progress for that story
      if (storyId != null) {
        if (!progressData[childId].containsKey(storyId)) {
          return null;
        }
        
        return {
          'storyId': storyId,
          ...progressData[childId][storyId] as Map<String, dynamic>,
        };
      }
      
      // Otherwise, find the most recent story progress
      String? mostRecentStoryId;
      int? mostRecentTimestamp;
      
      progressData[childId].forEach((key, value) {
        final int timestamp = value['timestamp'] as int;
        if (mostRecentTimestamp == null || timestamp > mostRecentTimestamp!) {
          mostRecentStoryId = key;
          mostRecentTimestamp = timestamp;
        }
      });
      
      if (mostRecentStoryId == null) {
        return null;
      }
      
      return {
        'storyId': mostRecentStoryId,
        ...progressData[childId][mostRecentStoryId] as Map<String, dynamic>,
      };
    } catch (e) {
      debugPrint('Error loading story progress: $e');
      return null;
    }
  }
  
  /// Clear story progress for a child
  static Future<bool> clearProgress({
    required String childId,
    String? storyId,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Get existing progress data
      final String? progressJson = prefs.getString(_progressKey);
      if (progressJson == null) {
        return true;
      }
      
      final Map<String, dynamic> progressData = 
          json.decode(progressJson) as Map<String, dynamic>;
      
      // Check if child has any progress
      if (!progressData.containsKey(childId)) {
        return true;
      }
      
      // If storyId is provided, clear progress for that story
      if (storyId != null) {
        if (progressData[childId].containsKey(storyId)) {
          progressData[childId].remove(storyId);
        }
      } else {
        // Otherwise, clear all progress for the child
        progressData.remove(childId);
      }
      
      // Save updated progress data
      await prefs.setString(_progressKey, json.encode(progressData));
      
      return true;
    } catch (e) {
      debugPrint('Error clearing story progress: $e');
      return false;
    }
  }
  
  /// Save the active child ID
  static Future<bool> saveActiveChildId(String childId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_activeChildKey, childId);
      return true;
    } catch (e) {
      debugPrint('Error saving active child ID: $e');
      return false;
    }
  }
  
  /// Load the active child ID
  static Future<String?> loadActiveChildId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_activeChildKey);
    } catch (e) {
      debugPrint('Error loading active child ID: $e');
      return null;
    }
  }
}
