import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../profile/models/child_profile.dart';

/// Service to handle saving and loading story progress
class StoryProgressService {
  /// Key for storing story progress in shared preferences
  static const String _progressKey = 'story_progress';

  /// Key for storing the active child ID in shared preferences
  static const String _activeChildKey = 'active_child_id';

  /// Firebase instances
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Save story progress for a child
  static Future<bool> saveProgress({
    required String childId,
    required String storyId,
    required String sceneId,
    List<String>? choiceHistory,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get existing progress data or create new
      final String? progressJson = prefs.getString(_progressKey);
      final Map<String, dynamic> progressData = progressJson != null
          ? json.decode(progressJson) as Map<String, dynamic>
          : {};

      // Create or update child's progress
      if (!progressData.containsKey(childId)) {
        progressData[childId] = {};
      }

      // Save story progress
      progressData[childId][storyId] = {
        'sceneId': sceneId,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        if (choiceHistory != null) 'choiceHistory': choiceHistory,
      };

      // Save updated progress data
      await prefs.setString(_progressKey, json.encode(progressData));

      return true;
    } catch (e) {
      debugPrint('Error saving story progress: $e');
      return false;
    }
  }

  /// Load story progress for a child
  static Future<Map<String, dynamic>?> loadProgress({
    required String childId,
    String? storyId,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get existing progress data
      final String? progressJson = prefs.getString(_progressKey);
      if (progressJson == null) {
        return null;
      }

      final Map<String, dynamic> progressData =
          json.decode(progressJson) as Map<String, dynamic>;

      // Check if child has any progress
      if (!progressData.containsKey(childId)) {
        return null;
      }

      // If storyId is provided, return progress for that story
      if (storyId != null) {
        if (!progressData[childId].containsKey(storyId)) {
          return null;
        }

        return {
          'storyId': storyId,
          ...progressData[childId][storyId] as Map<String, dynamic>,
        };
      }

      // Otherwise, find the most recent story progress
      String? mostRecentStoryId;
      int? mostRecentTimestamp;

      progressData[childId].forEach((key, value) {
        final int timestamp = value['timestamp'] as int;
        if (mostRecentTimestamp == null || timestamp > mostRecentTimestamp!) {
          mostRecentStoryId = key;
          mostRecentTimestamp = timestamp;
        }
      });

      if (mostRecentStoryId == null) {
        return null;
      }

      return {
        'storyId': mostRecentStoryId,
        ...progressData[childId][mostRecentStoryId] as Map<String, dynamic>,
      };
    } catch (e) {
      debugPrint('Error loading story progress: $e');
      return null;
    }
  }

  /// Clear story progress for a child
  static Future<bool> clearProgress({
    required String childId,
    String? storyId,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get existing progress data
      final String? progressJson = prefs.getString(_progressKey);
      if (progressJson == null) {
        return true;
      }

      final Map<String, dynamic> progressData =
          json.decode(progressJson) as Map<String, dynamic>;

      // Check if child has any progress
      if (!progressData.containsKey(childId)) {
        return true;
      }

      // If storyId is provided, clear progress for that story
      if (storyId != null) {
        if (progressData[childId].containsKey(storyId)) {
          progressData[childId].remove(storyId);
        }
      } else {
        // Otherwise, clear all progress for the child
        progressData.remove(childId);
      }

      // Save updated progress data
      await prefs.setString(_progressKey, json.encode(progressData));

      return true;
    } catch (e) {
      debugPrint('Error clearing story progress: $e');
      return false;
    }
  }

  /// Save the active child ID
  static Future<bool> saveActiveChildId(String childId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_activeChildKey, childId);
      return true;
    } catch (e) {
      debugPrint('Error saving active child ID: $e');
      return false;
    }
  }

  /// Load the active child ID
  static Future<String?> loadActiveChildId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_activeChildKey);
    } catch (e) {
      debugPrint('Error loading active child ID: $e');
      return null;
    }
  }

  // ========== CLOUD SYNC METHODS ==========

  /// Save child profile progress to Firestore
  static Future<bool> saveProgressToCloud(ChildProfile childProfile) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        debugPrint('No authenticated user for cloud sync');
        return false;
      }

      await _firestore
          .collection('children')
          .doc(childProfile.id)
          .update(childProfile.toFirestore());

      return true;
    } catch (e) {
      debugPrint('Error saving progress to cloud: $e');
      return false;
    }
  }

  /// Load child profile progress from Firestore
  static Future<ChildProfile?> loadProgressFromCloud(String childId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        debugPrint('No authenticated user for cloud sync');
        return null;
      }

      final doc = await _firestore.collection('children').doc(childId).get();

      if (doc.exists) {
        return ChildProfile.fromFirestore(doc);
      }

      return null;
    } catch (e) {
      debugPrint('Error loading progress from cloud: $e');
      return null;
    }
  }

  /// Sync offline progress to cloud with conflict resolution
  static Future<bool> syncOfflineProgressToCloud(ChildProfile childProfile) async {
    try {
      // Load local progress
      final localProgress = await loadProgress(childId: childProfile.id);
      if (localProgress == null) return true; // Nothing to sync

      // Load cloud progress
      final cloudProfile = await loadProgressFromCloud(childProfile.id);

      // Merge progress with conflict resolution
      ChildProfile updatedProfile = childProfile;

      if (cloudProfile != null) {
        updatedProfile = await _resolveConflicts(childProfile, cloudProfile);
      }

      // Save merged progress to cloud
      return await saveProgressToCloud(updatedProfile);
    } catch (e) {
      debugPrint('Error syncing offline progress to cloud: $e');
      return false;
    }
  }

  /// Resolve conflicts between local and cloud progress
  static Future<ChildProfile> _resolveConflicts(
    ChildProfile localProfile,
    ChildProfile cloudProfile,
  ) async {
    // Start with the profile that was last updated
    ChildProfile baseProfile = localProfile.lastActive.isAfter(cloudProfile.lastActive)
        ? localProfile
        : cloudProfile;

    // Merge story progress with intelligent conflict resolution
    final mergedProgress = <String, StoryProgress>{};

    // Get all unique story IDs from both profiles
    final allStoryIds = <String>{
      ...localProfile.storyProgress.keys,
      ...cloudProfile.storyProgress.keys,
    };

    for (final storyId in allStoryIds) {
      final localStoryProgress = localProfile.storyProgress[storyId];
      final cloudStoryProgress = cloudProfile.storyProgress[storyId];

      if (localStoryProgress == null) {
        // Only exists in cloud
        mergedProgress[storyId] = cloudStoryProgress!;
      } else if (cloudStoryProgress == null) {
        // Only exists locally
        mergedProgress[storyId] = localStoryProgress;
      } else {
        // Exists in both - resolve conflict
        mergedProgress[storyId] = _resolveStoryProgressConflict(
          localStoryProgress,
          cloudStoryProgress,
        );
      }
    }

    // Merge preferences (local takes precedence for newer data)
    final mergedPreferences = Map<String, dynamic>.from(cloudProfile.preferences);
    for (final entry in localProfile.preferences.entries) {
      mergedPreferences[entry.key] = entry.value;
    }

    // Use the most recent last active time
    final lastActive = localProfile.lastActive.isAfter(cloudProfile.lastActive)
        ? localProfile.lastActive
        : cloudProfile.lastActive;

    return baseProfile.copyWith(
      storyProgress: mergedProgress,
      preferences: mergedPreferences,
      lastActive: lastActive,
    );
  }

  /// Resolve conflicts for a specific story's progress
  static StoryProgress _resolveStoryProgressConflict(
    StoryProgress localProgress,
    StoryProgress cloudProgress,
  ) {
    // Use the progress with the most recent update
    if (localProgress.lastUpdated.isAfter(cloudProgress.lastUpdated)) {
      // Local is newer - merge some cloud data if beneficial
      return localProgress.copyWith(
        // Keep local progress but merge completed scenes
        completedScenes: _mergeCompletedScenes(
          localProgress.completedScenes,
          cloudProgress.completedScenes,
        ),
        // Add total time from both sources
        totalTimeMinutes: localProgress.totalTimeMinutes + cloudProgress.totalTimeMinutes,
      );
    } else if (cloudProgress.lastUpdated.isAfter(localProgress.lastUpdated)) {
      // Cloud is newer - merge some local data if beneficial
      return cloudProgress.copyWith(
        // Keep cloud progress but merge completed scenes
        completedScenes: _mergeCompletedScenes(
          cloudProgress.completedScenes,
          localProgress.completedScenes,
        ),
        // Add total time from both sources
        totalTimeMinutes: cloudProgress.totalTimeMinutes + localProgress.totalTimeMinutes,
      );
    } else {
      // Same timestamp - merge both
      final mergedChoices = Map<String, String>.from(cloudProgress.choicesMade);
      mergedChoices.addAll(localProgress.choicesMade);

      return StoryProgress(
        storyId: localProgress.storyId,
        currentSceneId: localProgress.currentSceneId, // Prefer local current scene
        completedScenes: _mergeCompletedScenes(
          localProgress.completedScenes,
          cloudProgress.completedScenes,
        ),
        choicesMade: mergedChoices,
        lastUpdated: DateTime.now(), // Update to current time
        isCompleted: localProgress.isCompleted || cloudProgress.isCompleted,
        totalTimeMinutes: localProgress.totalTimeMinutes + cloudProgress.totalTimeMinutes,
      );
    }
  }

  /// Merge completed scenes from two sources
  static List<String> _mergeCompletedScenes(
    List<String> scenes1,
    List<String> scenes2,
  ) {
    final merged = <String>{...scenes1, ...scenes2};
    return merged.toList();
  }

  /// Sync cloud progress to local storage
  static Future<bool> syncCloudProgressToLocal(String childId) async {
    try {
      final cloudProfile = await loadProgressFromCloud(childId);
      if (cloudProfile == null) return true; // Nothing to sync

      // Convert cloud progress to local format and save
      for (final entry in cloudProfile.storyProgress.entries) {
        final progress = entry.value;
        await saveProgress(
          childId: childId,
          storyId: progress.storyId,
          sceneId: progress.currentSceneId,
          choiceHistory: progress.choicesMade.values.toList(),
        );
      }

      return true;
    } catch (e) {
      debugPrint('Error syncing cloud progress to local: $e');
      return false;
    }
  }

  /// Migrate existing local progress to cloud format
  static Future<bool> migrateLocalProgressToCloud(ChildProfile childProfile) async {
    try {
      // Load all local progress for this child
      final localProgress = await loadProgress(childId: childProfile.id);
      if (localProgress == null) return true; // Nothing to migrate

      // Convert to new format
      final storyId = localProgress['storyId'] as String;
      final sceneId = localProgress['sceneId'] as String;
      final timestamp = localProgress['timestamp'] as int;
      final choiceHistory = localProgress['choiceHistory'] as List<String>? ?? [];

      // Create story progress
      final storyProgress = StoryProgress(
        storyId: storyId,
        currentSceneId: sceneId,
        completedScenes: [], // We don't have this data in old format
        choicesMade: {
          for (int i = 0; i < choiceHistory.length; i++)
            'choice_$i': choiceHistory[i]
        },
        lastUpdated: DateTime.fromMillisecondsSinceEpoch(timestamp),
        isCompleted: false, // We don't have this data in old format
        totalTimeMinutes: 0, // We don't have this data in old format
      );

      // Update child profile with migrated progress
      final updatedProfile = childProfile.copyWith(
        storyProgress: {storyId: storyProgress},
        lastActive: DateTime.fromMillisecondsSinceEpoch(timestamp),
      );

      // Save to cloud
      return await saveProgressToCloud(updatedProfile);
    } catch (e) {
      debugPrint('Error migrating local progress to cloud: $e');
      return false;
    }
  }

  /// Check if user is authenticated for cloud operations
  static bool get isCloudSyncAvailable => _auth.currentUser != null;

  /// Get last sync timestamp
  static Future<DateTime?> getLastSyncTimestamp(String childId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt('last_sync_$childId');
      return timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : null;
    } catch (e) {
      debugPrint('Error getting last sync timestamp: $e');
      return null;
    }
  }

  /// Set last sync timestamp
  static Future<bool> setLastSyncTimestamp(String childId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('last_sync_$childId', DateTime.now().millisecondsSinceEpoch);
      return true;
    } catch (e) {
      debugPrint('Error setting last sync timestamp: $e');
      return false;
    }
  }
}
