import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';

/// Service for handling In-App Purchases
class IAPService {
  static final IAPService _instance = IAPService._internal();
  factory IAPService() => _instance;
  IAPService._internal();

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;

  /// Available product IDs
  static const String premiumMonthlyId = 'premium_monthly';
  static const String premiumYearlyId = 'premium_yearly';
  static const String premiumLifetimeId = 'premium_lifetime';
  static const String storyPackId = 'story_pack_1';

  /// All product IDs
  static const Set<String> _productIds = {
    premiumMonthlyId,
    premiumYearlyId,
    premiumLifetimeId,
    storyPackId,
  };

  /// Available products
  List<ProductDetails> _products = [];

  /// Purchase stream controller
  final StreamController<PurchaseDetails> _purchaseController =
      StreamController<PurchaseDetails>.broadcast();

  /// Initialization status
  bool _isInitialized = false;

  /// Getters
  List<ProductDetails> get products => _products;
  Stream<PurchaseDetails> get purchaseStream => _purchaseController.stream;
  bool get isInitialized => _isInitialized;

  /// Initialize the IAP service
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Check if IAP is available
      final bool available = await _inAppPurchase.isAvailable();
      if (!available) {
        debugPrint('IAP not available on this device');
        return false;
      }

      // Set up purchase listener
      _subscription = _inAppPurchase.purchaseStream.listen(
        _handlePurchaseUpdate,
        onDone: () => _subscription.cancel(),
        onError: (error) => debugPrint('Purchase stream error: $error'),
      );

      // Load products
      await _loadProducts();

      _isInitialized = true;
      return true;
    } catch (e) {
      debugPrint('Error initializing IAP: $e');
      return false;
    }
  }

  /// Load available products
  Future<void> _loadProducts() async {
    try {
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(_productIds);

      if (response.error != null) {
        debugPrint('Error loading products: ${response.error}');
        return;
      }

      _products = response.productDetails;
      debugPrint('Loaded ${_products.length} products');
    } catch (e) {
      debugPrint('Error loading products: $e');
    }
  }

  /// Get product by ID
  ProductDetails? getProduct(String productId) {
    try {
      return _products.firstWhere((product) => product.id == productId);
    } catch (e) {
      return null;
    }
  }

  /// Purchase a product
  Future<bool> purchaseProduct(String productId) async {
    if (!_isInitialized) {
      debugPrint('IAP not initialized');
      return false;
    }

    final product = getProduct(productId);
    if (product == null) {
      debugPrint('Product not found: $productId');
      return false;
    }

    try {
      final PurchaseParam purchaseParam = PurchaseParam(productDetails: product);

      bool success;
      if (product.id == premiumMonthlyId || product.id == premiumYearlyId) {
        // Subscription purchase
        success = await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      } else {
        // One-time purchase
        success = await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      }

      return success;
    } catch (e) {
      debugPrint('Error purchasing product: $e');
      return false;
    }
  }

  /// Restore purchases
  Future<bool> restorePurchases() async {
    if (!_isInitialized) {
      debugPrint('IAP not initialized');
      return false;
    }

    try {
      await _inAppPurchase.restorePurchases();
      return true;
    } catch (e) {
      debugPrint('Error restoring purchases: $e');
      return false;
    }
  }

  /// Handle purchase updates
  void _handlePurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      _purchaseController.add(purchaseDetails);

      if (purchaseDetails.status == PurchaseStatus.pending) {
        // Handle pending purchase
        debugPrint('Purchase pending: ${purchaseDetails.productID}');
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        // Handle error
        debugPrint('Purchase error: ${purchaseDetails.error}');
      } else if (purchaseDetails.status == PurchaseStatus.purchased ||
                 purchaseDetails.status == PurchaseStatus.restored) {
        // Handle successful purchase/restore
        debugPrint('Purchase successful: ${purchaseDetails.productID}');
        _verifyPurchase(purchaseDetails);
      }

      // Complete the purchase
      if (purchaseDetails.pendingCompletePurchase) {
        _inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  /// Verify purchase (implement server-side verification in production)
  Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    try {
      // In production, send the purchase token to your server for verification
      // For now, we'll just validate locally with basic checks

      // Check if purchase details are valid
      if (purchaseDetails.productID.isEmpty) {
        debugPrint('Purchase product ID is empty');
        return false;
      }

      // Check purchase status
      if (purchaseDetails.status != PurchaseStatus.purchased &&
          purchaseDetails.status != PurchaseStatus.restored) {
        debugPrint('Purchase status is not purchased or restored: ${purchaseDetails.status}');
        return false;
      }

      // Platform-specific validation
      if (Platform.isAndroid) {
        final androidDetails = purchaseDetails as GooglePlayPurchaseDetails;
        final purchase = androidDetails.billingClientPurchase;

        // Verify purchase token exists
        if (purchase.purchaseToken.isEmpty) {
          debugPrint('Android purchase token is empty');
          return false;
        }

        return true;
      } else if (Platform.isIOS) {
        final iosDetails = purchaseDetails as AppStorePurchaseDetails;

        // Verify receipt data exists
        if (iosDetails.skPaymentTransaction.transactionIdentifier == null) {
          debugPrint('iOS transaction identifier is null');
          return false;
        }

        return true;
      }

      debugPrint('Unsupported platform for purchase verification');
      return false;
    } catch (e) {
      debugPrint('Error verifying purchase: $e');
      return false;
    }
  }

  /// Enhanced error handling for purchase failures
  String _getPurchaseErrorMessage(PurchaseDetails purchaseDetails) {
    if (purchaseDetails.error == null) return 'Unknown purchase error';

    final error = purchaseDetails.error!;

    switch (error.code) {
      case 'user_canceled':
        return 'Purchase was canceled';
      case 'payment_invalid':
        return 'Payment method is invalid';
      case 'payment_not_allowed':
        return 'Payment not allowed on this device';
      case 'billing_unavailable':
        return 'Billing service is unavailable';
      case 'item_unavailable':
        return 'This item is not available for purchase';
      case 'developer_error':
        return 'There was a configuration error. Please try again later.';
      case 'item_already_owned':
        return 'You already own this item';
      case 'item_not_owned':
        return 'You do not own this item';
      case 'network_error':
        return 'Network error. Please check your connection and try again.';
      default:
        return error.message ?? 'Purchase failed. Please try again.';
    }
  }

  /// Retry mechanism for failed operations
  Future<T?> _retryOperation<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 2),
  }) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (e) {
        debugPrint('Operation failed (attempt $attempt/$maxRetries): $e');

        if (attempt == maxRetries) {
          rethrow;
        }

        await Future.delayed(delay * attempt); // Exponential backoff
      }
    }
    return null;
  }

  /// Check if premium is unlocked
  Future<bool> isPremiumUnlocked() async {
    if (!_isInitialized) return false;

    try {
      // For now, we'll use a simple local check
      // In production, you would query the platform's purchase history
      // This is a simplified implementation for demo purposes

      // You can implement platform-specific purchase queries here
      // For Android: Use BillingClient.queryPurchasesAsync()
      // For iOS: Use SKPaymentQueue.default().transactions

      debugPrint('Checking premium status (simplified implementation)');
      return false; // Default to false for demo
    } catch (e) {
      debugPrint('Error checking premium status: $e');
      return false;
    }
  }

  /// Check if specific content is unlocked
  Future<bool> isContentUnlocked(String contentId) async {
    if (!_isInitialized) return false;

    // Check if premium is unlocked (unlocks all content)
    if (await isPremiumUnlocked()) return true;

    try {
      // For now, we'll use a simple local check
      // In production, you would query the platform's purchase history
      debugPrint('Checking content unlock status for: $contentId (simplified implementation)');
      return false; // Default to false for demo
    } catch (e) {
      debugPrint('Error checking content unlock status: $e');
      return false;
    }
  }

  /// Get premium products
  List<ProductDetails> get premiumProducts {
    return _products.where((product) =>
      product.id == premiumMonthlyId ||
      product.id == premiumYearlyId ||
      product.id == premiumLifetimeId
    ).toList();
  }

  /// Get content pack products
  List<ProductDetails> get contentPackProducts {
    return _products.where((product) =>
      product.id == storyPackId
    ).toList();
  }

  /// Dispose resources
  void dispose() {
    _subscription.cancel();
    _purchaseController.close();
    _isInitialized = false;
  }
}
