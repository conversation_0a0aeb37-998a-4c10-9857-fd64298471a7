import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/parent_auth_provider.dart';
import '../../../shared_kernel/theme/app_theme.dart';
import 'parent_auth_screen.dart';

/// Onboarding screen that presents authentication options
class OnboardingScreen extends StatefulWidget {
  /// Constructor
  const OnboardingScreen({super.key});

  /// Route name for navigation
  static const routeName = '/onboarding';

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  bool _isLoading = false;

  /// Handle guest login
  Future<void> _signInAsGuest() async {
    setState(() => _isLoading = true);
    
    try {
      final authProvider = Provider.of<ParentAuthProvider>(context, listen: false);
      final success = await authProvider.signInAsGuest();
      
      if (success && mounted) {
        // Navigation will be handled by AuthWrapper
        Navigator.of(context).pop();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to continue as guest. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Navigate to sign up screen
  void _navigateToSignUp() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ParentAuthScreen(),
      ),
    );
  }

  /// Navigate to sign in screen
  void _navigateToSignIn() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ParentAuthScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryColor.withOpacity(0.1),
              AppTheme.secondaryColor.withOpacity(0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 400),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // App logo and title
                      Icon(
                        Icons.auto_stories,
                        size: 120,
                        color: AppTheme.primaryColor,
                      ),
                      const SizedBox(height: 24),
                      
                      Text(
                        'Choice: Once Upon A Time',
                        style: AppTheme.headingStyle.copyWith(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      
                      Text(
                        'Interactive stories that teach valuable life lessons',
                        style: AppTheme.bodyStyle.copyWith(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 48),

                      // Guest access button
                      _buildAuthButton(
                        onPressed: _isLoading ? null : _signInAsGuest,
                        icon: Icons.person_outline,
                        title: 'Continue as Guest',
                        subtitle: 'Access free stories without an account',
                        isPrimary: true,
                        isLoading: _isLoading,
                      ),
                      const SizedBox(height: 16),

                      // Create account button
                      _buildAuthButton(
                        onPressed: _isLoading ? null : _navigateToSignUp,
                        icon: Icons.person_add,
                        title: 'Create Account',
                        subtitle: 'Full access + cloud sync + premium features',
                        isPrimary: false,
                      ),
                      const SizedBox(height: 16),

                      // Sign in button
                      _buildAuthButton(
                        onPressed: _isLoading ? null : _navigateToSignIn,
                        icon: Icons.login,
                        title: 'Sign In',
                        subtitle: 'Already have an account?',
                        isPrimary: false,
                      ),
                      const SizedBox(height: 32),

                      // Features preview
                      _buildFeaturesList(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAuthButton({
    required VoidCallback? onPressed,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isPrimary,
    bool isLoading = false,
  }) {
    return Card(
      elevation: isPrimary ? 4 : 2,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isPrimary 
                    ? AppTheme.primaryColor 
                    : AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: isPrimary ? Colors.white : AppTheme.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTheme.bodyStyle.copyWith(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: AppTheme.bodyStyle.copyWith(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              if (isLoading)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[400],
                  size: 16,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturesList() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'What you get:',
              style: AppTheme.bodyStyle.copyWith(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            _buildFeatureItem(
              icon: Icons.book_outlined,
              text: 'Interactive storytelling with choices',
            ),
            _buildFeatureItem(
              icon: Icons.volume_up_outlined,
              text: 'Professional narration and TTS',
            ),
            _buildFeatureItem(
              icon: Icons.child_care_outlined,
              text: 'Age-appropriate moral lessons',
            ),
            _buildFeatureItem(
              icon: Icons.devices_outlined,
              text: 'Works on phones, tablets, and computers',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String text,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: AppTheme.primaryColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: AppTheme.bodyStyle.copyWith(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}
