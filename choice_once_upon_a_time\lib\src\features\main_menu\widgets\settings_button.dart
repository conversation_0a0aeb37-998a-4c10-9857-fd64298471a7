import 'package:flutter/material.dart';
import '../../../shared_kernel/theme/app_theme.dart';
import 'dart:math' as math;

/// Widget for the settings button in the top right corner
class SettingsButton extends StatelessWidget {
  /// The action to perform when tapped
  final VoidCallback onTap;

  /// Constructor
  const SettingsButton({
    super.key,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions for responsive sizing
    final screenSize = MediaQuery.of(context).size;
    final minDimension = math.min(screenSize.width, screenSize.height);

    // Calculate responsive sizes
    final double iconSize = math.max(24, minDimension * 0.04); // Min 24, max 4% of screen
    final double padding = math.max(8, minDimension * 0.015); // Min 8, max 1.5% of screen

    return Padding(
      padding: EdgeInsets.all(padding),
      child: Material(
        color: Colors.transparent,
        shape: const CircleBorder(),
        clipBehavior: Clip.antiAlias,
        child: IconButton(
          icon: Icon(
            Icons.settings,
            color: AppTheme.primaryColor,
            size: iconSize,
          ),
          onPressed: onTap,
          tooltip: 'Settings',
          padding: EdgeInsets.all(padding),
        ),
      ),
    );
  }
}
