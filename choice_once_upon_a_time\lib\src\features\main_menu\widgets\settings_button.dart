import 'package:flutter/material.dart';
import '../../../shared_kernel/theme/app_theme.dart';

/// Widget for the settings button in the top right corner
class SettingsButton extends StatelessWidget {
  /// The action to perform when tapped
  final VoidCallback onTap;
  
  /// Constructor
  const SettingsButton({
    super.key,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Material(
        color: Colors.transparent,
        shape: const CircleBorder(),
        clipBehavior: Clip.antiAlias,
        child: IconButton(
          icon: const Icon(
            Icons.settings,
            color: AppTheme.primaryColor,
            size: 32,
          ),
          onPressed: onTap,
          tooltip: 'Settings',
          padding: const EdgeInsets.all(12),
        ),
      ),
    );
  }
}
