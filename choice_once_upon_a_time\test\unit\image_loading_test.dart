import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

@GenerateMocks([AssetBundle])
void main() {
  group('Image Loading Tests', () {
    late MockAssetBundle mockAssetBundle;

    setUp(() {
      mockAssetBundle = MockAssetBundle();
    });

    test('should load image with explicit extension', () async {
      // Setup mock to return data for a specific path
      when(mockAssetBundle.load('assets/images/test/image.png'))
          .thenAnswer((_) async => ByteData(0));

      // Attempt to load the image
      final result = await _findValidImagePath(
        'assets/images/test/image.png',
        ['', '.png', '.jpg', '.jpeg'],
        mockAssetBundle,
      );

      // Verify the correct path was returned
      expect(result, equals('assets/images/test/image.png'));
    });

    test('should try different extensions when no extension is provided', () async {
      // Setup mock to fail for some paths but succeed for one
      when(mockAssetBundle.load('assets/images/test/image'))
          .thenThrow(Exception('Asset not found'));
      when(mockAssetBundle.load('assets/images/test/image.png'))
          .thenThrow(Exception('Asset not found'));
      when(mockAssetBundle.load('assets/images/test/image.jpg'))
          .thenAnswer((_) async => ByteData(0));
      when(mockAssetBundle.load('assets/images/test/image.jpeg'))
          .thenThrow(Exception('Asset not found'));

      // Attempt to load the image
      final result = await _findValidImagePath(
        'assets/images/test/image',
        ['', '.png', '.jpg', '.jpeg'],
        mockAssetBundle,
      );

      // Verify the correct path was returned
      expect(result, equals('assets/images/test/image.jpg'));
    });

    test('should return null when no valid path is found', () async {
      // Setup mock to fail for all paths
      when(mockAssetBundle.load(any)).thenThrow(Exception('Asset not found'));

      // Attempt to load the image
      final result = await _findValidImagePath(
        'assets/images/test/nonexistent',
        ['', '.png', '.jpg', '.jpeg'],
        mockAssetBundle,
      );

      // Verify null was returned
      expect(result, isNull);
    });

    test('should check if path already has valid extension', () async {
      // Setup mock to return data for a specific path
      when(mockAssetBundle.load('assets/images/test/image.jpg'))
          .thenAnswer((_) async => ByteData(0));

      // Attempt to load the image
      final result = await _findValidImagePath(
        'assets/images/test/image.jpg',
        ['', '.png', '.jpg', '.jpeg'],
        mockAssetBundle,
      );

      // Verify the correct path was returned
      expect(result, equals('assets/images/test/image.jpg'));

      // Verify that we didn't try to load with additional extensions
      verifyNever(mockAssetBundle.load('assets/images/test/image.jpg.png'));
      verifyNever(mockAssetBundle.load('assets/images/test/image.jpg.jpg'));
    });
  });
}

/// Find a valid image path by trying different extensions
/// This is a simplified version of the method in StoryInteractionScreen
Future<String?> _findValidImagePath(
  String basePath,
  List<String> extensions,
  AssetBundle assetBundle,
) async {
  // First, check if the path already has a valid extension
  final String basePathLower = basePath.toLowerCase();
  if (basePathLower.endsWith('.png') ||
      basePathLower.endsWith('.jpg') ||
      basePathLower.endsWith('.jpeg') ||
      basePathLower.endsWith('.webp')) {
    try {
      await assetBundle.load(basePath);
      return basePath;
    } catch (e) {
      // Continue to try other extensions
    }
  }

  // Try each extension
  for (final extension in extensions) {
    final path = extension.isEmpty ? basePath : '$basePath$extension';
    try {
      // Check if the asset exists
      await assetBundle.load(path);
      return path;
    } catch (e) {
      // Asset not found with this extension, try the next one
      continue;
    }
  }

  // No valid path found
  return null;
}
