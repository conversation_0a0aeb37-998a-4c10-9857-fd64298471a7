import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/src/features/main_menu/widgets/action_button.dart';
import 'package:choice_once_upon_a_time/src/shared_kernel/theme/app_theme.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('ActionButton', () {
    testWidgets('should render correctly with required props', (WidgetTester tester) async {
      bool tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.primaryColor),
          ),
          home: Scaffold(
            body: Center(
              child: ActionButton(
                icon: Icons.play_arrow,
                text: 'Test Button',
                onTap: () {
                  tapped = true;
                },
              ),
            ),
          ),
        ),
      );

      // Verify the button is rendered
      expect(find.byType(ActionButton), findsOneWidget);

      // Verify the icon is rendered
      expect(find.byIcon(Icons.play_arrow), findsOneWidget);

      // Verify the text is rendered
      expect(find.text('Test Button'), findsOneWidget);

      // Tap the button
      await tester.tap(find.byType(ActionButton));
      await tester.pump();

      // Verify the onTap callback was called
      expect(tapped, isTrue);
    });

    testWidgets('should be disabled when isEnabled is false', (WidgetTester tester) async {
      bool tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.primaryColor),
          ),
          home: Scaffold(
            body: Center(
              child: ActionButton(
                icon: Icons.play_arrow,
                text: 'Disabled Button',
                onTap: () {
                  tapped = true;
                },
                isEnabled: false,
              ),
            ),
          ),
        ),
      );

      // Verify the button is rendered
      expect(find.byType(ActionButton), findsOneWidget);

      // Tap the button
      await tester.tap(find.byType(ActionButton));
      await tester.pump();

      // Verify the onTap callback was NOT called
      expect(tapped, isFalse);

      // Verify the button has a disabled appearance
      final Card card = tester.widget<Card>(find.byType(Card));
      expect(card.color, equals(Colors.grey[300]));
    });

    testWidgets('should be responsive to screen size', (WidgetTester tester) async {
      await TestHelpers.testOnAllScreenSizes(
        tester,
        (screenSize) => TestHelpers.createResponsiveTestWidget(
          child: ActionButton(
            icon: Icons.play_arrow,
            text: 'Responsive Button',
            onTap: () {},
          ),
          screenSize: screenSize,
        ),
        (tester, screenSize) async {
          await tester.pump();

          // Should display without overflow
          expect(find.byIcon(Icons.play_arrow), findsOneWidget);
          expect(find.text('Responsive Button'), findsOneWidget);
          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('should handle long text gracefully', (tester) async {
      const longText = 'This is a very long button text that might cause overflow';

      await tester.pumpWidget(
        TestHelpers.createResponsiveTestWidget(
          child: ActionButton(
            icon: Icons.book,
            text: longText,
            onTap: () {},
          ),
          screenSize: const Size(360, 640), // Small screen
        ),
      );

      expect(find.text(longText), findsOneWidget);
      TestHelpers.verifyNoOverflow(tester);
    });

    testWidgets('should meet accessibility guidelines', (tester) async {
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: ActionButton(
            icon: Icons.book,
            text: 'Accessible Button',
            onTap: () {},
          ),
        ),
      );

      await TestHelpers.verifyAccessibility(tester);
    });
  });
}
