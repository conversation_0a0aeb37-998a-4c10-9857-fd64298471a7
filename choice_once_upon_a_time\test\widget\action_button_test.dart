import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/src/features/main_menu/widgets/action_button.dart';
import 'package:choice_once_upon_a_time/src/shared_kernel/theme/app_theme.dart';

void main() {
  group('ActionButton', () {
    testWidgets('should render correctly with required props', (WidgetTester tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.primaryColor),
          ),
          home: Scaffold(
            body: Center(
              child: ActionButton(
                icon: Icons.play_arrow,
                text: 'Test Button',
                onTap: () {
                  tapped = true;
                },
              ),
            ),
          ),
        ),
      );
      
      // Verify the button is rendered
      expect(find.byType(ActionButton), findsOneWidget);
      
      // Verify the icon is rendered
      expect(find.byIcon(Icons.play_arrow), findsOneWidget);
      
      // Verify the text is rendered
      expect(find.text('Test Button'), findsOneWidget);
      
      // Tap the button
      await tester.tap(find.byType(ActionButton));
      await tester.pump();
      
      // Verify the onTap callback was called
      expect(tapped, isTrue);
    });
    
    testWidgets('should be disabled when isEnabled is false', (WidgetTester tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.primaryColor),
          ),
          home: Scaffold(
            body: Center(
              child: ActionButton(
                icon: Icons.play_arrow,
                text: 'Disabled Button',
                onTap: () {
                  tapped = true;
                },
                isEnabled: false,
              ),
            ),
          ),
        ),
      );
      
      // Verify the button is rendered
      expect(find.byType(ActionButton), findsOneWidget);
      
      // Tap the button
      await tester.tap(find.byType(ActionButton));
      await tester.pump();
      
      // Verify the onTap callback was NOT called
      expect(tapped, isFalse);
      
      // Verify the button has a disabled appearance
      final Card card = tester.widget<Card>(find.byType(Card));
      expect(card.color, equals(Colors.grey[300]));
    });
    
    testWidgets('should be responsive to screen size', (WidgetTester tester) async {
      // Set up a small screen size
      tester.binding.window.physicalSizeTestValue = const Size(320, 480);
      tester.binding.window.devicePixelRatioTestValue = 1.0;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.primaryColor),
          ),
          home: Scaffold(
            body: Center(
              child: ActionButton(
                icon: Icons.play_arrow,
                text: 'Responsive Button',
                onTap: () {},
              ),
            ),
          ),
        ),
      );
      
      // Get the size of the button on a small screen
      final smallScreenSize = tester.getSize(find.byType(Card));
      
      // Reset the screen size
      tester.binding.window.clearPhysicalSizeTestValue();
      tester.binding.window.clearDevicePixelRatioTestValue();
      
      // Set up a large screen size
      tester.binding.window.physicalSizeTestValue = const Size(1920, 1080);
      tester.binding.window.devicePixelRatioTestValue = 1.0;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.primaryColor),
          ),
          home: Scaffold(
            body: Center(
              child: ActionButton(
                icon: Icons.play_arrow,
                text: 'Responsive Button',
                onTap: () {},
              ),
            ),
          ),
        ),
      );
      
      // Get the size of the button on a large screen
      final largeScreenSize = tester.getSize(find.byType(Card));
      
      // Reset the screen size
      tester.binding.window.clearPhysicalSizeTestValue();
      tester.binding.window.clearDevicePixelRatioTestValue();
      
      // Verify that the button size changes with screen size
      expect(largeScreenSize.width, greaterThan(smallScreenSize.width));
      expect(largeScreenSize.height, greaterThan(smallScreenSize.height));
    });
  });
}
