import 'package:cloud_firestore/cloud_firestore.dart';

/// Model representing a parent profile
class ParentProfile {
  /// Unique identifier for the parent
  final String id;

  /// <PERSON><PERSON>'s full name
  final String name;

  /// <PERSON><PERSON>'s email address
  final String email;

  /// Parent's preferred language
  final String preferredLanguage;

  /// Whether to receive email notifications
  final bool emailNotifications;

  /// Whether to receive push notifications
  final bool pushNotifications;

  /// Preferred app theme (light, dark, auto)
  final String preferredTheme;

  /// Whether background music is enabled
  final bool backgroundMusicEnabled;

  /// Background music volume (0.0 to 1.0)
  final double backgroundMusicVolume;

  /// Whether sound effects are enabled
  final bool soundEffectsEnabled;

  /// Sound effects volume (0.0 to 1.0)
  final double soundEffectsVolume;

  /// Whether TTS narration is enabled by default
  final bool narrationEnabled;

  /// Default narration speed (0.3 to 1.5)
  final double narrationSpeed;

  /// Whether to use natural pauses in narration
  final bool naturalPauses;

  /// Maximum screen time per session (in minutes)
  final int maxScreenTimeMinutes;

  /// Whether to show reading progress to children
  final bool showProgressToChildren;

  /// Date when the profile was created
  final DateTime createdAt;

  /// Date when the profile was last updated
  final DateTime updatedAt;

  /// Constructor
  const ParentProfile({
    required this.id,
    required this.name,
    required this.email,
    this.preferredLanguage = 'en',
    this.emailNotifications = true,
    this.pushNotifications = true,
    this.preferredTheme = 'auto',
    this.backgroundMusicEnabled = true,
    this.backgroundMusicVolume = 0.5,
    this.soundEffectsEnabled = true,
    this.soundEffectsVolume = 0.7,
    this.narrationEnabled = true,
    this.narrationSpeed = 0.5,
    this.naturalPauses = true,
    this.maxScreenTimeMinutes = 30,
    this.showProgressToChildren = true,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create a copy of this profile with updated values
  ParentProfile copyWith({
    String? id,
    String? name,
    String? email,
    String? preferredLanguage,
    bool? emailNotifications,
    bool? pushNotifications,
    String? preferredTheme,
    bool? backgroundMusicEnabled,
    double? backgroundMusicVolume,
    bool? soundEffectsEnabled,
    double? soundEffectsVolume,
    bool? narrationEnabled,
    double? narrationSpeed,
    bool? naturalPauses,
    int? maxScreenTimeMinutes,
    bool? showProgressToChildren,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ParentProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      pushNotifications: pushNotifications ?? this.pushNotifications,
      preferredTheme: preferredTheme ?? this.preferredTheme,
      backgroundMusicEnabled: backgroundMusicEnabled ?? this.backgroundMusicEnabled,
      backgroundMusicVolume: backgroundMusicVolume ?? this.backgroundMusicVolume,
      soundEffectsEnabled: soundEffectsEnabled ?? this.soundEffectsEnabled,
      soundEffectsVolume: soundEffectsVolume ?? this.soundEffectsVolume,
      narrationEnabled: narrationEnabled ?? this.narrationEnabled,
      narrationSpeed: narrationSpeed ?? this.narrationSpeed,
      naturalPauses: naturalPauses ?? this.naturalPauses,
      maxScreenTimeMinutes: maxScreenTimeMinutes ?? this.maxScreenTimeMinutes,
      showProgressToChildren: showProgressToChildren ?? this.showProgressToChildren,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'preferredLanguage': preferredLanguage,
      'emailNotifications': emailNotifications,
      'pushNotifications': pushNotifications,
      'preferredTheme': preferredTheme,
      'backgroundMusicEnabled': backgroundMusicEnabled,
      'backgroundMusicVolume': backgroundMusicVolume,
      'soundEffectsEnabled': soundEffectsEnabled,
      'soundEffectsVolume': soundEffectsVolume,
      'narrationEnabled': narrationEnabled,
      'narrationSpeed': narrationSpeed,
      'naturalPauses': naturalPauses,
      'maxScreenTimeMinutes': maxScreenTimeMinutes,
      'showProgressToChildren': showProgressToChildren,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create from JSON
  factory ParentProfile.fromJson(Map<String, dynamic> json) {
    return ParentProfile(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      preferredLanguage: json['preferredLanguage'] as String? ?? 'en',
      emailNotifications: json['emailNotifications'] as bool? ?? true,
      pushNotifications: json['pushNotifications'] as bool? ?? true,
      preferredTheme: json['preferredTheme'] as String? ?? 'auto',
      backgroundMusicEnabled: json['backgroundMusicEnabled'] as bool? ?? true,
      backgroundMusicVolume: (json['backgroundMusicVolume'] as num?)?.toDouble() ?? 0.5,
      soundEffectsEnabled: json['soundEffectsEnabled'] as bool? ?? true,
      soundEffectsVolume: (json['soundEffectsVolume'] as num?)?.toDouble() ?? 0.7,
      narrationEnabled: json['narrationEnabled'] as bool? ?? true,
      narrationSpeed: (json['narrationSpeed'] as num?)?.toDouble() ?? 0.5,
      naturalPauses: json['naturalPauses'] as bool? ?? true,
      maxScreenTimeMinutes: json['maxScreenTimeMinutes'] as int? ?? 30,
      showProgressToChildren: json['showProgressToChildren'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Create from Firestore document
  factory ParentProfile.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ParentProfile.fromJson({
      'id': doc.id,
      ...data,
    });
  }

  /// Convert to Firestore document data
  Map<String, dynamic> toFirestore() {
    final json = toJson();
    json.remove('id'); // Firestore document ID is separate
    return json;
  }

  /// Create a default parent profile
  factory ParentProfile.createDefault({
    required String id,
    required String name,
    required String email,
  }) {
    final now = DateTime.now();
    return ParentProfile(
      id: id,
      name: name,
      email: email,
      createdAt: now,
      updatedAt: now,
    );
  }

  @override
  String toString() {
    return 'ParentProfile(id: $id, name: $name, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ParentProfile && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
