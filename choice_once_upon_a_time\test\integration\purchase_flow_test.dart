import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../lib/main.dart' as app;
import '../helpers/test_helpers.dart';
import '../mocks/mock_services.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Purchase Flow Integration Tests', () {
    late MockFirebaseAuthService mockAuthService;
    late MockIAPService mockIAPService;

    setUpAll(() {
      mockAuthService = MockFirebaseAuthService();
      mockIAPService = MockIAPService();
    });

    setUp(() {
      // Set up authenticated state
      mockAuthService.setAuthState(
        isSignedIn: true,
        userEmail: '<EMAIL>',
        userId: 'test-user-id',
      );

      // Set up IAP service
      mockIAPService.setIAPState(
        isInitialized: true,
        isPremiumUnlocked: false,
        products: [
          TestDataFactory.createProductDetails(
            id: 'premium_monthly',
            title: 'Premium Monthly',
            price: '\$4.99',
          ),
          TestDataFactory.createProductDetails(
            id: 'premium_yearly',
            title: 'Premium Yearly',
            price: '\$39.99',
          ),
        ],
      );

      MockFirestore.clear();
      MockFirestore.setDocument(
        'users/test-user-id',
        TestDataFactory.createUserData(
          children: [TestDataFactory.createChildData(name: 'Test Child')],
        ),
      );
    });

    testWidgets('Complete premium purchase flow', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Complete premium purchase flow',
        () async {
          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Should be at main menu (authenticated)
          expect(find.text('New Story'), findsOneWidget);

          // Navigate to story library
          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          // Find a locked story
          expect(find.byIcon(Icons.lock), findsWidgets);

          // Tap on a locked story
          await tester.tap(find.byIcon(Icons.lock).first);
          await tester.pumpAndSettle();

          // Should navigate to premium screen
          expect(find.text('Unlock Premium'), findsOneWidget);
          expect(find.text('Premium Monthly'), findsOneWidget);
          expect(find.text('Premium Yearly'), findsOneWidget);

          // Select monthly plan
          await tester.tap(find.text('Premium Monthly'));
          await tester.pumpAndSettle();

          // Purchase button should be enabled
          expect(find.text('Subscribe Now'), findsOneWidget);

          // Tap purchase button
          await tester.tap(find.text('Subscribe Now'));
          await tester.pumpAndSettle();

          // Should show loading state
          expect(find.byType(CircularProgressIndicator), findsOneWidget);

          // Wait for purchase to complete
          await tester.pumpAndSettle();

          // Should show success message
          expect(find.textContaining('Premium unlocked'), findsOneWidget);

          // Navigate back to story library
          await tester.tap(find.byIcon(Icons.arrow_back));
          await tester.pumpAndSettle();

          // Stories should now be unlocked
          expect(find.byIcon(Icons.lock), findsNothing);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Purchase cancellation flow', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Purchase cancellation flow',
        () async {
          // Set up IAP service to simulate user cancellation
          mockIAPService.setIAPState(
            isInitialized: true,
            isPremiumUnlocked: false,
            errorMessage: 'user_canceled',
            products: [
              TestDataFactory.createProductDetails(id: 'premium_monthly'),
            ],
          );

          // Launch app and navigate to premium screen
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.byIcon(Icons.lock).first);
          await tester.pumpAndSettle();

          // Select plan and attempt purchase
          await tester.tap(find.text('Premium Monthly'));
          await tester.pumpAndSettle();

          await tester.tap(find.text('Subscribe Now'));
          await tester.pumpAndSettle();

          // Should show cancellation message
          expect(find.textContaining('canceled'), findsOneWidget);

          // Should still be on premium screen
          expect(find.text('Unlock Premium'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Purchase error handling', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Purchase error handling',
        () async {
          // Set up IAP service to simulate error
          mockIAPService.setIAPState(
            isInitialized: true,
            isPremiumUnlocked: false,
            errorMessage: 'payment_invalid',
            products: [
              TestDataFactory.createProductDetails(id: 'premium_monthly'),
            ],
          );

          // Launch app and navigate to premium screen
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.byIcon(Icons.lock).first);
          await tester.pumpAndSettle();

          // Attempt purchase
          await tester.tap(find.text('Premium Monthly'));
          await tester.pumpAndSettle();

          await tester.tap(find.text('Subscribe Now'));
          await tester.pumpAndSettle();

          // Should show error message
          expect(find.textContaining('Payment method is invalid'), findsOneWidget);

          // Should provide retry option
          expect(find.text('Try Again'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Restore purchases flow', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Restore purchases flow',
        () async {
          // Launch app and navigate to premium screen
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.byIcon(Icons.lock).first);
          await tester.pumpAndSettle();

          // Tap restore purchases
          await tester.tap(find.text('Restore Purchases'));
          await tester.pumpAndSettle();

          // Should show loading state
          expect(find.byType(CircularProgressIndicator), findsOneWidget);

          // Wait for restore to complete
          await tester.pumpAndSettle();

          // Should show success message
          expect(find.textContaining('restored'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Premium content access after purchase', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Premium content access after purchase',
        () async {
          // Set up as premium user
          mockIAPService.setIAPState(
            isInitialized: true,
            isPremiumUnlocked: true,
            products: [],
          );

          // Launch app
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          // Navigate to story library
          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          // Should not see any locked stories
          expect(find.byIcon(Icons.lock), findsNothing);

          // Should see premium badge on stories
          expect(find.text('PREMIUM'), findsWidgets);

          // Should be able to access premium stories
          await tester.tap(find.text('PREMIUM').first);
          await tester.pumpAndSettle();

          // Should navigate to story interaction
          expect(find.byIcon(Icons.play_arrow), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Billing unavailable handling', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Billing unavailable handling',
        () async {
          // Set up IAP service as unavailable
          mockIAPService.setIAPState(
            isInitialized: false,
            isPremiumUnlocked: false,
            errorMessage: 'billing_unavailable',
            products: [],
          );

          // Launch app and navigate to premium screen
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.byIcon(Icons.lock).first);
          await tester.pumpAndSettle();

          // Should show billing unavailable message
          expect(find.textContaining('Billing service is unavailable'), findsOneWidget);

          // Should provide alternative options
          expect(find.text('Try Again Later'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Network error during purchase', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Network error during purchase',
        () async {
          // Set up network error
          mockIAPService.setIAPState(
            isInitialized: true,
            isPremiumUnlocked: false,
            errorMessage: 'network_error',
            products: [
              TestDataFactory.createProductDetails(id: 'premium_monthly'),
            ],
          );

          // Launch app and navigate to premium screen
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.byIcon(Icons.lock).first);
          await tester.pumpAndSettle();

          // Attempt purchase
          await tester.tap(find.text('Premium Monthly'));
          await tester.pumpAndSettle();

          await tester.tap(find.text('Subscribe Now'));
          await tester.pumpAndSettle();

          // Should show network error message
          expect(find.textContaining('Network error'), findsOneWidget);

          // Should provide retry option
          expect(find.text('Retry'), findsOneWidget);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Premium screen responsive design', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Premium screen responsive design',
        () async {
          for (final screenSize in TestHelpers.allScreenSizes) {
            await tester.binding.setSurfaceSize(screenSize);
            await tester.pumpAndSettle();

            // Launch app and navigate to premium screen
            await tester.pumpWidget(app.MyApp());
            await tester.pumpAndSettle();

            await tester.tap(find.text('New Story'));
            await tester.pumpAndSettle();

            await tester.tap(find.byIcon(Icons.lock).first);
            await tester.pumpAndSettle();

            // Should display properly without overflow
            expect(find.text('Unlock Premium'), findsOneWidget);
            TestHelpers.verifyNoOverflow(tester);
          }

          // Reset to default size
          await tester.binding.setSurfaceSize(null);
        },
      );
    });

    testWidgets('Premium benefits display', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Premium benefits display',
        () async {
          // Launch app and navigate to premium screen
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.byIcon(Icons.lock).first);
          await tester.pumpAndSettle();

          // Should show premium benefits
          expect(find.text('Premium Benefits'), findsOneWidget);
          expect(find.text('Access to all premium stories'), findsOneWidget);
          expect(find.text('New content added monthly'), findsOneWidget);
          expect(find.text('Ad-free experience'), findsOneWidget);
          expect(find.text('Offline downloads'), findsOneWidget);
          expect(find.text('Advanced parental controls'), findsOneWidget);

          // Should show benefit icons
          expect(find.byIcon(Icons.check_circle), findsWidgets);

          TestHelpers.verifyNoOverflow(tester);
        },
      );
    });

    testWidgets('Purchase flow accessibility', (tester) async {
      await TestHelpers.runTestWithErrorHandling(
        'Purchase flow accessibility',
        () async {
          // Launch app and navigate to premium screen
          await tester.pumpWidget(app.MyApp());
          await tester.pumpAndSettle();

          await tester.tap(find.text('New Story'));
          await tester.pumpAndSettle();

          await tester.tap(find.byIcon(Icons.lock).first);
          await tester.pumpAndSettle();

          // Test accessibility of premium screen
          await TestHelpers.verifyAccessibility(tester);

          // Test accessibility of product selection
          await tester.tap(find.text('Premium Monthly'));
          await tester.pumpAndSettle();

          await TestHelpers.verifyAccessibility(tester);
        },
      );
    });
  });
}
