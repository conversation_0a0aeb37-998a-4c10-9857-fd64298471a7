﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\firebase_storage_plugin_c_api.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\firebase_storage_plugin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\messages.g.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include\firebase_storage\firebase_storage_plugin_c_api.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\firebase_storage_plugin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\messages.g.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\generated\firebase_storage\plugin_version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{6EBADC12-2F3F-324D-B9DA-6C4FAE78EE8F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{41C86688-83D2-36B4-A927-EE1410F99E56}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
