{"buildFiles": ["D:\\Install\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\OneDrive\\Desktop\\VS Code\\choice\\choice_once_upon_a_time\\android\\app\\.cxx\\Debug\\3x3y5p2o\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\OneDrive\\Desktop\\VS Code\\choice\\choice_once_upon_a_time\\android\\app\\.cxx\\Debug\\3x3y5p2o\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}