import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/child_profile.dart';
import '../../story/services/story_progress_service.dart';
import '../../auth/providers/parent_auth_provider.dart';

/// Service for migrating existing local data to cloud storage
class MigrationService {
  static const String _migrationVersionKey = 'migration_version';
  static const String _hasMigratedKey = 'has_migrated_to_cloud';
  static const int _currentMigrationVersion = 1;

  /// Check if migration is needed
  static Future<bool> isMigrationNeeded() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final migrationVersion = prefs.getInt(_migrationVersionKey) ?? 0;
      final hasMigrated = prefs.getBool(_hasMigratedKey) ?? false;
      
      return migrationVersion < _currentMigrationVersion || !hasMigrated;
    } catch (e) {
      debugPrint('Error checking migration status: $e');
      return false;
    }
  }

  /// Perform migration of local data to cloud
  static Future<MigrationResult> migrateToCloud({
    required ParentAuthProvider authProvider,
    required ChildProfile localChildProfile,
  }) async {
    try {
      debugPrint('Starting migration to cloud for child: ${localChildProfile.name}');

      // Step 1: Check if user is authenticated
      if (!authProvider.isAuthenticated) {
        return MigrationResult.failure('User not authenticated');
      }

      // Step 2: Create or update child profile in cloud
      ChildProfile? cloudChildProfile;
      
      // Check if child already exists in cloud
      final existingChild = authProvider.getChildProfile(localChildProfile.id);
      
      if (existingChild != null) {
        // Update existing child profile
        cloudChildProfile = existingChild;
        debugPrint('Found existing child profile in cloud');
      } else {
        // Create new child profile in cloud
        cloudChildProfile = await authProvider.createChildProfile(
          name: localChildProfile.name,
          avatarId: localChildProfile.avatarId,
          age: localChildProfile.age,
        );
        
        if (cloudChildProfile == null) {
          return MigrationResult.failure('Failed to create child profile in cloud');
        }
        
        debugPrint('Created new child profile in cloud');
      }

      // Step 3: Migrate story progress
      final progressMigrated = await _migrateStoryProgress(
        localChildProfile,
        cloudChildProfile,
      );

      if (!progressMigrated) {
        return MigrationResult.failure('Failed to migrate story progress');
      }

      // Step 4: Migrate preferences
      final preferencesMigrated = await _migratePreferences(
        localChildProfile,
        cloudChildProfile,
      );

      if (!preferencesMigrated) {
        return MigrationResult.failure('Failed to migrate preferences');
      }

      // Step 5: Mark migration as complete
      await _markMigrationComplete();

      debugPrint('Migration completed successfully');
      return MigrationResult.success(cloudChildProfile);
    } catch (e) {
      debugPrint('Migration failed: $e');
      return MigrationResult.failure('Migration failed: $e');
    }
  }

  /// Migrate story progress data
  static Future<bool> _migrateStoryProgress(
    ChildProfile localProfile,
    ChildProfile cloudProfile,
  ) async {
    try {
      // Load legacy local progress
      final legacyProgress = await StoryProgressService.loadProgress(
        childId: localProfile.id,
      );

      if (legacyProgress == null) {
        debugPrint('No legacy progress to migrate');
        return true;
      }

      // Convert legacy format to new format
      final storyId = legacyProgress['storyId'] as String?;
      final sceneId = legacyProgress['sceneId'] as String?;
      final timestamp = legacyProgress['timestamp'] as int?;
      final choiceHistory = legacyProgress['choiceHistory'] as List<String>?;

      if (storyId == null || sceneId == null) {
        debugPrint('Invalid legacy progress data');
        return true; // Skip invalid data
      }

      // Create new story progress
      final newStoryProgress = StoryProgress(
        storyId: storyId,
        currentSceneId: sceneId,
        completedScenes: [], // Legacy format doesn't have this
        choicesMade: {
          if (choiceHistory != null)
            for (int i = 0; i < choiceHistory.length; i++)
              'choice_$i': choiceHistory[i]
        },
        lastUpdated: timestamp != null 
            ? DateTime.fromMillisecondsSinceEpoch(timestamp)
            : DateTime.now(),
        isCompleted: false, // Legacy format doesn't have this
        totalTimeMinutes: 0, // Legacy format doesn't have this
      );

      // Update cloud profile with migrated progress
      final updatedStoryProgress = Map<String, StoryProgress>.from(cloudProfile.storyProgress);
      updatedStoryProgress[storyId] = newStoryProgress;

      final updatedProfile = cloudProfile.copyWith(
        storyProgress: updatedStoryProgress,
        lastActive: newStoryProgress.lastUpdated,
      );

      // Save to cloud
      final success = await StoryProgressService.saveProgressToCloud(updatedProfile);
      
      if (success) {
        debugPrint('Story progress migrated successfully');
      }
      
      return success;
    } catch (e) {
      debugPrint('Error migrating story progress: $e');
      return false;
    }
  }

  /// Migrate user preferences
  static Future<bool> _migratePreferences(
    ChildProfile localProfile,
    ChildProfile cloudProfile,
  ) async {
    try {
      // Merge preferences (local takes precedence)
      final mergedPreferences = Map<String, dynamic>.from(cloudProfile.preferences);
      mergedPreferences.addAll(localProfile.preferences);

      final updatedProfile = cloudProfile.copyWith(
        preferences: mergedPreferences,
      );

      // Save to cloud
      final success = await StoryProgressService.saveProgressToCloud(updatedProfile);
      
      if (success) {
        debugPrint('Preferences migrated successfully');
      }
      
      return success;
    } catch (e) {
      debugPrint('Error migrating preferences: $e');
      return false;
    }
  }

  /// Mark migration as complete
  static Future<void> _markMigrationComplete() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_migrationVersionKey, _currentMigrationVersion);
      await prefs.setBool(_hasMigratedKey, true);
      debugPrint('Migration marked as complete');
    } catch (e) {
      debugPrint('Error marking migration complete: $e');
    }
  }

  /// Reset migration status (for testing)
  static Future<void> resetMigrationStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_migrationVersionKey);
      await prefs.remove(_hasMigratedKey);
      debugPrint('Migration status reset');
    } catch (e) {
      debugPrint('Error resetting migration status: $e');
    }
  }

  /// Get migration status info
  static Future<MigrationStatus> getMigrationStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final migrationVersion = prefs.getInt(_migrationVersionKey) ?? 0;
      final hasMigrated = prefs.getBool(_hasMigratedKey) ?? false;
      
      return MigrationStatus(
        currentVersion: migrationVersion,
        targetVersion: _currentMigrationVersion,
        isComplete: hasMigrated && migrationVersion >= _currentMigrationVersion,
      );
    } catch (e) {
      debugPrint('Error getting migration status: $e');
      return MigrationStatus(
        currentVersion: 0,
        targetVersion: _currentMigrationVersion,
        isComplete: false,
      );
    }
  }
}

/// Result of a migration operation
class MigrationResult {
  final bool isSuccess;
  final String? errorMessage;
  final ChildProfile? migratedProfile;

  const MigrationResult._({
    required this.isSuccess,
    this.errorMessage,
    this.migratedProfile,
  });

  factory MigrationResult.success(ChildProfile profile) {
    return MigrationResult._(
      isSuccess: true,
      migratedProfile: profile,
    );
  }

  factory MigrationResult.failure(String error) {
    return MigrationResult._(
      isSuccess: false,
      errorMessage: error,
    );
  }
}

/// Migration status information
class MigrationStatus {
  final int currentVersion;
  final int targetVersion;
  final bool isComplete;

  const MigrationStatus({
    required this.currentVersion,
    required this.targetVersion,
    required this.isComplete,
  });

  bool get isNeeded => currentVersion < targetVersion || !isComplete;
  
  double get progress => targetVersion > 0 ? currentVersion / targetVersion : 0.0;
}
