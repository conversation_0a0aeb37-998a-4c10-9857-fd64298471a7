import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/parent_profile.dart';
import '../../profile/models/child_profile.dart';

/// Service for handling Firebase authentication and user management
class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get the current user
  User? get currentUser => _auth.currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  /// Stream of authentication state changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  /// Sign up with email and password
  Future<UserCredential?> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String displayName,
  }) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update display name
      await credential.user?.updateDisplayName(displayName);

      // Create parent profile in Firestore
      if (credential.user != null) {
        await _createParentProfile(credential.user!, displayName);
      }

      return credential;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Sign in with email and password
  Future<UserCredential?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      return await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Sign in anonymously (guest mode)
  Future<UserCredential?> signInAnonymously() async {
    try {
      return await _auth.signInAnonymously();
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Sign out
  Future<void> signOut() async {
    await _auth.signOut();
  }

  /// Delete user account
  Future<void> deleteAccount() async {
    final user = currentUser;
    if (user == null) throw Exception('No user signed in');

    try {
      // Delete parent profile and all child profiles
      await _deleteParentData(user.uid);

      // Delete user account
      await user.delete();
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Get parent profile
  Future<ParentProfile?> getParentProfile([String? userId]) async {
    final uid = userId ?? currentUser?.uid;
    if (uid == null) return null;

    try {
      final doc = await _firestore.collection('parents').doc(uid).get();
      if (doc.exists) {
        return ParentProfile.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get parent profile: $e');
    }
  }

  /// Update parent profile
  Future<void> updateParentProfile(ParentProfile profile) async {
    final user = currentUser;
    if (user == null) throw Exception('No user signed in');

    try {
      await _firestore.collection('parents').doc(user.uid).update(
        profile.copyWith(updatedAt: DateTime.now()).toFirestore(),
      );
    } catch (e) {
      throw Exception('Failed to update parent profile: $e');
    }
  }

  /// Get child profiles for current parent
  Future<List<ChildProfile>> getChildProfiles([String? parentId]) async {
    final uid = parentId ?? currentUser?.uid;
    if (uid == null) return [];

    try {
      final querySnapshot = await _firestore
          .collection('children')
          .where('parentId', isEqualTo: uid)
          .orderBy('createdAt', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => ChildProfile.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get child profiles: $e');
    }
  }

  /// Create child profile
  Future<ChildProfile> createChildProfile({
    required String name,
    String? avatarId,
    int? age,
  }) async {
    final user = currentUser;
    if (user == null) throw Exception('No user signed in');

    try {
      final now = DateTime.now();
      final childProfile = ChildProfile(
        id: '', // Will be set by Firestore
        name: name,
        parentId: user.uid,
        createdAt: now,
        lastActive: now,
        avatarId: avatarId,
        age: age,
      );

      final docRef = await _firestore.collection('children').add(childProfile.toFirestore());

      // Update parent profile with new child ID
      final parentProfile = await getParentProfile();
      if (parentProfile != null) {
        final updatedChildIds = List<String>.from(parentProfile.childProfileIds)..add(docRef.id);
        await updateParentProfile(parentProfile.copyWith(childProfileIds: updatedChildIds));
      }

      return childProfile.copyWith(id: docRef.id);
    } catch (e) {
      throw Exception('Failed to create child profile: $e');
    }
  }

  /// Update child profile
  Future<void> updateChildProfile(ChildProfile profile) async {
    final user = currentUser;
    if (user == null) throw Exception('No user signed in');

    try {
      await _firestore.collection('children').doc(profile.id).update(
        profile.copyWith(lastActive: DateTime.now()).toFirestore(),
      );
    } catch (e) {
      throw Exception('Failed to update child profile: $e');
    }
  }

  /// Delete child profile
  Future<void> deleteChildProfile(String childId) async {
    final user = currentUser;
    if (user == null) throw Exception('No user signed in');

    try {
      // Delete child document
      await _firestore.collection('children').doc(childId).delete();

      // Update parent profile to remove child ID
      final parentProfile = await getParentProfile();
      if (parentProfile != null) {
        final updatedChildIds = List<String>.from(parentProfile.childProfileIds)..remove(childId);
        await updateParentProfile(parentProfile.copyWith(childProfileIds: updatedChildIds));
      }
    } catch (e) {
      throw Exception('Failed to delete child profile: $e');
    }
  }

  /// Create parent profile in Firestore
  Future<void> _createParentProfile(User user, String displayName) async {
    final profile = ParentProfile(
      id: user.uid,
      email: user.email ?? '',
      displayName: displayName,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      childProfileIds: [],
    );

    await _firestore.collection('parents').doc(user.uid).set(profile.toFirestore());
  }

  /// Delete all parent data
  Future<void> _deleteParentData(String parentId) async {
    final batch = _firestore.batch();

    // Delete all child profiles
    final childProfiles = await getChildProfiles(parentId);
    for (final child in childProfiles) {
      batch.delete(_firestore.collection('children').doc(child.id));
    }

    // Delete parent profile
    batch.delete(_firestore.collection('parents').doc(parentId));

    await batch.commit();
  }

  /// Handle Firebase Auth exceptions
  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'weak-password':
        return 'The password provided is too weak.';
      case 'email-already-in-use':
        return 'An account already exists for that email.';
      case 'user-not-found':
        return 'No user found for that email.';
      case 'wrong-password':
        return 'Wrong password provided.';
      case 'invalid-email':
        return 'The email address is not valid.';
      case 'user-disabled':
        return 'This user account has been disabled.';
      case 'too-many-requests':
        return 'Too many requests. Try again later.';
      case 'operation-not-allowed':
        return 'Signing in with Email and Password is not enabled.';
      default:
        return 'An authentication error occurred: ${e.message}';
    }
  }
}
