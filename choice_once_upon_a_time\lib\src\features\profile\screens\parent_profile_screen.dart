import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../auth/models/parent_profile.dart';
import '../../auth/providers/parent_auth_provider.dart';
import '../../../shared_kernel/theme/app_theme.dart';

/// Screen for editing parent profile
class ParentProfileScreen extends StatefulWidget {
  /// Constructor
  const ParentProfileScreen({super.key});

  /// Route name for navigation
  static const routeName = '/profile/parent';

  @override
  State<ParentProfileScreen> createState() => _ParentProfileScreenState();
}

class _ParentProfileScreenState extends State<ParentProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();

  // Using preferences map to store additional settings
  Map<String, dynamic> _preferences = {};

  bool _isLoading = false;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentProfile();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  /// Load current parent profile data
  void _loadCurrentProfile() {
    final authProvider = Provider.of<ParentAuthProvider>(context, listen: false);
    final profile = authProvider.parentProfile;

    if (profile != null) {
      _nameController.text = profile.displayName;
      _emailController.text = profile.email;
      _preferences = Map<String, dynamic>.from(profile.preferences);
    }
  }

  /// Mark that changes have been made
  void _markChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  /// Save profile changes
  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<ParentAuthProvider>(context, listen: false);
      final currentProfile = authProvider.parentProfile;

      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(
          displayName: _nameController.text.trim(),
          email: _emailController.text.trim(),
          preferences: _preferences,
          updatedAt: DateTime.now(),
        );

        await authProvider.updateParentProfile(updatedProfile);

        if (mounted) {
          setState(() {
            _hasChanges = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profile updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Parent Profile'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_hasChanges)
            TextButton(
              onPressed: _isLoading ? null : _saveProfile,
              child: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Text(
                    'Save',
                    style: TextStyle(color: Colors.white),
                  ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Basic Information
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Basic Information',
                      style: AppTheme.subheadingStyle.copyWith(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Name field
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'Full Name',
                        hintText: 'Enter your full name',
                        prefixIcon: Icon(Icons.person),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter your name';
                        }
                        return null;
                      },
                      onChanged: (_) => _markChanged(),
                    ),

                    const SizedBox(height: 16),

                    // Email field
                    TextFormField(
                      controller: _emailController,
                      decoration: const InputDecoration(
                        labelText: 'Email Address',
                        hintText: 'Enter your email address',
                        prefixIcon: Icon(Icons.email),
                      ),
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter your email';
                        }
                        if (!value.contains('@')) {
                          return 'Please enter a valid email';
                        }
                        return null;
                      },
                      onChanged: (_) => _markChanged(),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Preferences
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Preferences',
                      style: AppTheme.subheadingStyle.copyWith(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Language dropdown
                    DropdownButtonFormField<String>(
                      value: _preferences['preferredLanguage'] ?? 'en',
                      decoration: const InputDecoration(
                        labelText: 'Preferred Language',
                        prefixIcon: Icon(Icons.language),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'en', child: Text('English')),
                        DropdownMenuItem(value: 'es', child: Text('Spanish')),
                        DropdownMenuItem(value: 'fr', child: Text('French')),
                        DropdownMenuItem(value: 'de', child: Text('German')),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _preferences['preferredLanguage'] = value;
                          });
                          _markChanged();
                        }
                      },
                    ),

                    const SizedBox(height: 16),

                    // Theme dropdown
                    DropdownButtonFormField<String>(
                      value: _preferences['preferredTheme'] ?? 'auto',
                      decoration: const InputDecoration(
                        labelText: 'App Theme',
                        prefixIcon: Icon(Icons.palette),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'light', child: Text('Light')),
                        DropdownMenuItem(value: 'dark', child: Text('Dark')),
                        DropdownMenuItem(value: 'auto', child: Text('System Default')),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _preferences['preferredTheme'] = value;
                          });
                          _markChanged();
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Parental Controls
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Parental Controls',
                      style: AppTheme.subheadingStyle.copyWith(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Screen time slider
                    Text(
                      'Maximum Screen Time: $_maxScreenTimeMinutes minutes',
                      style: AppTheme.bodyStyle.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Slider(
                      value: _maxScreenTimeMinutes.toDouble(),
                      min: 5,
                      max: 120,
                      divisions: 23,
                      label: '$_maxScreenTimeMinutes min',
                      onChanged: (value) {
                        setState(() {
                          _maxScreenTimeMinutes = value.round();
                        });
                        _markChanged();
                      },
                    ),

                    // Show progress toggle
                    SwitchListTile(
                      title: const Text('Show Progress to Children'),
                      subtitle: const Text('Let children see their reading progress'),
                      value: _showProgressToChildren,
                      onChanged: (value) {
                        setState(() {
                          _showProgressToChildren = value;
                        });
                        _markChanged();
                      },
                      activeColor: AppTheme.primaryColor,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Notifications
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Notifications',
                      style: AppTheme.subheadingStyle.copyWith(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Email notifications toggle
                    SwitchListTile(
                      title: const Text('Email Notifications'),
                      subtitle: const Text('Receive updates via email'),
                      value: _emailNotifications,
                      onChanged: (value) {
                        setState(() {
                          _emailNotifications = value;
                        });
                        _markChanged();
                      },
                      activeColor: AppTheme.primaryColor,
                    ),

                    // Push notifications toggle
                    SwitchListTile(
                      title: const Text('Push Notifications'),
                      subtitle: const Text('Receive app notifications'),
                      value: _pushNotifications,
                      onChanged: (value) {
                        setState(() {
                          _pushNotifications = value;
                        });
                        _markChanged();
                      },
                      activeColor: AppTheme.primaryColor,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 32),

            // Save button
            if (_hasChanges)
              ElevatedButton(
                onPressed: _isLoading ? null : _saveProfile,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text('Save Changes'),
              ),
          ],
        ),
      ),
    );
  }
}
