# Choice: Once Upon A Time

An interactive storytelling app for children with branching narratives and comprehensive narration flow control.

## Features

- Interactive stories with branching narratives
- **Enhanced Text-to-Speech narration** with natural speech delivery and sentence-level pause injection
- **Complete scene narration enforcement** - blocks progression until all segments are narrated
- **Real-time word highlighting** with configurable narration speed (0.3x-1.5x)
- **Choice popup timing control** - only displays after complete narration
- **User-configurable narration settings** - speed, natural pauses, and timing controls
- Responsive landscape-oriented design for both phones and TV/tablets
- Child profile management with save/resume functionality
- Parent authentication system
- **Image caching and preloading optimization** for smooth performance
- **Comprehensive testing coverage** with unit, widget, and integration tests

## Getting Started

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Run `flutter run -d chrome` to launch the app in Chrome

## Adding New Stories

Stories are defined in JSON files located in the `assets/images` directory. Each story has a corresponding folder with scene images.

### Story JSON Structure

```json
{
  "storyId": "unique_story_identifier",
  "storyTitle": "The Story Title",
  "targetAge": "4-6",
  "moralTheme": "Kindness and Responsibility",
  "coverImage": "assets/images/story_folder/cover.jpg",
  "isLocked": false,
  "characterList": [
    {
      "characterName": "Character Name",
      "characterDescriptionForVisual": "Description of the character"
    }
  ],
  "scenes": [
    {
      "sceneId": "scene_1",
      "imageAssetPath": "assets/images/story_folder/scene1",
      "narrationText": "The narration text for this scene.",
      "isChoicePoint": true,
      "choicePrompt": "What should the character do?",
      "choices": [
        {
          "choiceText": "First choice",
          "nextSceneId": "scene_2a"
        },
        {
          "choiceText": "Second choice",
          "nextSceneId": "scene_2b"
        }
      ],
      "isEndingScene": false
    },
    {
      "sceneId": "scene_2a",
      "imageAssetPath": "assets/images/story_folder/scene2a",
      "narrationText": "The narration text for scene 2a.",
      "isChoicePoint": false,
      "defaultNextSceneId": "scene_3",
      "isEndingScene": false
    },
    {
      "sceneId": "scene_end",
      "imageAssetPath": "assets/images/story_folder/end",
      "narrationText": "The ending narration text.",
      "isChoicePoint": false,
      "isEndingScene": true,
      "moralLessonReinforced": "The moral lesson of the story."
    }
  ]
}
```

### Image Assets

For each story, create a folder in `assets/images` with the same name as the story ID. Place all scene images in this folder.

Image naming convention:
- `cover.jpg` - The cover image for the story
- `scene1.jpg` - The image for scene 1
- `scene2a.jpg` - The image for scene 2a
- `end.jpg` - The image for the ending scene

### Adding the Story to the App

1. Create a JSON file in the `assets/images` directory with the story structure
2. Create a folder in `assets/images` with the same name as the story ID
3. Add all scene images to the folder
4. Update `pubspec.yaml` to include the new assets if needed
5. Run `flutter pub get` to update the assets
6. Launch the app to see the new story in the Story Library

## Architecture

The app follows a feature-first directory structure:

- `lib/src/features/` - Contains all features of the app
  - `auth/` - Authentication feature
  - `profile/` - Child profile management
  - `story/` - Story models, providers, and services
  - `story_library/` - Story library screen
  - `story_interaction/` - Story interaction screen
  - `settings/` - Settings screen
  - `coming_soon/` - Coming soon screen
- `lib/src/shared_kernel/` - Contains shared components and utilities

## State Management

The app uses Provider for state management:

- `ActiveChildProvider` - Manages the active child profile
- `StoryProvider` - Manages the story state, including loading stories, navigating between scenes, and handling choices

## Responsive Design

The app is designed to be responsive and work well on both phones and TV/tablets:

- Phone: Portrait orientation with a vertical layout
- TV/Tablet: Landscape orientation with a horizontal layout

## Enhanced Text-to-Speech with Narration Flow Control

The app uses the `flutter_tts` package with comprehensive narration flow control:

### **Natural Speech Delivery**
- **Sentence-level pause injection** with configurable timing (200-2000ms)
- **Automatic pauses between sentences** (default: 650ms)
- **Longer pauses between text segments** (default: 1000ms)
- **Configurable narration speed** (0.3x to 1.5x range)

### **Complete Scene Narration Enforcement**
- **Blocks navigation** until ALL text segments within a scene are fully narrated
- **Prevents premature choice popup display** during mid-scene narration
- **Uses TTS completion events** to accurately detect when narration is truly finished

### **Real-time Word Highlighting**
- **Word-by-word highlighting** as text is being narrated
- **Sentence and word progress tracking** for visual feedback
- **Synchronized highlighting** with natural speech delivery

### **User Controls**
- **Play/pause/resume controls** that respect narration flow
- **Manual navigation** (previous/next segment) with proper narration stopping
- **Real-time settings adjustment** through StorySettingsDialog

## Future Improvements

- Add more stories
- Implement Firebase authentication for parent login
- Add in-app purchases for locked stories
- Implement theme selection
- Add sound effects and background music
