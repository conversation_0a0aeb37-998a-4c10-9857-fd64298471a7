# Choice: Once Upon A Time

An interactive storytelling app for children with branching narratives.

## Features

- Interactive stories with branching narratives
- Text-to-speech narration with word highlighting
- Responsive design for both phones and TV/tablets
- Child profile management
- Parent authentication

## Getting Started

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Run `flutter run -d chrome` to launch the app in Chrome

## Adding New Stories

Stories are defined in JSON files located in the `assets/images` directory. Each story has a corresponding folder with scene images.

### Story JSON Structure

```json
{
  "storyId": "unique_story_identifier",
  "storyTitle": "The Story Title",
  "targetAge": "4-6",
  "moralTheme": "Kindness and Responsibility",
  "coverImage": "assets/images/story_folder/cover.jpg",
  "isLocked": false,
  "characterList": [
    {
      "characterName": "Character Name",
      "characterDescriptionForVisual": "Description of the character"
    }
  ],
  "scenes": [
    {
      "sceneId": "scene_1",
      "imageAssetPath": "assets/images/story_folder/scene1",
      "narrationText": "The narration text for this scene.",
      "isChoicePoint": true,
      "choicePrompt": "What should the character do?",
      "choices": [
        {
          "choiceText": "First choice",
          "nextSceneId": "scene_2a"
        },
        {
          "choiceText": "Second choice",
          "nextSceneId": "scene_2b"
        }
      ],
      "isEndingScene": false
    },
    {
      "sceneId": "scene_2a",
      "imageAssetPath": "assets/images/story_folder/scene2a",
      "narrationText": "The narration text for scene 2a.",
      "isChoicePoint": false,
      "defaultNextSceneId": "scene_3",
      "isEndingScene": false
    },
    {
      "sceneId": "scene_end",
      "imageAssetPath": "assets/images/story_folder/end",
      "narrationText": "The ending narration text.",
      "isChoicePoint": false,
      "isEndingScene": true,
      "moralLessonReinforced": "The moral lesson of the story."
    }
  ]
}
```

### Image Assets

For each story, create a folder in `assets/images` with the same name as the story ID. Place all scene images in this folder.

Image naming convention:
- `cover.jpg` - The cover image for the story
- `scene1.jpg` - The image for scene 1
- `scene2a.jpg` - The image for scene 2a
- `end.jpg` - The image for the ending scene

### Adding the Story to the App

1. Create a JSON file in the `assets/images` directory with the story structure
2. Create a folder in `assets/images` with the same name as the story ID
3. Add all scene images to the folder
4. Update `pubspec.yaml` to include the new assets if needed
5. Run `flutter pub get` to update the assets
6. Launch the app to see the new story in the Story Library

## Architecture

The app follows a feature-first directory structure:

- `lib/src/features/` - Contains all features of the app
  - `auth/` - Authentication feature
  - `profile/` - Child profile management
  - `story/` - Story models, providers, and services
  - `story_library/` - Story library screen
  - `story_interaction/` - Story interaction screen
  - `settings/` - Settings screen
  - `coming_soon/` - Coming soon screen
- `lib/src/shared_kernel/` - Contains shared components and utilities

## State Management

The app uses Provider for state management:

- `ActiveChildProvider` - Manages the active child profile
- `StoryProvider` - Manages the story state, including loading stories, navigating between scenes, and handling choices

## Responsive Design

The app is designed to be responsive and work well on both phones and TV/tablets:

- Phone: Portrait orientation with a vertical layout
- TV/Tablet: Landscape orientation with a horizontal layout

## Text-to-Speech

The app uses the `flutter_tts` package for text-to-speech narration:

- Narrates the scene text with word highlighting
- Narrates the choice prompt and choices
- Narrates the moral lesson at the end of the story

## Future Improvements

- Add more stories
- Implement Firebase authentication for parent login
- Add in-app purchases for locked stories
- Implement theme selection
- Add sound effects and background music
