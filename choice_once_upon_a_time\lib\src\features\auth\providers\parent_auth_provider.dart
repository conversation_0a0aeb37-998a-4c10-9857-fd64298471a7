import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../models/parent_profile.dart';
import '../services/auth_service.dart';
import '../../profile/models/child_profile.dart';
import '../../../core/config/app_config.dart';

/// Provider for managing parent authentication and profile data
class ParentAuthProvider extends ChangeNotifier {
  /// Authentication service instance (only used when not in local testing mode)
  AuthService? _authService;

  /// Current authenticated user
  User? _currentUser;

  /// Current parent profile
  ParentProfile? _parentProfile;

  /// Child profiles for current parent
  List<ChildProfile> _childProfiles = [];

  /// Loading state
  bool _isLoading = false;

  /// Error message
  String? _errorMessage;

  /// Authentication state
  bool _isAuthenticated = false;

  /// Guest mode state
  bool _isGuestMode = false;

  /// Getters
  User? get currentUser => _currentUser;
  ParentProfile? get parentProfile => _parentProfile;
  List<ChildProfile> get childProfiles => _childProfiles;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _isAuthenticated;
  bool get isGuestMode => _isGuestMode;
  bool get isPremium => _parentProfile?.isPremiumActive ?? false;

  /// Constructor
  ParentAuthProvider() {
    if (AppConfig.useLocalTestingMode) {
      _initializeLocalTestingMode();
    } else {
      _authService = AuthService();
      _initializeAuthListener();
    }
  }

  /// Initialize local testing mode with mock data
  void _initializeLocalTestingMode() {
    AppConfig.logInfo('Initializing local testing mode');

    // Set up mock authenticated state
    _isAuthenticated = true;
    _parentProfile = ParentProfile(
      id: 'local_parent_123',
      email: AppConfig.localTestingParentEmail,
      displayName: AppConfig.localTestingParentName,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      childProfileIds: ['local_child_1', 'local_child_2'],
      isPremium: false,
    );

    // Set up mock child profiles
    _childProfiles = [
      ChildProfile(
        id: 'local_child_1',
        parentId: 'local_parent_123',
        name: 'Emma',
        age: 7,
        avatarId: 'avatar2',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        lastActive: DateTime.now().subtract(const Duration(hours: 2)),
        storyProgress: {},
        preferences: {},
      ),
      ChildProfile(
        id: 'local_child_2',
        parentId: 'local_parent_123',
        name: 'Alex',
        age: 9,
        avatarId: 'avatar3',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        lastActive: DateTime.now().subtract(const Duration(days: 1)),
        storyProgress: {},
        preferences: {},
      ),
    ];

    _isLoading = false;
    _errorMessage = null;

    // Notify listeners after a short delay to simulate loading
    Future.delayed(const Duration(milliseconds: 500), () {
      notifyListeners();
    });
  }

  /// Initialize guest mode with limited functionality
  void _initializeGuestMode() {
    AppConfig.logInfo('Initializing guest mode');

    _isAuthenticated = true;
    _isGuestMode = true;

    // Create a guest parent profile
    _parentProfile = ParentProfile(
      id: 'guest_${DateTime.now().millisecondsSinceEpoch}',
      email: '<EMAIL>',
      displayName: 'Guest User',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      childProfileIds: [],
      isPremium: false, // Guests don't have premium access
    );

    // Start with no child profiles - guests can create temporary ones
    _childProfiles = [];

    _isLoading = false;
    _errorMessage = null;
  }

  /// Initialize authentication state listener
  void _initializeAuthListener() {
    _authService?.authStateChanges.listen((User? user) async {
      _currentUser = user;
      _isAuthenticated = user != null;

      if (user != null) {
        await _loadParentData();
      } else {
        _clearData();
      }

      notifyListeners();
    });
  }

  /// Sign up with email and password
  Future<bool> signUp({
    required String email,
    required String password,
    required String displayName,
  }) async {
    if (AppConfig.useLocalTestingMode) {
      return _performLocalAuthAction(() async {
        AppConfig.logInfo('Local testing: Creating account for $email');

        _isAuthenticated = true;
        _parentProfile = ParentProfile(
          id: 'local_parent_${DateTime.now().millisecondsSinceEpoch}',
          email: email,
          displayName: displayName,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          childProfileIds: [],
          isPremium: false,
        );
        _childProfiles = [];

        return true;
      });
    }

    return _performAuthAction(() async {
      await _authService!.signUpWithEmailAndPassword(
        email: email,
        password: password,
        displayName: displayName,
      );
      return true;
    });
  }

  /// Sign in with email and password
  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    if (AppConfig.useLocalTestingMode) {
      return _performLocalAuthAction(() async {
        AppConfig.logInfo('Local testing: Signing in $email');

        // Mock successful sign in - data already set in constructor
        return true;
      });
    }

    return _performAuthAction(() async {
      await _authService!.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return true;
    });
  }

  /// Sign in as guest
  Future<bool> signInAsGuest() async {
    if (AppConfig.useLocalTestingMode) {
      return _performLocalAuthAction(() async {
        AppConfig.logInfo('Local testing: Signing in as guest');
        _initializeGuestMode();
        return true;
      });
    }

    return _performAuthAction(() async {
      // Sign in anonymously with Firebase
      await _authService!.signInAnonymously();
      _initializeGuestMode();
      return true;
    });
  }

  /// Send password reset email
  Future<bool> sendPasswordResetEmail(String email) async {
    if (AppConfig.useLocalTestingMode) {
      AppConfig.logInfo('Local testing: Password reset email would be sent to $email');
      return true;
    }

    return _performAuthAction(() async {
      await _authService!.sendPasswordResetEmail(email);
      return true;
    });
  }

  /// Sign out
  Future<void> signOut() async {
    if (AppConfig.useLocalTestingMode) {
      AppConfig.logInfo('Local testing: Signing out');
      _clearData();
      notifyListeners();
      return;
    }

    await _authService!.signOut();
    _clearData();
    notifyListeners();
  }

  /// Delete account
  Future<bool> deleteAccount() async {
    if (AppConfig.useLocalTestingMode) {
      AppConfig.logInfo('Local testing: Account would be deleted');
      _clearData();
      return true;
    }

    return _performAuthAction(() async {
      await _authService!.deleteAccount();
      _clearData();
      return true;
    });
  }

  /// Update parent profile
  Future<bool> updateParentProfile(ParentProfile profile) async {
    if (AppConfig.useLocalTestingMode) {
      return _performLocalAuthAction(() async {
        AppConfig.logInfo('Local testing: Updating parent profile');
        _parentProfile = profile;
        return true;
      });
    }

    return _performAuthAction(() async {
      await _authService!.updateParentProfile(profile);
      _parentProfile = profile;
      return true;
    });
  }

  /// Create child profile
  Future<ChildProfile?> createChildProfile({
    required String name,
    String? avatarId,
    int? age,
  }) async {
    if (AppConfig.useLocalTestingMode) {
      ChildProfile? newProfile;

      final success = await _performLocalAuthAction(() async {
        AppConfig.logInfo('Local testing: Creating child profile for $name');

        newProfile = ChildProfile(
          id: 'local_child_${DateTime.now().millisecondsSinceEpoch}',
          parentId: _parentProfile!.id,
          name: name,
          age: age ?? 6,
          avatarId: avatarId ?? 'avatar1',
          createdAt: DateTime.now(),
          lastActive: DateTime.now(),
          storyProgress: {},
          preferences: {},
        );

        // Add to local list
        _childProfiles.add(newProfile!);
        return true;
      });

      return success ? newProfile : null;
    }

    ChildProfile? newProfile;

    final success = await _performAuthAction(() async {
      newProfile = await _authService!.createChildProfile(
        name: name,
        avatarId: avatarId,
        age: age,
      );

      // Add to local list
      _childProfiles.add(newProfile!);
      return true;
    });

    return success ? newProfile : null;
  }

  /// Update child profile
  Future<bool> updateChildProfile(ChildProfile profile) async {
    if (AppConfig.useLocalTestingMode) {
      return _performLocalAuthAction(() async {
        AppConfig.logInfo('Local testing: Updating child profile ${profile.name}');

        // Update local list
        final index = _childProfiles.indexWhere((p) => p.id == profile.id);
        if (index != -1) {
          _childProfiles[index] = profile;
        }
        return true;
      });
    }

    return _performAuthAction(() async {
      await _authService!.updateChildProfile(profile);

      // Update local list
      final index = _childProfiles.indexWhere((p) => p.id == profile.id);
      if (index != -1) {
        _childProfiles[index] = profile;
      }

      return true;
    });
  }

  /// Delete child profile
  Future<bool> deleteChildProfile(String childId) async {
    if (AppConfig.useLocalTestingMode) {
      return _performLocalAuthAction(() async {
        AppConfig.logInfo('Local testing: Deleting child profile $childId');

        // Remove from local list
        _childProfiles.removeWhere((p) => p.id == childId);
        return true;
      });
    }

    return _performAuthAction(() async {
      await _authService!.deleteChildProfile(childId);

      // Remove from local list
      _childProfiles.removeWhere((p) => p.id == childId);
      return true;
    });
  }

  /// Refresh parent and child data
  Future<void> refreshData() async {
    if (_isAuthenticated) {
      await _loadParentData();
      notifyListeners();
    }
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Load parent profile and child profiles
  Future<void> _loadParentData() async {
    if (AppConfig.useLocalTestingMode) {
      // Data already loaded in local testing mode during initialization
      AppConfig.logInfo('Local testing: Skipping data load - using mock data');
      return;
    }

    try {
      _isLoading = true;
      notifyListeners();

      // Load parent profile
      _parentProfile = await _authService!.getParentProfile();

      // Load child profiles
      _childProfiles = await _authService!.getChildProfiles();

      _errorMessage = null;
    } catch (e) {
      _errorMessage = e.toString();
      debugPrint('Error loading parent data: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Clear all data
  void _clearData() {
    _currentUser = null;
    _parentProfile = null;
    _childProfiles = [];
    _isAuthenticated = false;
    _isGuestMode = false;
    _errorMessage = null;
    _isLoading = false;
  }

  /// Perform authentication action with error handling
  Future<bool> _performAuthAction(Future<bool> Function() action) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final result = await action();

      _errorMessage = null;
      return result;
    } catch (e) {
      _errorMessage = e.toString();
      debugPrint('Auth action error: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Perform local authentication action (for testing mode)
  Future<bool> _performLocalAuthAction(Future<bool> Function() action) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 500));

      final result = await action();

      _errorMessage = null;
      return result;
    } catch (e) {
      _errorMessage = e.toString();
      AppConfig.logError('Local auth action error', e);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Update premium status
  Future<bool> updatePremiumStatus({
    required bool isPremium,
    DateTime? expiryDate,
  }) async {
    if (_parentProfile == null) return false;

    final updatedProfile = _parentProfile!.copyWith(
      isPremium: isPremium,
      premiumExpiryDate: expiryDate,
      updatedAt: DateTime.now(),
    );

    return await updateParentProfile(updatedProfile);
  }

  /// Get child profile by ID
  ChildProfile? getChildProfile(String childId) {
    try {
      return _childProfiles.firstWhere((profile) => profile.id == childId);
    } catch (e) {
      return null;
    }
  }

  /// Check if parent has any children
  bool get hasChildren => _childProfiles.isNotEmpty;

  /// Get total number of children
  int get childrenCount => _childProfiles.length;


}
