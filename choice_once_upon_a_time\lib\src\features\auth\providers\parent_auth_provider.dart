import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../models/parent_profile.dart';
import '../services/auth_service.dart';
import '../../profile/models/child_profile.dart';

/// Provider for managing parent authentication and profile data
class ParentAuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();

  /// Current authenticated user
  User? _currentUser;

  /// Current parent profile
  ParentProfile? _parentProfile;

  /// Child profiles for current parent
  List<ChildProfile> _childProfiles = [];

  /// Loading state
  bool _isLoading = false;

  /// Error message
  String? _errorMessage;

  /// Authentication state
  bool _isAuthenticated = false;

  /// Getters
  User? get currentUser => _currentUser;
  ParentProfile? get parentProfile => _parentProfile;
  List<ChildProfile> get childProfiles => _childProfiles;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _isAuthenticated;
  bool get isPremium => _parentProfile?.isPremiumActive ?? false;

  /// Constructor
  ParentAuthProvider() {
    _initializeAuthListener();
  }

  /// Initialize authentication state listener
  void _initializeAuthListener() {
    _authService.authStateChanges.listen((User? user) async {
      _currentUser = user;
      _isAuthenticated = user != null;
      
      if (user != null) {
        await _loadParentData();
      } else {
        _clearData();
      }
      
      notifyListeners();
    });
  }

  /// Sign up with email and password
  Future<bool> signUp({
    required String email,
    required String password,
    required String displayName,
  }) async {
    return _performAuthAction(() async {
      await _authService.signUpWithEmailAndPassword(
        email: email,
        password: password,
        displayName: displayName,
      );
      return true;
    });
  }

  /// Sign in with email and password
  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    return _performAuthAction(() async {
      await _authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return true;
    });
  }

  /// Send password reset email
  Future<bool> sendPasswordResetEmail(String email) async {
    return _performAuthAction(() async {
      await _authService.sendPasswordResetEmail(email);
      return true;
    });
  }

  /// Sign out
  Future<void> signOut() async {
    await _authService.signOut();
    _clearData();
    notifyListeners();
  }

  /// Delete account
  Future<bool> deleteAccount() async {
    return _performAuthAction(() async {
      await _authService.deleteAccount();
      _clearData();
      return true;
    });
  }

  /// Update parent profile
  Future<bool> updateParentProfile(ParentProfile profile) async {
    return _performAuthAction(() async {
      await _authService.updateParentProfile(profile);
      _parentProfile = profile;
      return true;
    });
  }

  /// Create child profile
  Future<ChildProfile?> createChildProfile({
    required String name,
    String? avatarId,
    int? age,
  }) async {
    ChildProfile? newProfile;
    
    final success = await _performAuthAction(() async {
      newProfile = await _authService.createChildProfile(
        name: name,
        avatarId: avatarId,
        age: age,
      );
      
      // Add to local list
      _childProfiles.add(newProfile!);
      return true;
    });

    return success ? newProfile : null;
  }

  /// Update child profile
  Future<bool> updateChildProfile(ChildProfile profile) async {
    return _performAuthAction(() async {
      await _authService.updateChildProfile(profile);
      
      // Update local list
      final index = _childProfiles.indexWhere((p) => p.id == profile.id);
      if (index != -1) {
        _childProfiles[index] = profile;
      }
      
      return true;
    });
  }

  /// Delete child profile
  Future<bool> deleteChildProfile(String childId) async {
    return _performAuthAction(() async {
      await _authService.deleteChildProfile(childId);
      
      // Remove from local list
      _childProfiles.removeWhere((p) => p.id == childId);
      return true;
    });
  }

  /// Refresh parent and child data
  Future<void> refreshData() async {
    if (_isAuthenticated) {
      await _loadParentData();
      notifyListeners();
    }
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Load parent profile and child profiles
  Future<void> _loadParentData() async {
    try {
      _isLoading = true;
      notifyListeners();

      // Load parent profile
      _parentProfile = await _authService.getParentProfile();
      
      // Load child profiles
      _childProfiles = await _authService.getChildProfiles();
      
      _errorMessage = null;
    } catch (e) {
      _errorMessage = e.toString();
      debugPrint('Error loading parent data: $e');
    } finally {
      _isLoading = false;
    }
  }

  /// Clear all data
  void _clearData() {
    _currentUser = null;
    _parentProfile = null;
    _childProfiles = [];
    _isAuthenticated = false;
    _errorMessage = null;
    _isLoading = false;
  }

  /// Perform authentication action with error handling
  Future<bool> _performAuthAction(Future<bool> Function() action) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final result = await action();
      
      _errorMessage = null;
      return result;
    } catch (e) {
      _errorMessage = e.toString();
      debugPrint('Auth action error: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Update premium status
  Future<bool> updatePremiumStatus({
    required bool isPremium,
    DateTime? expiryDate,
  }) async {
    if (_parentProfile == null) return false;

    final updatedProfile = _parentProfile!.copyWith(
      isPremium: isPremium,
      premiumExpiryDate: expiryDate,
      updatedAt: DateTime.now(),
    );

    return await updateParentProfile(updatedProfile);
  }

  /// Get child profile by ID
  ChildProfile? getChildProfile(String childId) {
    try {
      return _childProfiles.firstWhere((profile) => profile.id == childId);
    } catch (e) {
      return null;
    }
  }

  /// Check if parent has any children
  bool get hasChildren => _childProfiles.isNotEmpty;

  /// Get total number of children
  int get childrenCount => _childProfiles.length;

  @override
  void dispose() {
    super.dispose();
  }
}
