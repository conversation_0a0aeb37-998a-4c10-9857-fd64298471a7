#!/usr/bin/env dart

/// Test runner script for Choice: Once Upon A Time
/// 
/// This script provides a comprehensive test runner that can execute different
/// types of tests with various configurations.
/// 
/// Usage:
/// dart scripts/run_tests.dart [options]
/// 
/// Options:
/// --unit                Run unit tests only
/// --widget              Run widget tests only
/// --integration         Run integration tests only
/// --all                 Run all tests (default)
/// --coverage            Generate coverage report
/// --chrome              Run tests on Chrome (for integration tests)
/// --responsive          Run responsive design tests
/// --accessibility       Run accessibility tests
/// --performance         Run performance tests
/// --help                Show this help message

import 'dart:io';
import 'dart:convert';

class TestRunner {
  static const String projectRoot = '.';
  static const String testDir = 'test';
  static const String integrationTestDir = 'integration_test';
  static const String coverageDir = 'coverage';

  static Future<void> main(List<String> args) async {
    final config = TestConfig.fromArgs(args);
    
    if (config.showHelp) {
      _showHelp();
      return;
    }

    print('🧪 Choice: Once Upon A Time - Test Runner');
    print('==========================================');
    
    try {
      await _setupTestEnvironment();
      
      if (config.runUnit) {
        await _runUnitTests(config);
      }
      
      if (config.runWidget) {
        await _runWidgetTests(config);
      }
      
      if (config.runIntegration) {
        await _runIntegrationTests(config);
      }
      
      if (config.generateCoverage) {
        await _generateCoverageReport();
      }
      
      await _showTestSummary();
      
    } catch (e, stackTrace) {
      print('❌ Test runner failed: $e');
      print('Stack trace: $stackTrace');
      exit(1);
    } finally {
      await _cleanupTestEnvironment();
    }
  }

  static Future<void> _setupTestEnvironment() async {
    print('🔧 Setting up test environment...');
    
    // Ensure test directories exist
    await _ensureDirectoryExists(testDir);
    await _ensureDirectoryExists(integrationTestDir);
    await _ensureDirectoryExists(coverageDir);
    
    // Clean previous test artifacts
    await _cleanDirectory('$testDir/temp');
    await _cleanDirectory('$coverageDir/temp');
    
    print('✅ Test environment ready');
  }

  static Future<void> _runUnitTests(TestConfig config) async {
    print('\n📋 Running Unit Tests...');
    print('========================');
    
    final testFiles = await _findTestFiles('$testDir/unit');
    
    for (final testFile in testFiles) {
      print('Running: $testFile');
      
      final result = await _runFlutterTest(
        testFile,
        coverage: config.generateCoverage,
        timeout: '30s',
      );
      
      if (result.exitCode != 0) {
        print('❌ Failed: $testFile');
        print(result.stderr);
      } else {
        print('✅ Passed: $testFile');
      }
    }
  }

  static Future<void> _runWidgetTests(TestConfig config) async {
    print('\n🎨 Running Widget Tests...');
    print('===========================');
    
    final testFiles = await _findTestFiles('$testDir/widget');
    
    for (final testFile in testFiles) {
      print('Running: $testFile');
      
      final result = await _runFlutterTest(
        testFile,
        coverage: config.generateCoverage,
        timeout: '60s',
      );
      
      if (result.exitCode != 0) {
        print('❌ Failed: $testFile');
        print(result.stderr);
      } else {
        print('✅ Passed: $testFile');
      }
    }
    
    if (config.runResponsive) {
      await _runResponsiveTests();
    }
    
    if (config.runAccessibility) {
      await _runAccessibilityTests();
    }
  }

  static Future<void> _runIntegrationTests(TestConfig config) async {
    print('\n🔗 Running Integration Tests...');
    print('=================================');
    
    final testFiles = await _findTestFiles(integrationTestDir);
    
    for (final testFile in testFiles) {
      print('Running: $testFile');
      
      final result = await _runIntegrationTest(
        testFile,
        device: config.targetDevice,
        headless: config.headless,
      );
      
      if (result.exitCode != 0) {
        print('❌ Failed: $testFile');
        print(result.stderr);
      } else {
        print('✅ Passed: $testFile');
      }
    }
  }

  static Future<void> _runResponsiveTests() async {
    print('\n📱 Running Responsive Design Tests...');
    print('======================================');
    
    final screenSizes = [
      'phone_portrait',
      'phone_landscape', 
      'tablet_portrait',
      'tablet_landscape',
      'desktop_small',
      'desktop_large',
    ];
    
    for (final size in screenSizes) {
      print('Testing screen size: $size');
      
      final result = await _runFlutterTest(
        '$testDir/responsive',
        environment: {'SCREEN_SIZE': size},
      );
      
      if (result.exitCode != 0) {
        print('❌ Failed responsive test for: $size');
      } else {
        print('✅ Passed responsive test for: $size');
      }
    }
  }

  static Future<void> _runAccessibilityTests() async {
    print('\n♿ Running Accessibility Tests...');
    print('==================================');
    
    final result = await _runFlutterTest(
      '$testDir/accessibility',
      timeout: '120s',
    );
    
    if (result.exitCode != 0) {
      print('❌ Failed accessibility tests');
      print(result.stderr);
    } else {
      print('✅ Passed accessibility tests');
    }
  }

  static Future<ProcessResult> _runFlutterTest(
    String testPath, {
    bool coverage = false,
    String timeout = '30s',
    Map<String, String>? environment,
  }) async {
    final args = ['test'];
    
    if (coverage) {
      args.addAll(['--coverage']);
    }
    
    args.addAll([
      '--timeout', timeout,
      testPath,
    ]);
    
    return await Process.run(
      'flutter',
      args,
      environment: environment,
      workingDirectory: projectRoot,
    );
  }

  static Future<ProcessResult> _runIntegrationTest(
    String testPath, {
    String device = 'chrome',
    bool headless = false,
  }) async {
    final args = [
      'drive',
      '--driver=test_driver/integration_test.dart',
      '--target=$testPath',
      '-d', device,
    ];
    
    if (headless && device == 'chrome') {
      args.addAll(['--chrome-args', '--headless']);
    }
    
    return await Process.run(
      'flutter',
      args,
      workingDirectory: projectRoot,
    );
  }

  static Future<void> _generateCoverageReport() async {
    print('\n📊 Generating Coverage Report...');
    print('=================================');
    
    // Convert coverage to LCOV format
    final lcovResult = await Process.run(
      'dart',
      ['run', 'coverage:format_coverage', '--lcov', '--in=coverage', '--out=coverage/lcov.info'],
      workingDirectory: projectRoot,
    );
    
    if (lcovResult.exitCode != 0) {
      print('❌ Failed to generate LCOV report');
      return;
    }
    
    // Generate HTML report
    final htmlResult = await Process.run(
      'genhtml',
      ['coverage/lcov.info', '-o', 'coverage/html'],
      workingDirectory: projectRoot,
    );
    
    if (htmlResult.exitCode == 0) {
      print('✅ Coverage report generated: coverage/html/index.html');
    } else {
      print('⚠️  HTML coverage report generation failed (genhtml not available)');
      print('✅ LCOV report available: coverage/lcov.info');
    }
    
    // Show coverage summary
    await _showCoverageSummary();
  }

  static Future<void> _showCoverageSummary() async {
    final lcovFile = File('coverage/lcov.info');
    if (!await lcovFile.exists()) return;
    
    final content = await lcovFile.readAsString();
    final lines = content.split('\n');
    
    int totalLines = 0;
    int coveredLines = 0;
    
    for (final line in lines) {
      if (line.startsWith('LF:')) {
        totalLines += int.parse(line.substring(3));
      } else if (line.startsWith('LH:')) {
        coveredLines += int.parse(line.substring(3));
      }
    }
    
    if (totalLines > 0) {
      final percentage = (coveredLines / totalLines * 100).toStringAsFixed(1);
      print('📈 Line Coverage: $coveredLines/$totalLines ($percentage%)');
      
      if (double.parse(percentage) >= 90.0) {
        print('🎉 Excellent coverage!');
      } else if (double.parse(percentage) >= 80.0) {
        print('👍 Good coverage');
      } else {
        print('⚠️  Coverage could be improved');
      }
    }
  }

  static Future<void> _showTestSummary() async {
    print('\n📋 Test Summary');
    print('================');
    
    // This would show a summary of all test results
    // For now, just show completion message
    print('✅ All tests completed');
    print('📊 Check coverage report for detailed metrics');
    print('🔍 Review test output above for any failures');
  }

  static Future<List<String>> _findTestFiles(String directory) async {
    final dir = Directory(directory);
    if (!await dir.exists()) return [];
    
    final files = <String>[];
    await for (final entity in dir.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('_test.dart')) {
        files.add(entity.path);
      }
    }
    
    return files;
  }

  static Future<void> _ensureDirectoryExists(String path) async {
    final dir = Directory(path);
    if (!await dir.exists()) {
      await dir.create(recursive: true);
    }
  }

  static Future<void> _cleanDirectory(String path) async {
    final dir = Directory(path);
    if (await dir.exists()) {
      await dir.delete(recursive: true);
    }
  }

  static Future<void> _cleanupTestEnvironment() async {
    print('\n🧹 Cleaning up test environment...');
    
    // Clean temporary files
    await _cleanDirectory('$testDir/temp');
    
    print('✅ Cleanup complete');
  }

  static void _showHelp() {
    print('''
Choice: Once Upon A Time - Test Runner

Usage: dart scripts/run_tests.dart [options]

Options:
  --unit                Run unit tests only
  --widget              Run widget tests only  
  --integration         Run integration tests only
  --all                 Run all tests (default)
  --coverage            Generate coverage report
  --chrome              Run tests on Chrome (for integration tests)
  --responsive          Run responsive design tests
  --accessibility       Run accessibility tests
  --performance         Run performance tests
  --headless            Run browser tests in headless mode
  --help                Show this help message

Examples:
  dart scripts/run_tests.dart --all --coverage
  dart scripts/run_tests.dart --unit --widget
  dart scripts/run_tests.dart --integration --chrome
  dart scripts/run_tests.dart --responsive --accessibility
''');
  }
}

class TestConfig {
  final bool runUnit;
  final bool runWidget;
  final bool runIntegration;
  final bool generateCoverage;
  final bool runResponsive;
  final bool runAccessibility;
  final bool runPerformance;
  final String targetDevice;
  final bool headless;
  final bool showHelp;

  TestConfig({
    this.runUnit = false,
    this.runWidget = false,
    this.runIntegration = false,
    this.generateCoverage = false,
    this.runResponsive = false,
    this.runAccessibility = false,
    this.runPerformance = false,
    this.targetDevice = 'chrome',
    this.headless = false,
    this.showHelp = false,
  });

  factory TestConfig.fromArgs(List<String> args) {
    final runAll = args.isEmpty || args.contains('--all');
    
    return TestConfig(
      runUnit: runAll || args.contains('--unit'),
      runWidget: runAll || args.contains('--widget'),
      runIntegration: runAll || args.contains('--integration'),
      generateCoverage: args.contains('--coverage'),
      runResponsive: args.contains('--responsive'),
      runAccessibility: args.contains('--accessibility'),
      runPerformance: args.contains('--performance'),
      targetDevice: args.contains('--chrome') ? 'chrome' : 'chrome',
      headless: args.contains('--headless'),
      showHelp: args.contains('--help'),
    );
  }
}

void main(List<String> args) async {
  await TestRunner.main(args);
}
