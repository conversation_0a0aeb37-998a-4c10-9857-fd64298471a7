import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/active_child_provider.dart';
import '../../auth/providers/parent_auth_provider.dart';
import '../../../shared_kernel/theme/app_theme.dart';

/// Widget that shows the current sync status
class SyncStatusIndicator extends StatefulWidget {
  /// Whether to show as a compact indicator
  final bool compact;

  /// Constructor
  const SyncStatusIndicator({
    super.key,
    this.compact = false,
  });

  @override
  State<SyncStatusIndicator> createState() => _SyncStatusIndicatorState();
}

class _SyncStatusIndicatorState extends State<SyncStatusIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;
  bool _isSyncing = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.linear,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Trigger manual sync
  Future<void> _triggerSync() async {
    if (_isSyncing) return;

    setState(() {
      _isSyncing = true;
    });

    _animationController.repeat();

    try {
      final activeChildProvider = Provider.of<ActiveChildProvider>(context, listen: false);
      await activeChildProvider.syncWithCloud();
      
      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('Sync completed successfully'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('Sync failed: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
        _animationController.stop();
        _animationController.reset();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ParentAuthProvider, ActiveChildProvider>(
      builder: (context, authProvider, activeChildProvider, child) {
        // Don't show if not authenticated
        if (!authProvider.isAuthenticated) {
          return const SizedBox.shrink();
        }

        // Don't show if no active child
        if (activeChildProvider.activeProfile == null) {
          return const SizedBox.shrink();
        }

        if (widget.compact) {
          return _buildCompactIndicator(activeChildProvider);
        } else {
          return _buildFullIndicator(activeChildProvider);
        }
      },
    );
  }

  Widget _buildCompactIndicator(ActiveChildProvider activeChildProvider) {
    return GestureDetector(
      onTap: activeChildProvider.isCloudSyncAvailable ? _triggerSync : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _getSyncStatusColor().withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getSyncStatusColor(),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedBuilder(
              animation: _rotationAnimation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _isSyncing ? _rotationAnimation.value * 2 * 3.14159 : 0,
                  child: Icon(
                    _getSyncStatusIcon(),
                    size: 16,
                    color: _getSyncStatusColor(),
                  ),
                );
              },
            ),
            const SizedBox(width: 4),
            Text(
              _getSyncStatusText(),
              style: TextStyle(
                fontSize: 12,
                color: _getSyncStatusColor(),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFullIndicator(ActiveChildProvider activeChildProvider) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                AnimatedBuilder(
                  animation: _rotationAnimation,
                  builder: (context, child) {
                    return Transform.rotate(
                      angle: _isSyncing ? _rotationAnimation.value * 2 * 3.14159 : 0,
                      child: Icon(
                        _getSyncStatusIcon(),
                        color: _getSyncStatusColor(),
                      ),
                    );
                  },
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Cloud Sync',
                        style: AppTheme.subheadingStyle.copyWith(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _getSyncStatusText(),
                        style: TextStyle(
                          color: _getSyncStatusColor(),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                if (activeChildProvider.isCloudSyncAvailable && !_isSyncing)
                  TextButton(
                    onPressed: _triggerSync,
                    child: const Text('Sync Now'),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            FutureBuilder<DateTime?>(
              future: activeChildProvider.getLastSyncTimestamp(),
              builder: (context, snapshot) {
                if (snapshot.hasData && snapshot.data != null) {
                  return Text(
                    'Last synced: ${_formatSyncTime(snapshot.data!)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  );
                }
                return const Text(
                  'Never synced',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  IconData _getSyncStatusIcon() {
    if (_isSyncing) {
      return Icons.sync;
    } else if (!Provider.of<ActiveChildProvider>(context, listen: false).isCloudSyncAvailable) {
      return Icons.cloud_off;
    } else {
      return Icons.cloud_done;
    }
  }

  Color _getSyncStatusColor() {
    if (_isSyncing) {
      return Colors.blue;
    } else if (!Provider.of<ActiveChildProvider>(context, listen: false).isCloudSyncAvailable) {
      return Colors.grey;
    } else {
      return Colors.green;
    }
  }

  String _getSyncStatusText() {
    if (_isSyncing) {
      return 'Syncing...';
    } else if (!Provider.of<ActiveChildProvider>(context, listen: false).isCloudSyncAvailable) {
      return 'Offline';
    } else {
      return 'Synced';
    }
  }

  String _formatSyncTime(DateTime syncTime) {
    final now = DateTime.now();
    final difference = now.difference(syncTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

/// Floating sync button for quick access
class FloatingSyncButton extends StatelessWidget {
  const FloatingSyncButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<ParentAuthProvider, ActiveChildProvider>(
      builder: (context, authProvider, activeChildProvider, child) {
        // Don't show if not authenticated or no cloud sync available
        if (!authProvider.isAuthenticated || !activeChildProvider.isCloudSyncAvailable) {
          return const SizedBox.shrink();
        }

        return FloatingActionButton.small(
          onPressed: () async {
            try {
              await activeChildProvider.syncWithCloud();
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Sync completed'),
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            } catch (e) {
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Sync failed: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          },
          tooltip: 'Sync with Cloud',
          child: const Icon(Icons.cloud_sync),
        );
      },
    );
  }
}
