^C:\USERS\<USER>\ONEDRIVE\DESKTOP\VS CODE\CHOICE\CHOICE_ONCE_UPON_A_TIME\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"D:\Install\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SC:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/windows" "-BC:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64" --check-stamp-file "C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64/flutter/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
