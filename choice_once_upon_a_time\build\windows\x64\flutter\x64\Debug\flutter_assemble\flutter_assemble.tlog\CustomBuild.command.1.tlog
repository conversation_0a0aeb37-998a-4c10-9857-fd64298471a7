^C:\USERS\<USER>\ONEDRIVE\DESKTOP\VS CODE\CHOICE\CHOICE_ONCE_UPON_A_TIME\BUILD\WINDOWS\X64\CMAKEFILES\86DE92941C5B6701E69950DB29F15E48\FLUTTER_WINDOWS.DLL.RULE
setlocal
"D:\Install\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\Install\flutter "PROJECT_DIR=C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time" FLUTTER_ROOT=D:\Install\flutter "FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral" "PROJECT_DIR=C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time" "FLUTTER_TARGET=C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\lib\main.dart" DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false "PACKAGE_CONFIG=C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\.dart_tool\package_config.json" D:/Install/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\VS CODE\CHOICE\CHOICE_ONCE_UPON_A_TIME\BUILD\WINDOWS\X64\CMAKEFILES\00618CADB3B30E46DC2643DFA175746F\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\VS CODE\CHOICE\CHOICE_ONCE_UPON_A_TIME\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"D:\Install\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SC:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/windows" "-BC:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64" --check-stamp-file "C:/Users/<USER>/OneDrive/Desktop/VS Code/choice/choice_once_upon_a_time/build/windows/x64/flutter/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
