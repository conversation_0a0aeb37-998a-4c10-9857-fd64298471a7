import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import '../services/iap_service.dart';
import '../../auth/providers/parent_auth_provider.dart';

/// Provider for managing in-app purchases and premium content
class PurchaseProvider extends ChangeNotifier {
  final IAPService _iapService = IAPService();
  final ParentAuthProvider? _authProvider;

  /// Purchase state
  bool _isInitialized = false;
  bool _isPremiumUnlocked = false;
  bool _isLoading = false;
  String? _errorMessage;

  /// Available products
  List<ProductDetails> _products = [];

  /// Purchase stream subscription
  StreamSubscription<PurchaseDetails>? _purchaseSubscription;

  /// Unlocked content IDs
  Set<String> _unlockedContent = {};

  /// Constructor
  PurchaseProvider(this._authProvider) {
    _initialize();
  }

  /// Getters
  bool get isInitialized => _isInitialized;
  bool get isPremiumUnlocked => _isPremiumUnlocked;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  List<ProductDetails> get products => _products;
  List<ProductDetails> get premiumProducts => _iapService.premiumProducts;
  List<ProductDetails> get contentPackProducts => _iapService.contentPackProducts;
  Set<String> get unlockedContent => _unlockedContent;

  /// Initialize the purchase system
  Future<void> _initialize() async {
    try {
      _isLoading = true;
      notifyListeners();

      final success = await _iapService.initialize();
      if (success) {
        _isInitialized = true;
        _products = _iapService.products;

        // Set up purchase listener
        _purchaseSubscription = _iapService.purchaseStream.listen(_handlePurchaseUpdate);

        // Check current purchase status
        await _refreshPurchaseStatus();
      } else {
        _errorMessage = 'Failed to initialize purchase system';
      }
    } catch (e) {
      _errorMessage = 'Error initializing purchases: $e';
      debugPrint(_errorMessage);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Handle purchase updates
  void _handlePurchaseUpdate(PurchaseDetails purchaseDetails) {
    switch (purchaseDetails.status) {
      case PurchaseStatus.pending:
        _isLoading = true;
        _errorMessage = null;
        break;

      case PurchaseStatus.purchased:
      case PurchaseStatus.restored:
        _handleSuccessfulPurchase(purchaseDetails);
        _isLoading = false;
        _errorMessage = null;
        break;

      case PurchaseStatus.error:
        _isLoading = false;
        _errorMessage = _getPurchaseErrorMessage(purchaseDetails);
        break;

      case PurchaseStatus.canceled:
        _isLoading = false;
        _errorMessage = null;
        break;
    }

    notifyListeners();
  }

  /// Handle successful purchase
  void _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) {
    final productId = purchaseDetails.productID;

    // Update premium status
    if (productId == IAPService.premiumMonthlyId ||
        productId == IAPService.premiumYearlyId ||
        productId == IAPService.premiumLifetimeId) {
      _isPremiumUnlocked = true;

      // Update parent profile with premium status
      _updateParentPremiumStatus(true);
    }

    // Update unlocked content
    _unlockedContent.add(productId);

    debugPrint('Purchase successful: $productId');
  }

  /// Update parent premium status in Firebase
  Future<void> _updateParentPremiumStatus(bool isPremium) async {
    if (_authProvider == null || !_authProvider!.isAuthenticated) return;

    try {
      DateTime? expiryDate;

      // Set expiry date based on subscription type
      if (_unlockedContent.contains(IAPService.premiumMonthlyId)) {
        expiryDate = DateTime.now().add(const Duration(days: 30));
      } else if (_unlockedContent.contains(IAPService.premiumYearlyId)) {
        expiryDate = DateTime.now().add(const Duration(days: 365));
      }
      // Lifetime premium has no expiry date (null)

      await _authProvider!.updatePremiumStatus(
        isPremium: isPremium,
        expiryDate: expiryDate,
      );
    } catch (e) {
      debugPrint('Error updating parent premium status: $e');
    }
  }

  /// Purchase a product
  Future<bool> purchaseProduct(String productId) async {
    if (!_isInitialized) {
      _errorMessage = 'Purchase system not initialized';
      notifyListeners();
      return false;
    }

    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final success = await _iapService.purchaseProduct(productId);

      if (!success) {
        _errorMessage = 'Failed to initiate purchase';
        _isLoading = false;
        notifyListeners();
      }

      return success;
    } catch (e) {
      _errorMessage = 'Purchase error: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// Restore purchases
  Future<bool> restorePurchases() async {
    if (!_isInitialized) {
      _errorMessage = 'Purchase system not initialized';
      notifyListeners();
      return false;
    }

    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final success = await _iapService.restorePurchases();

      if (success) {
        await _refreshPurchaseStatus();
      } else {
        _errorMessage = 'Failed to restore purchases';
      }

      _isLoading = false;
      notifyListeners();
      return success;
    } catch (e) {
      _errorMessage = 'Restore error: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// Refresh purchase status
  Future<void> _refreshPurchaseStatus() async {
    try {
      _isPremiumUnlocked = await _iapService.isPremiumUnlocked();

      // Check individual content unlocks
      _unlockedContent.clear();
      for (final product in _products) {
        if (await _iapService.isContentUnlocked(product.id)) {
          _unlockedContent.add(product.id);
        }
      }

      // Update parent profile if premium status changed
      if (_isPremiumUnlocked) {
        await _updateParentPremiumStatus(true);
      }
    } catch (e) {
      debugPrint('Error refreshing purchase status: $e');
    }
  }

  /// Check if specific content is unlocked
  bool isContentUnlocked(String contentId) {
    // Premium unlocks all content
    if (_isPremiumUnlocked) return true;

    // Check specific content unlock
    return _unlockedContent.contains(contentId);
  }

  /// Check if story is unlocked
  bool isStoryUnlocked(String storyId) {
    // Free stories are always available to all users (including guests)
    final freeStoryIds = [
      'corals-lost-colors',
      'the-brave-little-turtle',
    ];

    if (freeStoryIds.contains(storyId)) {
      return true;
    }

    // Guest users can only access free stories
    if (_authProvider?.isGuestMode == true) {
      return false;
    }

    // Premium unlocks all stories for authenticated users
    if (_isPremiumUnlocked) return true;

    // Check if story pack is purchased
    return _unlockedContent.contains(IAPService.storyPackId);
  }

  /// Get product by ID
  ProductDetails? getProduct(String productId) {
    return _iapService.getProduct(productId);
  }

  /// Get premium product with best value
  ProductDetails? getBestValuePremiumProduct() {
    final premiumProducts = _iapService.premiumProducts;

    // Prefer yearly subscription for best value
    for (final product in premiumProducts) {
      if (product.id == IAPService.premiumYearlyId) {
        return product;
      }
    }

    // Fallback to any premium product
    return premiumProducts.isNotEmpty ? premiumProducts.first : null;
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Refresh products and status
  Future<void> refresh() async {
    if (_isInitialized) {
      await _refreshPurchaseStatus();
      notifyListeners();
    }
  }

  /// Enhanced error handling for purchase failures
  String _getPurchaseErrorMessage(PurchaseDetails purchaseDetails) {
    if (purchaseDetails.error == null) return 'Unknown purchase error';

    final error = purchaseDetails.error!;

    switch (error.code) {
      case 'user_canceled':
        return 'Purchase was canceled';
      case 'payment_invalid':
        return 'Payment method is invalid';
      case 'payment_not_allowed':
        return 'Payment not allowed on this device';
      case 'billing_unavailable':
        return 'Billing service is unavailable';
      case 'item_unavailable':
        return 'This item is not available for purchase';
      case 'developer_error':
        return 'There was a configuration error. Please try again later.';
      case 'item_already_owned':
        return 'You already own this item';
      case 'item_not_owned':
        return 'You do not own this item';
      case 'network_error':
        return 'Network error. Please check your connection and try again.';
      default:
        return error.message;
    }
  }

  @override
  void dispose() {
    _purchaseSubscription?.cancel();
    _iapService.dispose();
    super.dispose();
  }
}
