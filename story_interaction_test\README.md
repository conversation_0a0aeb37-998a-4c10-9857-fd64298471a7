# Story Interaction Test Project

A dedicated Flutter Web project for testing the complete Story Interaction functionality of the "Choice: Once Upon A Time" application.

## Overview

This project implements and comprehensively tests the story interaction flow including:

- ✅ **Story Loading & Parsing**: JSON-based story structure with scenes and choices
- ✅ **Text Segmentation**: Breaking narration into manageable chunks
- ✅ **TTS Integration**: Text-to-speech with word-by-word highlighting
- ✅ **Interactive Navigation**: Previous/next controls, auto-advance
- ✅ **Choice System**: Branching narratives with choice popups
- ✅ **Progress Tracking**: Dot-based progress indicators
- ✅ **Settings Panel**: Customizable UI and TTS settings
- ✅ **Story Completion**: End-of-story options and restart functionality
- ✅ **Responsive Design**: Landscape orientation, multiple screen sizes
- ✅ **Robust Image Loading**: Multiple format support with fallbacks

## Architecture

### Core Components

- **Models**: `StoryModel`, `StoryScene`, `StoryChoice`
- **Services**: `TTSService`, `TextSegmenter`
- **Providers**: `StoryProvider`, `StorySettingsProvider`
- **Screens**: `StorySelectionScreen`, `StoryInteractionScreen`
- **Widgets**: `StoryControlPanel`, `ChoicePopup`, `HighlightedText`, etc.

### State Management

Uses Provider pattern for:
- Story state and navigation
- Settings persistence
- TTS control and progress tracking

## Getting Started

### Prerequisites

- Flutter SDK (3.7.2+)
- Chrome browser
- Git

### Installation

1. **Clone and setup:**
   ```bash
   cd story_interaction_test
   flutter pub get
   ```

2. **Run the application:**
   ```bash
   flutter run -d chrome
   ```

3. **Run tests:**
   ```bash
   # Unit and widget tests
   flutter test
   
   # Integration tests in Chrome
   flutter test integration_test/ -d chrome
   ```

## Testing

### Test Coverage

- **Unit Tests**: Story models, text segmentation, navigation logic
- **Widget Tests**: UI components, highlighting, progress indicators
- **Integration Tests**: Complete user flows, choice navigation, settings

### Running Tests

See [test_runner.md](test_runner.md) for detailed testing instructions.

**Quick test commands:**
```bash
# All tests
flutter test

# Integration tests only
flutter test integration_test/ -d chrome

# With coverage
flutter test --coverage
```

## Features Implemented

### Story Flow Management

1. **Scene Navigation**
   - Automatic text segmentation
   - Previous/next controls
   - Auto-advance between scenes
   - Progress tracking with dots

2. **Choice Handling**
   - Interactive choice popups
   - TTS narration of choices
   - Choice history tracking
   - Branching story paths

3. **Story Completion**
   - End-of-story detection
   - Moral lesson display
   - Restart options
   - Choice replay functionality

### User Interface

1. **Responsive Layout**
   - Landscape orientation
   - Full-height background images
   - Bottom control panel
   - Top-right settings access

2. **Interactive Controls**
   - Play/pause narration
   - Manual navigation
   - Settings customization
   - Choice selection

3. **Visual Feedback**
   - Word-by-word highlighting
   - Progress indicators
   - Button animations
   - Loading states

### Settings & Customization

1. **TTS Settings**
   - Speech rate control
   - Volume adjustment
   - Pause/resume functionality

2. **UI Settings**
   - Panel transparency
   - Font size adjustment
   - Auto-advance timing

3. **Accessibility**
   - High contrast options
   - Keyboard navigation support
   - Screen reader compatibility

## Story Format

Stories are defined in JSON format with the following structure:

```json
{
  "storyId": "story-identifier",
  "storyTitle": "Story Title",
  "targetAge": "4-6 years old",
  "moralTheme": "Theme",
  "storyNodes": [
    {
      "sceneId": "scene_id",
      "narrationText": "Scene narration text...",
      "imageAssetPath": "assets/images/story/scene",
      "isChoicePoint": false,
      "choices": [],
      "isEndingScene": false,
      "moralLessonReinforced": "Lesson text"
    }
  ]
}
```

## Development Guidelines

### Code Standards

- Follow Flutter/Dart conventions
- Use Provider for state management
- Implement comprehensive error handling
- Write testable, modular code
- Document public APIs

### Testing Requirements

- Unit tests for all business logic
- Widget tests for UI components
- Integration tests for user flows
- Minimum 80% code coverage

### Performance Considerations

- Efficient image loading with caching
- Optimized text segmentation
- Smooth animations and transitions
- Memory management for long stories

## Browser Compatibility

**Primary Target**: Chrome (latest)
**Secondary**: Firefox, Safari, Edge

**Note**: TTS functionality may vary across browsers. Chrome provides the most consistent experience.

## Known Limitations

1. **TTS Limitations**: Browser-dependent speech synthesis
2. **Image Formats**: Limited to common web formats (PNG, JPG, WebP)
3. **Offline Support**: Requires internet connection for initial load
4. **Mobile**: Optimized for landscape tablets, limited phone support

## Contributing

1. Follow the established architecture patterns
2. Add tests for new functionality
3. Update documentation for API changes
4. Test across different screen sizes
5. Verify Chrome compatibility

## License

This is a test project for the "Choice: Once Upon A Time" application.

## Support

For issues or questions:
1. Check the test documentation
2. Review the integration test scenarios
3. Verify browser compatibility
4. Check console for error messages
