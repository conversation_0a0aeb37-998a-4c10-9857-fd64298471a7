import 'package:flutter/material.dart';
import '../../../shared_kernel/theme/app_theme.dart';

/// Widget for the choice buttons in the story interaction screen
class ChoiceButton extends StatelessWidget {
  /// The text of the choice
  final String text;
  
  /// The action to perform when tapped
  final VoidCallback onTap;
  
  /// Constructor
  const ChoiceButton({
    super.key,
    required this.text,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onTap,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.secondaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(
          horizontal: 24,
          vertical: 16,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 4,
        minimumSize: const Size(double.infinity, 60),
      ),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
