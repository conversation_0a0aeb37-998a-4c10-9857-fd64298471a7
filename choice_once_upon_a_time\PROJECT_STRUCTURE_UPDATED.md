# Choice: Once Upon A Time - Updated Project Structure Documentation
*Updated: December 2024 - Comprehensive Narration Flow Control Implementation*

## Project Overview

**Choice: Once Upon A Time** is a Flutter application designed as an interactive storytelling platform for children with **comprehensive narration flow control**. The project follows a feature-first architecture pattern and uses Provider for state management. The application is optimized for landscape orientation with responsive design supporting both phones and TV/tablets.

### Enhanced Key Features
- Interactive stories with branching narratives
- **Enhanced Text-to-Speech narration** with natural speech delivery and sentence-level pause injection
- **Complete scene narration enforcement** - blocks progression until all segments are narrated
- **Real-time word highlighting** with configurable narration speed (0.3x-1.5x)
- **Choice popup timing control** - only displays after complete narration
- **User-configurable narration settings** - speed, natural pauses, and timing controls
- Responsive landscape-oriented UI design
- Child profile management with save/resume functionality
- Parent authentication system
- **Image caching and preloading optimization** for smooth performance
- **Comprehensive testing coverage** with unit, widget, and integration tests

---

## Enhanced Architecture Components

### **1. Enhanced TTSService with Natural Speech Delivery**

**Location**: `lib/src/features/story/services/tts_service.dart`

**Key Enhancements:**
- **Sentence-level pause injection** with configurable timing (200-2000ms)
- **Natural speech delivery** with automatic pauses between sentences (default: 650ms)
- **Configurable narration speed** (0.3x to 1.5x range)
- **Proper completion detection** using `Completer<void>` for accurate timing
- **Word and sentence tracking** for real-time highlighting

**New Methods:**
```dart
// Natural pause configuration
setNaturalPauseSettings({bool? useNaturalPauses, int? sentencePauseDuration, int? segmentPauseDuration})
getNarrationSettings() -> Map<String, dynamic>

// Enhanced speech delivery
_splitIntoSentences(String text) -> List<String>
_speakWithNaturalPauses() -> Future<void>

// Enhanced tracking
int get currentSentenceIndex
List<String> get sentences
double get narrationSpeed
bool get useNaturalPauses
```

### **2. Enhanced StoryProvider with Complete Scene Narration Enforcement**

**Location**: `lib/src/features/story/providers/story_provider.dart`

**Key Enhancements:**
- **Complete scene narration enforcement** - blocks progression until ALL segments are narrated
- **Navigation blocking** - prevents premature scene transitions
- **Choice popup timing control** - only shows choices after complete narration
- **Enhanced state management** with proper completion tracking

**New State Properties:**
```dart
bool get isSceneNarrationComplete  // Tracks if entire scene is narrated
bool get isNavigationBlocked       // Prevents premature navigation
```

**Enhanced Methods:**
```dart
// Enhanced narration completion with scene-level tracking
void completeNarration()           // Now enforces complete scene narration
void _handleSceneCompletion()      // Only proceeds when narration is complete

// Enhanced navigation with blocking
bool navigateToNextSegment()       // Respects navigation blocking
```

### **3. Enhanced StorySettingsProvider with Narration Controls**

**Location**: `lib/src/features/story/providers/story_settings_provider.dart`

**Key Enhancements:**
- **Natural pause controls** - enable/disable and configure timing
- **Sentence pause duration** controls (200-2000ms range)
- **Segment pause duration** controls (500-3000ms range)
- **Real-time TTS synchronization** for immediate setting updates

**New Settings:**
```dart
bool get useNaturalPauses          // Enable/disable natural pauses
int get sentencePauseDuration      // Pause between sentences (ms)
int get segmentPauseDuration       // Pause between segments (ms)

// Configuration methods
void setUseNaturalPauses(bool enabled)
void setSentencePauseDuration(int duration)
void setSegmentPauseDuration(int duration)
```

### **4. Enhanced StoryInteractionScreen with Flow Control**

**Location**: `lib/src/features/story_interaction/screens/story_interaction_screen.dart`

**Key Enhancements:**
- **Auto-narration logic** respects navigation blocking
- **TTS settings synchronization** with provider settings
- **Enhanced navigation controls** with proper timing
- **Real-time settings updates** before narration

**New Methods:**
```dart
void _updateTTSSettings()          // Sync settings with providers
```

**Enhanced Logic:**
- Auto-start narration only when `!storyProvider.isNavigationBlocked`
- Update TTS settings before each narration
- Respect complete scene narration enforcement

### **5. Enhanced StorySettingsDialog with User Controls**

**Location**: `lib/src/features/story/widgets/story_settings_dialog.dart`

**Key Enhancements:**
- **Natural pause toggle** - enable/disable natural pauses
- **Sentence pause slider** - adjust timing between sentences (200-2000ms)
- **Real-time TTS updates** - changes apply immediately
- **Enhanced reset functionality** - restore all default settings

**New UI Controls:**
```dart
// Natural pause controls
SwitchListTile for useNaturalPauses
Slider for sentencePauseDuration (when enabled)

// Enhanced reset button
TextButton with comprehensive settings reset including TTS updates
```

---

## Narration Flow Control Architecture

### **Flow Control Sequence:**

1. **Scene Loading**:
   - `StoryProvider.navigateToScene()` sets `isNavigationBlocked = true`
   - `isSceneNarrationComplete = false`
   - Image caching and validation occurs

2. **Narration Start**:
   - `StoryInteractionScreen` calls `_updateTTSSettings()`
   - `TTSService.speak()` begins with natural pause injection
   - Real-time word highlighting starts

3. **Segment Completion**:
   - `TTSService` completes and calls `StoryProvider.completeNarration()`
   - If more segments exist: advance to next segment
   - If last segment: set `isSceneNarrationComplete = true`

4. **Scene Completion**:
   - `_handleSceneCompletion()` only proceeds if `isSceneNarrationComplete = true`
   - For choice points: show choice popup
   - For non-choice scenes: auto-advance to next scene
   - Set `isNavigationBlocked = false`

### **State Management Flow:**

```
Scene Load → Navigation Blocked → Narration Start → Segment Complete → 
More Segments? → [Yes: Next Segment] → [No: Scene Complete] → 
Choice Point? → [Yes: Show Choices] → [No: Auto-advance] → Navigation Unblocked
```

---

## Testing Architecture

### **Enhanced Test Coverage:**

**Unit Tests:**
- `test/unit/tts_service_test.dart` - Enhanced TTS functionality
- `test/unit/story_provider_optimizations_test.dart` - Narration flow control
- `test/unit/text_segmenter_test.dart` - Text processing

**Widget Tests:**
- `test/widget/sentence_progress_indicator_test.dart` - Progress tracking
- `test/widget/action_button_test.dart` - Control responsiveness

**Integration Tests:**
- `test/integration/narration_flow_test.dart` - Complete flow control testing
- `test/integration/story_interaction_test.dart` - End-to-end scenarios

---

## Performance Optimizations

### **Image Loading Optimizations:**
- **Pre-validation** of story assets before loading
- **Image caching per scene** - load once, cache in memory
- **Duplicate loading prevention** with loading state tracking
- **Auto-advance logic** for sequential scenes

### **Narration Optimizations:**
- **Proper async/await handling** for accurate completion detection
- **Sentence splitting optimization** for natural pause injection
- **Real-time settings synchronization** without performance impact
- **Memory-efficient word tracking** for highlighting

---

## Configuration and Settings

### **Default Narration Settings:**
```dart
narrationSpeed: 0.5              // 50% of normal speed
useNaturalPauses: true           // Enable natural pauses
sentencePauseDuration: 650       // 650ms between sentences
segmentPauseDuration: 1000       // 1000ms between segments
```

### **Configurable Ranges:**
```dart
narrationSpeed: 0.3 - 1.5        // 30% to 150% speed
sentencePauseDuration: 200 - 2000 // 200ms to 2000ms
segmentPauseDuration: 500 - 3000  // 500ms to 3000ms
```

---

## Future Enhancement Opportunities

### **Potential Improvements:**
- **Voice selection** - multiple TTS voices
- **Audio effects** - background music, sound effects
- **Advanced highlighting** - sentence-level highlighting
- **Accessibility features** - screen reader integration
- **Performance monitoring** - narration timing analytics

### **Architecture Extensions:**
- **Plugin architecture** for TTS engines
- **Configurable highlighting styles**
- **Advanced pause algorithms** based on punctuation
- **Narration history and replay features**

---

This updated documentation reflects the comprehensive narration flow control implementation that ensures proper sequencing, natural speech delivery, and enhanced user experience while maintaining the established feature-first architecture and responsive design patterns.
