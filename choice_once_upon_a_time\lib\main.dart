import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';

import 'firebase_options.dart';
import 'src/features/auth/screens/parent_auth_screen.dart';
import 'src/features/auth/providers/parent_auth_provider.dart';
import 'src/features/coming_soon/screens/coming_soon_screen.dart';
import 'src/features/main_menu/screens/main_menu_screen.dart';
import 'src/features/monetization/providers/purchase_provider.dart';
import 'src/features/monetization/screens/premium_screen.dart';
import 'src/features/profile/models/child_profile.dart';
import 'src/features/profile/providers/active_child_provider.dart';
import 'src/features/profile/screens/child_profile_screen.dart';
import 'src/features/settings/screens/settings_screen.dart';
import 'src/features/story/providers/story_provider.dart';
import 'src/features/story/providers/story_settings_provider.dart';
import 'src/features/story_interaction/screens/story_interaction_screen.dart';
import 'src/features/story_library/screens/story_library_screen.dart';
import 'src/shared_kernel/theme/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Force landscape orientation
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Create a default child profile for demonstration
    final defaultProfile = ChildProfile(
      id: 'default',
      name: 'Leo',
      parentId: 'demo_parent',
      createdAt: DateTime.now(),
      lastActive: DateTime.now(),
    );

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => ParentAuthProvider(),
        ),
        ChangeNotifierProxyProvider<ParentAuthProvider, PurchaseProvider>(
          create: (context) => PurchaseProvider(
            Provider.of<ParentAuthProvider>(context, listen: false),
          ),
          update: (context, authProvider, previousPurchaseProvider) =>
              previousPurchaseProvider ?? PurchaseProvider(authProvider),
        ),
        ChangeNotifierProvider(
          create: (_) => ActiveChildProvider(defaultProfile: defaultProfile),
        ),
        ChangeNotifierProvider(
          create: (_) => StoryProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => StorySettingsProvider(),
        ),
      ],
      child: MaterialApp(
        title: 'Choice: Once Upon A Time',
        theme: AppTheme.lightTheme,
        initialRoute: MainMenuScreen.routeName,
        routes: {
          MainMenuScreen.routeName: (context) => const MainMenuScreen(),
          StoryLibraryScreen.routeName: (context) => const StoryLibraryScreen(),
          SettingsScreen.routeName: (context) => const SettingsScreen(),
          ParentAuthScreen.routeName: (context) => const ParentAuthScreen(),
          PremiumScreen.routeName: (context) => const PremiumScreen(),
          ChildProfileScreen.routeName: (context) => const ChildProfileScreen(),
          ComingSoonScreen.routeName: (context) => const ComingSoonScreen(
                featureName: 'Feature',
              ),
        },
        onGenerateRoute: (settings) {
          if (settings.name == ComingSoonScreen.routeName) {
            final featureName = settings.arguments as String? ?? 'Feature';
            return MaterialPageRoute(
              builder: (context) => ComingSoonScreen(
                featureName: featureName,
              ),
            );
          } else if (settings.name == StoryInteractionScreen.routeName) {
            final args = settings.arguments as Map<String, dynamic>;
            return MaterialPageRoute(
              builder: (context) => StoryInteractionScreen(
                storyId: args['storyId'],
                initialSceneId: args['initialSceneId'],
              ),
            );
          }
          return null;
        },
      ),
    );
  }
}
