import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';

import 'firebase_options.dart';
import 'src/core/config/app_config.dart';
import 'src/features/auth/screens/parent_auth_screen.dart';
import 'src/features/auth/providers/parent_auth_provider.dart';

import 'src/features/auth/widgets/auth_wrapper.dart';
import 'src/features/coming_soon/screens/coming_soon_screen.dart';
import 'src/features/main_menu/screens/main_menu_screen.dart';
import 'src/features/monetization/providers/purchase_provider.dart';
import 'src/features/monetization/screens/premium_screen.dart';
import 'src/features/profile/models/child_profile.dart';
import 'src/features/profile/providers/active_child_provider.dart';
import 'src/features/profile/screens/child_profile_screen.dart';
import 'src/features/profile/screens/parent_profile_screen.dart';
import 'src/features/settings/screens/settings_screen.dart';
import 'src/features/settings/screens/theme_settings_screen.dart';
import 'src/features/settings/screens/audio_settings_screen.dart';
import 'src/features/settings/providers/settings_provider.dart';
import 'src/features/audio/providers/audio_provider.dart';
import 'src/features/story/providers/story_provider.dart';
import 'src/features/story/providers/story_settings_provider.dart';
import 'src/features/story_interaction/screens/story_interaction_screen.dart';
import 'src/features/story_library/screens/story_library_screen.dart';
import 'src/shared_kernel/theme/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Force landscape orientation
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  // Initialize Firebase only if not using local testing mode
  if (!AppConfig.useLocalTestingMode) {
    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      AppConfig.logInfo('Firebase initialized successfully');
    } catch (e) {
      AppConfig.logError('Failed to initialize Firebase', e);
      // Continue with local testing mode as fallback
    }
  } else {
    AppConfig.logInfo('Using local testing mode - Firebase initialization skipped');
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Create a default child profile for demonstration
    final defaultProfile = ChildProfile(
      id: 'default',
      name: 'Leo',
      parentId: 'demo_parent',
      createdAt: DateTime.now(),
      lastActive: DateTime.now(),
    );

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => ParentAuthProvider(),
        ),
        ChangeNotifierProxyProvider<ParentAuthProvider, PurchaseProvider>(
          create: (context) => PurchaseProvider(
            Provider.of<ParentAuthProvider>(context, listen: false),
          ),
          update: (context, authProvider, previousPurchaseProvider) =>
              previousPurchaseProvider ?? PurchaseProvider(authProvider),
        ),
        ChangeNotifierProvider(
          create: (_) => ActiveChildProvider(defaultProfile: defaultProfile),
        ),
        ChangeNotifierProvider(
          create: (_) => StoryProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => StorySettingsProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => SettingsProvider()..initialize(),
        ),
        ChangeNotifierProvider(
          create: (_) => AudioProvider()..initialize(),
        ),
      ],
      child: MaterialApp(
        title: 'Choice: Once Upon A Time',
        theme: AppTheme.lightTheme,
        initialRoute: '/', // Use initial route instead of home
        routes: {
          '/': (context) => const AuthWrapper(), // Define AuthWrapper for the '/' route
          MainMenuScreen.routeName: (context) => const MainMenuScreen(),
          StoryLibraryScreen.routeName: (context) => const StoryLibraryScreen(),
          SettingsScreen.routeName: (context) => const SettingsScreen(),
          ParentAuthScreen.routeName: (context) => const ParentAuthScreen(),
          PremiumScreen.routeName: (context) => const PremiumScreen(),
          ChildProfileScreen.routeName: (context) => const ChildProfileScreen(),
          ParentProfileScreen.routeName: (context) => const ParentProfileScreen(),
          ThemeSettingsScreen.routeName: (context) => const ThemeSettingsScreen(),
          AudioSettingsScreen.routeName: (context) => const AudioSettingsScreen(),
          ComingSoonScreen.routeName: (context) => const ComingSoonScreen(
                featureName: 'Feature', // Default for direct navigation
              ),
          // Note: StoryInteractionScreen is handled by onGenerateRoute due to arguments
        },
        onGenerateRoute: (settings) {
          // Handle routes that need arguments or custom logic
          if (settings.name == ComingSoonScreen.routeName) {
            final featureName = settings.arguments as String? ?? 'Feature';
            return MaterialPageRoute(
              builder: (context) => ComingSoonScreen(
                featureName: featureName,
              ),
            );
          } else if (settings.name == StoryInteractionScreen.routeName) {
            // Ensure arguments are of the correct type
            if (settings.arguments is Map<String, dynamic>) {
              final args = settings.arguments as Map<String, dynamic>;
              // Check for required arguments
              if (args.containsKey('storyId')) {
                return MaterialPageRoute(
                  builder: (context) => StoryInteractionScreen(
                    storyId: args['storyId'],
                    initialSceneId: args['initialSceneId'], // This can be null
                  ),
                );
              }
            }
            // Fallback or error handling if arguments are incorrect for StoryInteractionScreen
            // For example, navigate to an error screen or a default page
            return MaterialPageRoute(
                builder: (_) => Scaffold(
                        body: Center(
                            child: Text(
                                'Error: Invalid arguments for ${settings.name}'))));
          }
          // If no route matches in routes or onGenerateRoute,
          // Flutter will show an error or you can handle it here.
          return null;
        },
      ),
    );
  }
}