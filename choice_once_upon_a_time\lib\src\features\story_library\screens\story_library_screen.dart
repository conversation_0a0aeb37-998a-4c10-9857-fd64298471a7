import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// Assuming your project structure, adjust paths if necessary
// If your shared_kernel is directly under lib/src:

import '../../story/providers/story_provider.dart';
import '../../monetization/providers/purchase_provider.dart';
import '../../monetization/screens/premium_screen.dart';
import '../widgets/story_card.dart';

/// Screen that displays the library of available stories
class StoryLibraryScreen extends StatefulWidget {
  /// Constructor
  const StoryLibraryScreen({super.key});

  /// Route name for navigation
  static const routeName = '/story-library';

  @override
  State<StoryLibraryScreen> createState() => _StoryLibraryScreenState();
}

class _StoryLibraryScreenState extends State<StoryLibraryScreen> {
  /// Error message (if any)
  String? _errorMessage;
  bool _isMounted = false;

  @override
  void initState() {
    super.initState();
    _isMounted = true;
    // Use addPostFrameCallback to ensure context is available for Provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_isMounted) {
        _loadStories();
      }
    });
  }

  @override
  void dispose() {
    _isMounted = false;
    super.dispose();
  }

  /// Load stories from assets
  Future<void> _loadStories() async {
    // Ensure provider access is within a valid context (e.g., after initState or in build)
    if (!mounted) return;

    try {
      // Get the story provider
      final storyProvider = Provider.of<StoryProvider>(context, listen: false);

      // Optionally clear old data if you want a fresh load every time
      // storyProvider.clearAllStoryData();

      // Load all stories
      await storyProvider.loadAllStories();

      if (!_isMounted) return;
      // Clear any previous error message on successful load
      if (_errorMessage != null) {
        setState(() {
          _errorMessage = null;
        });
      }
    } catch (e) {
      if (!_isMounted) return;
      setState(() {
        _errorMessage = 'Error loading stories: $e';
      });
      debugPrint('Error loading stories: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get screen size to determine grid columns
    final screenWidth = MediaQuery.of(context).size.width;
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    // Determine number of columns based on screen width and orientation
    int crossAxisCount = 2; // Default for phones

    if (isLandscape) {
      if (screenWidth > 1200) {
        crossAxisCount = 4; // Large tablets/TV in landscape
      } else if (screenWidth > 800) {
        crossAxisCount = 3; // Tablets in landscape
      }
    } else {
      if (screenWidth > 600) {
        crossAxisCount = 3; // Tablets in portrait
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Choose Your Adventure!'),
        centerTitle: true,
      ),
      body: Consumer2<StoryProvider, PurchaseProvider>(
        builder: (context, storyProvider, purchaseProvider, child) {
          // Show error if any
          if (_errorMessage != null && !storyProvider.isLoading) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _errorMessage!,
                    style: const TextStyle(fontSize: 18),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadStories,
                    child: const Text('Try Again'),
                  ),
                ],
              ),
            );
          }

          // Show loading indicator
          if (storyProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          // Show empty state if no stories
          if (storyProvider.stories.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'No stories found.',
                    style: TextStyle(fontSize: 18),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadStories, // Allow retrying if empty due to an error not caught by _errorMessage
                    child: const Text('Reload Stories'),
                  ),
                ],
              ),
            );
          }

          // Show stories grid
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                childAspectRatio: 0.7, // Taller than wide
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: storyProvider.stories.length,
              itemBuilder: (context, index) {
                final story = storyProvider.stories[index];
                String? finalCoverImagePath;

                // 1. Try to get a provider-validated explicit cover image path
                final String? validatedExplicitCover = storyProvider.getValidatedStoryCoverPath(story.id);
                if (validatedExplicitCover != null && validatedExplicitCover.isNotEmpty) {
                  finalCoverImagePath = validatedExplicitCover;
                }
                // 2. If no validated explicit cover, check the raw coverImagePath from the model.
                else if (story.coverImagePath != null && story.coverImagePath!.isNotEmpty) {
                  finalCoverImagePath = story.coverImagePath;
                }

                // 3. If still no cover image path, fall back to the first scene's *validated* image.
                if ((finalCoverImagePath == null || finalCoverImagePath.isEmpty) && story.scenes.isNotEmpty) {
                  final firstSceneId = story.scenes.first.id;
                  final validatedSceneInfo = storyProvider.getValidatedImageInfo(firstSceneId);
                  finalCoverImagePath = validatedSceneInfo?.validPath;
                }

                // **NEW: Logging the determined cover image path**
                debugPrint('Story: "${story.title}", Attempting Cover Image Path: "${finalCoverImagePath ?? 'No path determined'}"');

                // Check if story is unlocked based on purchase status
                final isStoryUnlocked = purchaseProvider.isStoryUnlocked(story.id);

                return StoryCard(
                  title: story.title,
                  coverImagePath: finalCoverImagePath ?? '', // Pass the determined path
                  isLocked: !isStoryUnlocked,
                  onTap: () {
                    if (!isStoryUnlocked) {
                      // Show premium screen for unlock
                      Navigator.of(context).pushNamed(PremiumScreen.routeName);
                    } else {
                      // Navigate to story interaction screen
                      Navigator.of(context).pushNamed(
                        '/story-interaction', // Make sure this route is defined in your main.dart
                        arguments: {
                          'storyId': story.id,
                          // Default to first scene, ensure activeStory in provider handles this
                          'initialSceneId': story.scenes.isNotEmpty ? story.scenes.first.id : null,
                        },
                      );
                    }
                  },
                );
              },
            ),
          );
        },
      ),
    );
  }
}