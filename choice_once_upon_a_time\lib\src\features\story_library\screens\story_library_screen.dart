import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// Assuming your project structure, adjust paths if necessary
// If your shared_kernel is directly under lib/src:

import '../../story/providers/story_provider.dart';
import '../../monetization/providers/purchase_provider.dart';
import '../../monetization/screens/premium_screen.dart';
import '../../auth/providers/parent_auth_provider.dart';
import '../../auth/screens/onboarding_screen.dart';
import '../widgets/story_card.dart';

/// Screen that displays the library of available stories
class StoryLibraryScreen extends StatefulWidget {
  /// Constructor
  const StoryLibraryScreen({super.key});

  /// Route name for navigation
  static const routeName = '/story-library';

  @override
  State<StoryLibraryScreen> createState() => _StoryLibraryScreenState();
}

class _StoryLibraryScreenState extends State<StoryLibraryScreen> {
  /// Error message (if any)
  String? _errorMessage;
  bool _isMounted = false;

  @override
  void initState() {
    super.initState();
    _isMounted = true;
    // Use addPostFrameCallback to ensure context is available for Provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_isMounted) {
        _loadStories();
      }
    });
  }

  @override
  void dispose() {
    _isMounted = false;
    super.dispose();
  }

  /// Load stories from assets
  Future<void> _loadStories() async {
    // Ensure provider access is within a valid context (e.g., after initState or in build)
    if (!mounted) return;

    try {
      // Get the story provider
      final storyProvider = Provider.of<StoryProvider>(context, listen: false);

      // Optionally clear old data if you want a fresh load every time
      // storyProvider.clearAllStoryData();

      // Load all stories
      await storyProvider.loadAllStories();

      if (!_isMounted) return;
      // Clear any previous error message on successful load
      if (_errorMessage != null) {
        setState(() {
          _errorMessage = null;
        });
      }
    } catch (e) {
      if (!_isMounted) return;
      setState(() {
        _errorMessage = 'Error loading stories: $e';
      });
      debugPrint('Error loading stories: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get screen size to determine grid columns
    final screenWidth = MediaQuery.of(context).size.width;
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    // Determine number of columns based on screen width and orientation
    int crossAxisCount = 2; // Default for phones

    if (isLandscape) {
      if (screenWidth > 1200) {
        crossAxisCount = 4; // Large tablets/TV in landscape
      } else if (screenWidth > 800) {
        crossAxisCount = 3; // Tablets in landscape
      }
    } else {
      if (screenWidth > 600) {
        crossAxisCount = 3; // Tablets in portrait
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Choose Your Adventure!'),
        centerTitle: true,
        actions: [
          Consumer<ParentAuthProvider>(
            builder: (context, authProvider, child) {
              return Padding(
                padding: const EdgeInsets.only(right: 16),
                child: Center(
                  child: _buildUserStatusChip(authProvider),
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer3<StoryProvider, PurchaseProvider, ParentAuthProvider>(
        builder: (context, storyProvider, purchaseProvider, authProvider, child) {
          // Show error if any
          if (_errorMessage != null && !storyProvider.isLoading) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _errorMessage!,
                    style: const TextStyle(fontSize: 18),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadStories,
                    child: const Text('Try Again'),
                  ),
                ],
              ),
            );
          }

          // Show loading indicator
          if (storyProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          // Show empty state if no stories
          if (storyProvider.stories.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'No stories found.',
                    style: TextStyle(fontSize: 18),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadStories, // Allow retrying if empty due to an error not caught by _errorMessage
                    child: const Text('Reload Stories'),
                  ),
                ],
              ),
            );
          }

          // Show stories grid
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                childAspectRatio: 0.7, // Taller than wide
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: storyProvider.stories.length,
              itemBuilder: (context, index) {
                final story = storyProvider.stories[index];
                String? finalCoverImagePath;

                // 1. Try to get a provider-validated explicit cover image path
                final String? validatedExplicitCover = storyProvider.getValidatedStoryCoverPath(story.id);
                if (validatedExplicitCover != null && validatedExplicitCover.isNotEmpty) {
                  finalCoverImagePath = validatedExplicitCover;
                }
                // 2. If no validated explicit cover, check the raw coverImagePath from the model.
                else if (story.coverImagePath != null && story.coverImagePath!.isNotEmpty) {
                  finalCoverImagePath = story.coverImagePath;
                }

                // 3. If still no cover image path, fall back to the first scene's *validated* image.
                if ((finalCoverImagePath == null || finalCoverImagePath.isEmpty) && story.scenes.isNotEmpty) {
                  final firstSceneId = story.scenes.first.id;
                  final validatedSceneInfo = storyProvider.getValidatedImageInfo(firstSceneId);
                  finalCoverImagePath = validatedSceneInfo?.validPath;
                }

                // **NEW: Logging the determined cover image path**
                debugPrint('Story: "${story.title}", Attempting Cover Image Path: "${finalCoverImagePath ?? 'No path determined'}"');

                // Check if story is unlocked based on purchase status
                final isStoryUnlocked = purchaseProvider.isStoryUnlocked(story.id);

                return StoryCard(
                  title: story.title,
                  coverImagePath: finalCoverImagePath ?? '', // Pass the determined path
                  isLocked: !isStoryUnlocked,
                  onTap: () {
                    if (!isStoryUnlocked) {
                      _handleLockedStoryTap(authProvider, story.title);
                    } else {
                      // Navigate to story interaction screen
                      Navigator.of(context).pushNamed(
                        '/story-interaction', // Make sure this route is defined in your main.dart
                        arguments: {
                          'storyId': story.id,
                          // Default to first scene, ensure activeStory in provider handles this
                          'initialSceneId': story.scenes.isNotEmpty ? story.scenes.first.id : null,
                        },
                      );
                    }
                  },
                );
              },
            ),
          );
        },
      ),
    );
  }

  /// Build user status chip for app bar
  Widget _buildUserStatusChip(ParentAuthProvider authProvider) {
    String statusText;
    Color chipColor;
    IconData statusIcon;

    if (authProvider.isGuestMode) {
      statusText = 'Guest';
      chipColor = Colors.orange;
      statusIcon = Icons.person_outline;
    } else if (authProvider.isPremium) {
      statusText = 'Premium';
      chipColor = Colors.amber;
      statusIcon = Icons.star;
    } else if (authProvider.isAuthenticated) {
      statusText = 'Free Account';
      chipColor = Colors.blue;
      statusIcon = Icons.person;
    } else {
      statusText = 'Not Signed In';
      chipColor = Colors.grey;
      statusIcon = Icons.person_off;
    }

    return Chip(
      avatar: Icon(
        statusIcon,
        size: 16,
        color: Colors.white,
      ),
      label: Text(
        statusText,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      backgroundColor: chipColor,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  /// Handle tap on locked story
  void _handleLockedStoryTap(ParentAuthProvider authProvider, String storyTitle) {
    if (authProvider.isGuestMode) {
      _showGuestUpgradeDialog(storyTitle);
    } else {
      // Show premium screen for authenticated users
      Navigator.of(context).pushNamed('/premium');
    }
  }

  /// Show upgrade dialog for guest users
  void _showGuestUpgradeDialog(String storyTitle) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Account to Continue'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('To read "$storyTitle" and unlock more stories, create a free account.'),
            const SizedBox(height: 16),
            const Text('With a free account you get:'),
            const SizedBox(height: 8),
            const Row(
              children: [
                Icon(Icons.cloud_sync, size: 16, color: Colors.blue),
                SizedBox(width: 8),
                Expanded(child: Text('Cloud sync across devices')),
              ],
            ),
            const SizedBox(height: 4),
            const Row(
              children: [
                Icon(Icons.bookmark, size: 16, color: Colors.blue),
                SizedBox(width: 8),
                Expanded(child: Text('Save reading progress')),
              ],
            ),
            const SizedBox(height: 4),
            const Row(
              children: [
                Icon(Icons.family_restroom, size: 16, color: Colors.blue),
                SizedBox(width: 8),
                Expanded(child: Text('Multiple child profiles')),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Stay as Guest'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const OnboardingScreen(),
                ),
              );
            },
            child: const Text('Create Account'),
          ),
        ],
      ),
    );
  }
}