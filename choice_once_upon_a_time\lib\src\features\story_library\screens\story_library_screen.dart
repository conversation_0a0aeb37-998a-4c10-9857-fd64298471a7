import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../shared_kernel/theme/app_theme.dart';
import '../../../features/story/models/story_model.dart';
import '../../../features/story/providers/story_provider.dart';
import '../widgets/story_card.dart';

/// Screen that displays the library of available stories
class StoryLibraryScreen extends StatefulWidget {
  /// Constructor
  const StoryLibraryScreen({super.key});

  /// Route name for navigation
  static const routeName = '/story-library';

  @override
  State<StoryLibraryScreen> createState() => _StoryLibraryScreenState();
}

class _StoryLibraryScreenState extends State<StoryLibraryScreen> {
  /// Error message (if any)
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadStories();
  }

  /// Load stories from assets
  Future<void> _loadStories() async {
    try {
      // Get the story provider
      final storyProvider = Provider.of<StoryProvider>(context, listen: false);

      // Load all stories
      await storyProvider.loadAllStories();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading stories: $e';
      });
      debugPrint('Error loading stories: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get screen size to determine grid columns
    final screenWidth = MediaQuery.of(context).size.width;
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    // Determine number of columns based on screen width and orientation
    int crossAxisCount = 2; // Default for phones

    if (isLandscape) {
      if (screenWidth > 1200) {
        crossAxisCount = 4; // Large tablets/TV in landscape
      } else if (screenWidth > 800) {
        crossAxisCount = 3; // Tablets in landscape
      }
    } else {
      if (screenWidth > 600) {
        crossAxisCount = 3; // Tablets in portrait
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Choose Your Adventure!'),
        centerTitle: true,
      ),
      body: Consumer<StoryProvider>(
        builder: (context, storyProvider, child) {
          // Show error if any
          if (_errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _errorMessage!,
                    style: const TextStyle(fontSize: 18),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadStories,
                    child: const Text('Try Again'),
                  ),
                ],
              ),
            );
          }

          // Show loading indicator
          if (storyProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          // Show empty state if no stories
          if (storyProvider.stories.isEmpty) {
            return const Center(
              child: Text(
                'No stories found. Please add some stories to the assets/stories directory.',
                style: TextStyle(fontSize: 18),
                textAlign: TextAlign.center,
              ),
            );
          }

          // Show stories grid
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                childAspectRatio: 0.7, // Taller than wide
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: storyProvider.stories.length,
              itemBuilder: (context, index) {
                final story = storyProvider.stories[index];

                // Determine cover image path
                String? coverImagePath = story.coverImagePath;
                if (coverImagePath == null && story.scenes.isNotEmpty) {
                  // Use first scene image as cover if no cover specified
                  coverImagePath = story.scenes.first.imagePath;
                }

                return StoryCard(
                  title: story.title,
                  coverImagePath: coverImagePath ?? '',
                  isLocked: story.isLocked,
                  onTap: () {
                    if (story.isLocked) {
                      // Show unlock prompt
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('This story is locked. Unlock feature coming soon!'),
                        ),
                      );
                    } else {
                      // Navigate to story interaction screen
                      Navigator.of(context).pushNamed(
                        '/story-interaction',
                        arguments: {
                          'storyId': story.id,
                          'initialSceneId': story.scenes.first.id, // Default to first scene
                        },
                      );
                    }
                  },
                );
              },
            ),
          );
        },
      ),
    );
  }
}
