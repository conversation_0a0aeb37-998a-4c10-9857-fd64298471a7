# Project Scripts Documentation: "Choice: Once Upon A Time"

## Introduction

This document provides information about key project scripts/Dart files in the "Choice: Once Upon A Time" Flutter application. It outlines their purpose, use cases, and guidance on when they might need modification. This serves as a reference for current and future development.

## Main Application Logic

### File Path: `lib/main.dart`

**Purpose/Use Case:**
- App entry point
- Root widget setup
- Global configurations (routing, theming, global providers)
- App initialization

**When to Modify:**
- Changes to app initialization
- Root-level navigation setup
- Global theme changes
- Adding/configuring top-level state providers
- Modifying the app's overall structure

## Core Features

### Story Library

#### File Path: `lib/src/features/story_library/screens/story_library_screen.dart`

**Purpose/Use Case:**
- Displays a grid or list of available stories
- Allows users to browse and select stories
- Shows story thumbnails, titles, and brief descriptions

**When to Modify:**
- UI layout changes for the story library
- Adding new filtering or sorting options
- Changing how stories are displayed
- Modifying story card appearance

#### File Path: `lib/src/features/story_library/widgets/story_card.dart`

**Purpose/Use Case:**
- Displays an individual story's thumbnail and information
- <PERSON><PERSON> tapping to select a story

**When to Modify:**
- Changing the appearance of story cards
- Adding new information to display on cards
- Modifying interaction behavior

### Story Interaction

#### File Path: `lib/src/features/story_interaction/screens/story_interaction_screen.dart`

**Purpose/Use Case:**
- Main screen for story narration and interaction
- Displays story scenes with images and text
- Handles user choices and navigation between scenes
- Controls text-to-speech narration

**When to Modify:**
- Changing the layout of the story interaction screen
- Modifying how scenes are displayed
- Updating choice presentation
- Changing narration behavior
- Adding new interactive elements

#### File Path: `lib/src/features/story/widgets/story_control_panel.dart`

**Purpose/Use Case:**
- Provides controls for story narration (play/pause, next/previous)
- Displays current narration text
- Shows progress through the current scene

**When to Modify:**
- Changing control panel appearance
- Adding new controls
- Modifying how text is displayed
- Updating progress indicators

#### File Path: `lib/src/features/story/widgets/choice_popup.dart`

**Purpose/Use Case:**
- Displays choice options at decision points in stories
- Handles selection of choices
- Manages narration of choice options

**When to Modify:**
- Changing choice presentation
- Modifying choice selection behavior
- Updating choice narration

### Settings

#### File Path: `lib/src/features/settings/screens/settings_screen.dart`

**Purpose/Use Case:**
- Allows users to configure app settings
- Controls for text-to-speech settings (speed, volume)
- UI appearance settings
- Accessibility options

**When to Modify:**
- Adding new settings
- Changing settings UI
- Modifying how settings are saved/loaded
- Adding new accessibility features

#### File Path: `lib/src/features/settings/providers/settings_provider.dart`

**Purpose/Use Case:**
- Manages app-wide settings state
- Handles persistence of settings
- Provides settings values to other components

**When to Modify:**
- Adding new settings
- Changing how settings are stored
- Modifying default settings
- Adding validation for settings values

### Profile Management

#### File Path: `lib/src/features/profile/screens/profile_screen.dart`

**Purpose/Use Case:**
- Allows creation and management of child profiles
- Displays profile information
- Handles profile selection

**When to Modify:**
- Changing profile UI
- Adding new profile fields
- Modifying profile creation/editing flow
- Updating profile selection behavior

#### File Path: `lib/src/features/profile/providers/active_child_provider.dart`

**Purpose/Use Case:**
- Manages the currently active child profile
- Handles loading/saving profile data
- Tracks story progress for the active profile

**When to Modify:**
- Changing how profiles are stored
- Modifying profile data structure
- Updating story progress tracking
- Adding new profile-related features

## Core Services

### File Path: `lib/src/features/story/services/tts_service.dart`

**Purpose/Use Case:**
- **Enhanced text-to-speech functionality** with comprehensive narration flow control
- **Natural speech delivery** with sentence-level pause injection and configurable timing
- **Complete narration tracking** using Completer<void> for accurate completion detection
- **Real-time word and sentence highlighting** with progress events
- **Configurable narration settings** (speed: 0.3x-1.5x, pause durations, natural pauses)
- **Proper async/await handling** for reliable narration flow control

**Key Features:**
- `speak()` method with natural pause injection between sentences
- `setNaturalPauseSettings()` for configurable pause behavior
- `getNarrationSettings()` for current configuration retrieval
- Sentence splitting and word tracking for synchronized highlighting
- Pause/resume functionality that respects narration flow

**When to Modify:**
- Changing natural speech delivery behavior
- Updating sentence splitting logic
- Modifying pause timing algorithms
- Adding new narration features (voice selection, effects)
- Fixing narration flow or completion detection issues
- Enhancing word highlighting synchronization

### File Path: `lib/src/features/story/services/text_segmenter.dart`

**Purpose/Use Case:**
- Splits story text into manageable segments
- Handles sentence and paragraph detection
- Provides utilities for text processing

**When to Modify:**
- Changing text segmentation logic
- Improving sentence detection
- Modifying segment length calculation
- Fixing text processing issues

## Data Models

### File Path: `lib/src/features/story/models/story_model.dart`

**Purpose/Use Case:**
- Represents story data structure
- Handles parsing story JSON
- Provides access to story content and metadata

**When to Modify:**
- Changing story data structure
- Adding new story properties
- Modifying JSON parsing
- Updating story navigation logic

### File Path: `lib/src/features/profile/models/child_profile_model.dart`

**Purpose/Use Case:**
- Represents child profile data
- Manages profile preferences and progress
- Handles serialization for storage

**When to Modify:**
- Changing profile data structure
- Adding new profile properties
- Modifying serialization
- Updating progress tracking

## State Management

### File Path: `lib/src/features/story/providers/story_provider.dart`

**Purpose/Use Case:**
- **Central state management** for story interaction with enhanced narration flow control
- **Complete scene narration enforcement** - blocks progression until all segments are narrated
- **Navigation blocking** - prevents premature scene transitions during narration
- **Choice popup timing control** - only shows choices after complete narration
- **Enhanced image caching and preloading** for smooth performance
- **Auto-advance logic** for sequential scenes without explicit linking

**Key Features:**
- `isSceneNarrationComplete` and `isNavigationBlocked` state properties
- Enhanced `completeNarration()` method with proper state transitions
- `_handleSceneCompletion()` with narration completion validation
- Image validation, caching, and duplicate loading prevention
- Automatic scene progression for linear narratives

**When to Modify:**
- Changing narration flow control logic
- Modifying scene completion validation
- Updating navigation blocking behavior
- Adding new story interaction features
- Fixing choice popup timing issues
- Enhancing image loading optimizations

### File Path: `lib/src/features/story/providers/story_settings_provider.dart`

**Purpose/Use Case:**
- **Enhanced story-specific settings** with comprehensive narration controls
- **Narration speed control** (0.3x to 1.5x range) with real-time TTS updates
- **Natural pause settings** - enable/disable and configure timing
- **Sentence and segment pause duration** controls (200-2000ms, 500-3000ms)
- **UI appearance settings** for story screens (font family, size, panel opacity)
- **Real-time settings synchronization** with TTSService

**Key Features:**
- `setNarrationSpeed()` with immediate TTS rate updates
- `setUseNaturalPauses()` for natural speech delivery control
- `setSentencePauseDuration()` and `setSegmentPauseDuration()` for timing control
- Integration with StorySettingsDialog for user-friendly controls
- Persistent settings storage and validation

**When to Modify:**
- Adding new narration control features
- Changing natural pause timing algorithms
- Modifying settings UI integration
- Updating default narration settings
- Adding voice selection or audio effects
- Enhancing settings persistence or validation

## Shared/Common Widgets

### File Path: `lib/src/shared_kernel/widgets/responsive_container.dart`

**Purpose/Use Case:**
- Provides responsive sizing based on screen dimensions
- Adapts to different device sizes and orientations

**When to Modify:**
- Improving responsiveness
- Adding new responsive behaviors
- Fixing layout issues on specific devices

### File Path: `lib/src/shared_kernel/widgets/animated_button.dart`

**Purpose/Use Case:**
- Reusable animated button component
- Provides consistent button behavior and appearance
- Handles different button states (enabled, disabled, pressed)

**When to Modify:**
- Changing button appearance
- Adding new animation effects
- Modifying button behavior
- Improving accessibility

## Key Configuration Files

### File Path: `pubspec.yaml`

**Purpose/Use Case:**
- Defines project dependencies
- Declares assets (images, sounds, etc.)
- Configures Flutter settings

**When to Modify:**
- Adding/updating packages
- Declaring new assets
- Changing Flutter configuration
- Adding new fonts or resources

### File Path: `android/app/build.gradle`

**Purpose/Use Case:**
- Android-specific build settings
- Defines app ID, version, SDK versions
- Configures Android plugins

**When to Modify:**
- Changing app ID
- Updating version number
- Modifying Android SDK settings
- Adding Android-specific configurations

### File Path: `ios/Runner/Info.plist`

**Purpose/Use Case:**
- iOS-specific settings
- Defines app permissions
- Configures iOS features

**When to Modify:**
- Adding iOS permissions
- Changing app display name
- Configuring iOS-specific features
- Updating iOS settings
