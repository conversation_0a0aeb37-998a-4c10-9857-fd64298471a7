# Project Scripts Documentation: "Choice: Once Upon A Time"

## Introduction

This document provides information about key project scripts/Dart files in the "Choice: Once Upon A Time" Flutter application. It outlines their purpose, use cases, and guidance on when they might need modification. This serves as a reference for current and future development.

## Main Application Logic

### File Path: `lib/main.dart`

**Purpose/Use Case:** 
- App entry point
- Root widget setup
- Global configurations (routing, theming, global providers)
- App initialization

**When to Modify:**
- Changes to app initialization
- Root-level navigation setup
- Global theme changes
- Adding/configuring top-level state providers
- Modifying the app's overall structure

## Core Features

### Story Library

#### File Path: `lib/src/features/story_library/screens/story_library_screen.dart`

**Purpose/Use Case:**
- Displays a grid or list of available stories
- Allows users to browse and select stories
- Shows story thumbnails, titles, and brief descriptions

**When to Modify:**
- UI layout changes for the story library
- Adding new filtering or sorting options
- Changing how stories are displayed
- Modifying story card appearance

#### File Path: `lib/src/features/story_library/widgets/story_card.dart`

**Purpose/Use Case:**
- Displays an individual story's thumbnail and information
- <PERSON><PERSON> tapping to select a story

**When to Modify:**
- Changing the appearance of story cards
- Adding new information to display on cards
- Modifying interaction behavior

### Story Interaction

#### File Path: `lib/src/features/story_interaction/screens/story_interaction_screen.dart`

**Purpose/Use Case:**
- Main screen for story narration and interaction
- Displays story scenes with images and text
- Handles user choices and navigation between scenes
- Controls text-to-speech narration

**When to Modify:**
- Changing the layout of the story interaction screen
- Modifying how scenes are displayed
- Updating choice presentation
- Changing narration behavior
- Adding new interactive elements

#### File Path: `lib/src/features/story/widgets/story_control_panel.dart`

**Purpose/Use Case:**
- Provides controls for story narration (play/pause, next/previous)
- Displays current narration text
- Shows progress through the current scene

**When to Modify:**
- Changing control panel appearance
- Adding new controls
- Modifying how text is displayed
- Updating progress indicators

#### File Path: `lib/src/features/story/widgets/choice_popup.dart`

**Purpose/Use Case:**
- Displays choice options at decision points in stories
- Handles selection of choices
- Manages narration of choice options

**When to Modify:**
- Changing choice presentation
- Modifying choice selection behavior
- Updating choice narration

### Settings

#### File Path: `lib/src/features/settings/screens/settings_screen.dart`

**Purpose/Use Case:**
- Allows users to configure app settings
- Controls for text-to-speech settings (speed, volume)
- UI appearance settings
- Accessibility options

**When to Modify:**
- Adding new settings
- Changing settings UI
- Modifying how settings are saved/loaded
- Adding new accessibility features

#### File Path: `lib/src/features/settings/providers/settings_provider.dart`

**Purpose/Use Case:**
- Manages app-wide settings state
- Handles persistence of settings
- Provides settings values to other components

**When to Modify:**
- Adding new settings
- Changing how settings are stored
- Modifying default settings
- Adding validation for settings values

### Profile Management

#### File Path: `lib/src/features/profile/screens/profile_screen.dart`

**Purpose/Use Case:**
- Allows creation and management of child profiles
- Displays profile information
- Handles profile selection

**When to Modify:**
- Changing profile UI
- Adding new profile fields
- Modifying profile creation/editing flow
- Updating profile selection behavior

#### File Path: `lib/src/features/profile/providers/active_child_provider.dart`

**Purpose/Use Case:**
- Manages the currently active child profile
- Handles loading/saving profile data
- Tracks story progress for the active profile

**When to Modify:**
- Changing how profiles are stored
- Modifying profile data structure
- Updating story progress tracking
- Adding new profile-related features

## Core Services

### File Path: `lib/src/features/story/services/tts_service.dart`

**Purpose/Use Case:**
- Manages text-to-speech functionality
- Handles speaking text, pausing, resuming
- Tracks current word being spoken
- Provides word progress events

**When to Modify:**
- Changing TTS behavior
- Updating word tracking
- Modifying speech settings
- Adding new TTS features
- Fixing narration issues

### File Path: `lib/src/features/story/services/text_segmenter.dart`

**Purpose/Use Case:**
- Splits story text into manageable segments
- Handles sentence and paragraph detection
- Provides utilities for text processing

**When to Modify:**
- Changing text segmentation logic
- Improving sentence detection
- Modifying segment length calculation
- Fixing text processing issues

## Data Models

### File Path: `lib/src/features/story/models/story_model.dart`

**Purpose/Use Case:**
- Represents story data structure
- Handles parsing story JSON
- Provides access to story content and metadata

**When to Modify:**
- Changing story data structure
- Adding new story properties
- Modifying JSON parsing
- Updating story navigation logic

### File Path: `lib/src/features/profile/models/child_profile_model.dart`

**Purpose/Use Case:**
- Represents child profile data
- Manages profile preferences and progress
- Handles serialization for storage

**When to Modify:**
- Changing profile data structure
- Adding new profile properties
- Modifying serialization
- Updating progress tracking

## State Management

### File Path: `lib/src/features/story/providers/story_provider.dart`

**Purpose/Use Case:**
- Central state management for story interaction
- Handles loading stories and scenes
- Manages navigation between scenes
- Tracks story progress and choices

**When to Modify:**
- Changing story navigation logic
- Modifying how stories are loaded
- Updating progress tracking
- Adding new story interaction features
- Fixing story flow issues

### File Path: `lib/src/features/story/providers/story_settings_provider.dart`

**Purpose/Use Case:**
- Manages story-specific settings
- Controls narration speed, volume
- Handles UI appearance for story screens

**When to Modify:**
- Adding new story settings
- Changing default settings
- Modifying setting persistence
- Updating setting validation

## Shared/Common Widgets

### File Path: `lib/src/shared_kernel/widgets/responsive_container.dart`

**Purpose/Use Case:**
- Provides responsive sizing based on screen dimensions
- Adapts to different device sizes and orientations

**When to Modify:**
- Improving responsiveness
- Adding new responsive behaviors
- Fixing layout issues on specific devices

### File Path: `lib/src/shared_kernel/widgets/animated_button.dart`

**Purpose/Use Case:**
- Reusable animated button component
- Provides consistent button behavior and appearance
- Handles different button states (enabled, disabled, pressed)

**When to Modify:**
- Changing button appearance
- Adding new animation effects
- Modifying button behavior
- Improving accessibility

## Key Configuration Files

### File Path: `pubspec.yaml`

**Purpose/Use Case:**
- Defines project dependencies
- Declares assets (images, sounds, etc.)
- Configures Flutter settings

**When to Modify:**
- Adding/updating packages
- Declaring new assets
- Changing Flutter configuration
- Adding new fonts or resources

### File Path: `android/app/build.gradle`

**Purpose/Use Case:**
- Android-specific build settings
- Defines app ID, version, SDK versions
- Configures Android plugins

**When to Modify:**
- Changing app ID
- Updating version number
- Modifying Android SDK settings
- Adding Android-specific configurations

### File Path: `ios/Runner/Info.plist`

**Purpose/Use Case:**
- iOS-specific settings
- Defines app permissions
- Configures iOS features

**When to Modify:**
- Adding iOS permissions
- Changing app display name
- Configuring iOS-specific features
- Updating iOS settings
