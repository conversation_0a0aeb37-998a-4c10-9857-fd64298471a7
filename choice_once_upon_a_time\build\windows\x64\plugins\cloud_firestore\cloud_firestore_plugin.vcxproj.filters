﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\cloud_firestore_plugin_c_api.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\cloud_firestore_plugin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\messages.g.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\firestore_codec.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include\cloud_firestore\cloud_firestore_plugin_c_api.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\cloud_firestore_plugin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\messages.g.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\firestore_codec.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\build\windows\x64\generated\cloud_firestore\plugin_version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\VS Code\choice\choice_once_upon_a_time\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{6EBADC12-2F3F-324D-B9DA-6C4FAE78EE8F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{41C86688-83D2-36B4-A927-EE1410F99E56}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
