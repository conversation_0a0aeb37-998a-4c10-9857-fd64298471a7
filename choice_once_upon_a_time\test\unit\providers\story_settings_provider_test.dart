import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../lib/src/features/story/providers/story_settings_provider.dart';
import '../../helpers/test_helpers.dart';

void main() {
  group('StorySettingsProvider', () {
    late StorySettingsProvider settingsProvider;

    setUp(() {
      // Set up SharedPreferences mock
      SharedPreferences.setMockInitialValues({});
      settingsProvider = StorySettingsProvider();
    });

    tearDown(() {
      settingsProvider.dispose();
    });

    group('Initialization', () {
      test('should start with default settings', () {
        expect(settingsProvider.speechRate, 1.0);
        expect(settingsProvider.autoAdvance, true);
        expect(settingsProvider.showSubtitles, true);
        expect(settingsProvider.backgroundMusicEnabled, true);
        expect(settingsProvider.soundEffectsEnabled, true);
        expect(settingsProvider.backgroundMusicVolume, 0.5);
        expect(settingsProvider.soundEffectsVolume, 0.8);
      });

      test('should load settings from storage', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({
          'speech_rate': 1.5,
          'auto_advance': false,
          'show_subtitles': false,
          'background_music_enabled': false,
          'sound_effects_enabled': false,
          'background_music_volume': 0.3,
          'sound_effects_volume': 0.6,
        });

        // Act
        await settingsProvider.loadSettings();

        // Assert
        expect(settingsProvider.speechRate, 1.5);
        expect(settingsProvider.autoAdvance, false);
        expect(settingsProvider.showSubtitles, false);
        expect(settingsProvider.backgroundMusicEnabled, false);
        expect(settingsProvider.soundEffectsEnabled, false);
        expect(settingsProvider.backgroundMusicVolume, 0.3);
        expect(settingsProvider.soundEffectsVolume, 0.6);
      });

      test('should handle missing settings gracefully', () async {
        // Arrange - empty preferences
        SharedPreferences.setMockInitialValues({});

        // Act
        await settingsProvider.loadSettings();

        // Assert - should use defaults
        expect(settingsProvider.speechRate, 1.0);
        expect(settingsProvider.autoAdvance, true);
        expect(settingsProvider.showSubtitles, true);
      });
    });

    group('Speech Settings', () {
      test('should update speech rate', () async {
        // Act
        await settingsProvider.setSpeechRate(1.5);

        // Assert
        expect(settingsProvider.speechRate, 1.5);
      });

      test('should clamp speech rate to valid range', () async {
        // Test minimum
        await settingsProvider.setSpeechRate(0.1);
        expect(settingsProvider.speechRate, 0.3); // Should clamp to minimum

        // Test maximum
        await settingsProvider.setSpeechRate(3.0);
        expect(settingsProvider.speechRate, 1.5); // Should clamp to maximum
      });

      test('should get speech rate options', () {
        // Act
        final options = settingsProvider.getSpeechRateOptions();

        // Assert
        expect(options, contains(0.3));
        expect(options, contains(1.0));
        expect(options, contains(1.5));
        expect(options.length, greaterThan(2));
      });

      test('should format speech rate for display', () {
        // Test various rates
        expect(settingsProvider.formatSpeechRate(0.5), '0.5x');
        expect(settingsProvider.formatSpeechRate(1.0), '1.0x');
        expect(settingsProvider.formatSpeechRate(1.5), '1.5x');
      });
    });

    group('Auto-Advance Settings', () {
      test('should toggle auto advance', () async {
        // Initial state
        expect(settingsProvider.autoAdvance, true);

        // Act
        await settingsProvider.setAutoAdvance(false);

        // Assert
        expect(settingsProvider.autoAdvance, false);
      });

      test('should get auto advance delay', () {
        // Act
        final delay = settingsProvider.getAutoAdvanceDelay();

        // Assert
        expect(delay, isA<Duration>());
        expect(delay.inMilliseconds, greaterThan(0));
      });

      test('should set auto advance delay', () async {
        // Arrange
        const newDelay = Duration(seconds: 3);

        // Act
        await settingsProvider.setAutoAdvanceDelay(newDelay);

        // Assert
        expect(settingsProvider.getAutoAdvanceDelay(), newDelay);
      });
    });

    group('Subtitle Settings', () {
      test('should toggle subtitles', () async {
        // Initial state
        expect(settingsProvider.showSubtitles, true);

        // Act
        await settingsProvider.setShowSubtitles(false);

        // Assert
        expect(settingsProvider.showSubtitles, false);
      });

      test('should get subtitle font size', () {
        // Act
        final fontSize = settingsProvider.getSubtitleFontSize();

        // Assert
        expect(fontSize, isA<double>());
        expect(fontSize, greaterThan(0));
      });

      test('should set subtitle font size', () async {
        // Act
        await settingsProvider.setSubtitleFontSize(20.0);

        // Assert
        expect(settingsProvider.getSubtitleFontSize(), 20.0);
      });

      test('should clamp subtitle font size to valid range', () async {
        // Test minimum
        await settingsProvider.setSubtitleFontSize(5.0);
        expect(settingsProvider.getSubtitleFontSize(), greaterThanOrEqualTo(12.0));

        // Test maximum
        await settingsProvider.setSubtitleFontSize(50.0);
        expect(settingsProvider.getSubtitleFontSize(), lessThanOrEqualTo(24.0));
      });
    });

    group('Audio Settings', () {
      test('should toggle background music', () async {
        // Initial state
        expect(settingsProvider.backgroundMusicEnabled, true);

        // Act
        await settingsProvider.setBackgroundMusicEnabled(false);

        // Assert
        expect(settingsProvider.backgroundMusicEnabled, false);
      });

      test('should set background music volume', () async {
        // Act
        await settingsProvider.setBackgroundMusicVolume(0.7);

        // Assert
        expect(settingsProvider.backgroundMusicVolume, 0.7);
      });

      test('should clamp background music volume', () async {
        // Test minimum
        await settingsProvider.setBackgroundMusicVolume(-0.1);
        expect(settingsProvider.backgroundMusicVolume, 0.0);

        // Test maximum
        await settingsProvider.setBackgroundMusicVolume(1.5);
        expect(settingsProvider.backgroundMusicVolume, 1.0);
      });

      test('should toggle sound effects', () async {
        // Initial state
        expect(settingsProvider.soundEffectsEnabled, true);

        // Act
        await settingsProvider.setSoundEffectsEnabled(false);

        // Assert
        expect(settingsProvider.soundEffectsEnabled, false);
      });

      test('should set sound effects volume', () async {
        // Act
        await settingsProvider.setSoundEffectsVolume(0.9);

        // Assert
        expect(settingsProvider.soundEffectsVolume, 0.9);
      });

      test('should clamp sound effects volume', () async {
        // Test minimum
        await settingsProvider.setSoundEffectsVolume(-0.1);
        expect(settingsProvider.soundEffectsVolume, 0.0);

        // Test maximum
        await settingsProvider.setSoundEffectsVolume(1.5);
        expect(settingsProvider.soundEffectsVolume, 1.0);
      });
    });

    group('Persistence', () {
      test('should save settings to storage', () async {
        // Arrange
        await settingsProvider.setSpeechRate(1.2);
        await settingsProvider.setAutoAdvance(false);
        await settingsProvider.setShowSubtitles(false);

        // Act
        await settingsProvider.saveSettings();

        // Assert - verify settings were saved
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getDouble('speech_rate'), 1.2);
        expect(prefs.getBool('auto_advance'), false);
        expect(prefs.getBool('show_subtitles'), false);
      });

      test('should load settings from storage', () async {
        // Arrange - save settings first
        await settingsProvider.setSpeechRate(0.8);
        await settingsProvider.setBackgroundMusicVolume(0.3);
        await settingsProvider.saveSettings();

        // Create new provider instance
        final newProvider = StorySettingsProvider();

        // Act
        await newProvider.loadSettings();

        // Assert
        expect(newProvider.speechRate, 0.8);
        expect(newProvider.backgroundMusicVolume, 0.3);

        newProvider.dispose();
      });

      test('should handle save errors gracefully', () async {
        // Act & Assert - should not throw
        expect(() async {
          await settingsProvider.saveSettings();
        }, returnsNormally);
      });

      test('should handle load errors gracefully', () async {
        // Act & Assert - should not throw
        expect(() async {
          await settingsProvider.loadSettings();
        }, returnsNormally);
      });
    });

    group('Reset Settings', () {
      test('should reset to defaults', () async {
        // Arrange - change settings
        await settingsProvider.setSpeechRate(1.5);
        await settingsProvider.setAutoAdvance(false);
        await settingsProvider.setShowSubtitles(false);
        await settingsProvider.setBackgroundMusicVolume(0.2);

        // Act
        await settingsProvider.resetToDefaults();

        // Assert
        expect(settingsProvider.speechRate, 1.0);
        expect(settingsProvider.autoAdvance, true);
        expect(settingsProvider.showSubtitles, true);
        expect(settingsProvider.backgroundMusicVolume, 0.5);
      });

      test('should save defaults after reset', () async {
        // Arrange - change settings
        await settingsProvider.setSpeechRate(1.5);
        await settingsProvider.saveSettings();

        // Act
        await settingsProvider.resetToDefaults();

        // Assert - verify defaults were saved
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getDouble('speech_rate'), 1.0);
      });
    });

    group('Settings Validation', () {
      test('should validate speech rate range', () {
        expect(settingsProvider.isValidSpeechRate(0.3), true);
        expect(settingsProvider.isValidSpeechRate(1.0), true);
        expect(settingsProvider.isValidSpeechRate(1.5), true);
        expect(settingsProvider.isValidSpeechRate(0.2), false);
        expect(settingsProvider.isValidSpeechRate(2.0), false);
      });

      test('should validate volume range', () {
        expect(settingsProvider.isValidVolume(0.0), true);
        expect(settingsProvider.isValidVolume(0.5), true);
        expect(settingsProvider.isValidVolume(1.0), true);
        expect(settingsProvider.isValidVolume(-0.1), false);
        expect(settingsProvider.isValidVolume(1.1), false);
      });

      test('should validate font size range', () {
        expect(settingsProvider.isValidFontSize(12.0), true);
        expect(settingsProvider.isValidFontSize(18.0), true);
        expect(settingsProvider.isValidFontSize(24.0), true);
        expect(settingsProvider.isValidFontSize(10.0), false);
        expect(settingsProvider.isValidFontSize(30.0), false);
      });
    });

    group('Settings Export/Import', () {
      test('should export settings to map', () {
        // Arrange
        settingsProvider.setSpeechRate(1.2);
        settingsProvider.setAutoAdvance(false);

        // Act
        final exported = settingsProvider.exportSettings();

        // Assert
        expect(exported['speech_rate'], 1.2);
        expect(exported['auto_advance'], false);
        expect(exported, isA<Map<String, dynamic>>());
      });

      test('should import settings from map', () async {
        // Arrange
        final settings = {
          'speech_rate': 0.8,
          'auto_advance': false,
          'show_subtitles': false,
          'background_music_volume': 0.3,
        };

        // Act
        await settingsProvider.importSettings(settings);

        // Assert
        expect(settingsProvider.speechRate, 0.8);
        expect(settingsProvider.autoAdvance, false);
        expect(settingsProvider.showSubtitles, false);
        expect(settingsProvider.backgroundMusicVolume, 0.3);
      });

      test('should handle invalid import data gracefully', () async {
        // Arrange
        final invalidSettings = {
          'speech_rate': 'invalid',
          'auto_advance': 'not_boolean',
          'invalid_key': 'value',
        };

        // Act & Assert - should not throw
        expect(() async {
          await settingsProvider.importSettings(invalidSettings);
        }, returnsNormally);
      });
    });
  });
}
