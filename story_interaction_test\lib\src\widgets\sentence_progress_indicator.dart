import 'package:flutter/material.dart';

/// Widget that displays progress through sentences using dots
class SentenceProgressIndicator extends StatelessWidget {
  final List<String> sentences;
  final int currentSentenceIndex;
  final double dotSize;
  final double spacing;
  final Color activeDotColor;
  final Color inactiveDotColor;

  const SentenceProgressIndicator({
    super.key,
    required this.sentences,
    required this.currentSentenceIndex,
    this.dotSize = 8.0,
    this.spacing = 4.0,
    this.activeDotColor = Colors.blue,
    this.inactiveDotColor = Colors.grey,
  });

  @override
  Widget build(BuildContext context) {
    if (sentences.isEmpty) {
      return const SizedBox.shrink();
    }

    // If there are too many sentences, use a linear progress indicator instead
    if (sentences.length > 20) {
      return _buildLinearProgress();
    }

    return _buildDotProgress();
  }

  Widget _buildDotProgress() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(sentences.length, (index) {
        final isActive = index == currentSentenceIndex;
        final isCompleted = index < currentSentenceIndex;
        
        return Container(
          margin: EdgeInsets.symmetric(horizontal: spacing / 2),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: isActive ? dotSize * 1.5 : dotSize,
            height: isActive ? dotSize * 1.5 : dotSize,
            decoration: BoxDecoration(
              color: isActive || isCompleted ? activeDotColor : inactiveDotColor,
              shape: BoxShape.circle,
              border: isActive
                  ? Border.all(color: activeDotColor.withAlpha(128), width: 2)
                  : null,
            ),
          ),
        );
      }),
    );
  }

  Widget _buildLinearProgress() {
    final progress = sentences.isEmpty ? 0.0 : (currentSentenceIndex + 1) / sentences.length;
    
    return Container(
      height: 6,
      decoration: BoxDecoration(
        color: inactiveDotColor.withAlpha(128),
        borderRadius: BorderRadius.circular(3),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(3),
        child: LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.transparent,
          valueColor: AlwaysStoppedAnimation<Color>(activeDotColor),
        ),
      ),
    );
  }
}
