import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../../../lib/src/features/story/providers/story_provider.dart';
import '../../../lib/src/features/story/models/story.dart';
import '../../../lib/src/features/story/models/scene.dart';
import '../../helpers/test_helpers.dart';
import '../../mocks/mock_services.dart';

void main() {
  group('StoryProvider', () {
    late StoryProvider storyProvider;

    setUp(() {
      storyProvider = StoryProvider();
    });

    tearDown(() {
      storyProvider.dispose();
    });

    group('Initialization', () {
      test('should start with default state', () {
        expect(storyProvider.stories, isEmpty);
        expect(storyProvider.isLoading, false);
        expect(storyProvider.activeStory, isNull);
        expect(storyProvider.currentScene, isNull);
        expect(storyProvider.errorMessage, isNull);
      });

      test('should load stories successfully', () async {
        // Arrange
        final testStories = [
          TestHelpers.createMockStory(id: 'story1', title: 'Story 1'),
          TestHelpers.createMockStory(id: 'story2', title: 'Story 2'),
        ];

        // Act
        await storyProvider.loadStories();

        // Assert
        expect(storyProvider.isLoading, false);
        expect(storyProvider.errorMessage, isNull);
        // In real implementation, this would load from assets or API
      });

      test('should handle loading errors', () async {
        // This would test error scenarios during story loading
        // For now, verify the provider handles errors gracefully
        
        // Act & Assert - should not throw
        expect(() async {
          await storyProvider.loadStories();
        }, returnsNormally);
      });
    });

    group('Story Management', () {
      late Story testStory;

      setUp(() {
        testStory = TestHelpers.createMockStory(
          id: 'test-story',
          title: 'Test Story',
          scenes: [
            TestHelpers.createMockScene(
              id: 'scene1',
              text: 'First scene',
              choices: [
                Choice(id: 'choice1', text: 'Choice 1', nextSceneId: 'scene2'),
                Choice(id: 'choice2', text: 'Choice 2', nextSceneId: 'scene3'),
              ],
            ),
            TestHelpers.createMockScene(
              id: 'scene2',
              text: 'Second scene',
              choices: [],
            ),
            TestHelpers.createMockScene(
              id: 'scene3',
              text: 'Third scene',
              choices: [],
            ),
          ],
        );
      });

      test('should set active story', () {
        // Act
        storyProvider.setActiveStory(testStory);

        // Assert
        expect(storyProvider.activeStory, equals(testStory));
        expect(storyProvider.currentScene, equals(testStory.scenes.first));
      });

      test('should navigate to scene by ID', () {
        // Arrange
        storyProvider.setActiveStory(testStory);

        // Act
        final success = storyProvider.navigateToScene('scene2');

        // Assert
        expect(success, true);
        expect(storyProvider.currentScene?.id, 'scene2');
      });

      test('should handle invalid scene navigation', () {
        // Arrange
        storyProvider.setActiveStory(testStory);

        // Act
        final success = storyProvider.navigateToScene('invalid-scene');

        // Assert
        expect(success, false);
        expect(storyProvider.currentScene?.id, 'scene1'); // Should stay on current scene
      });

      test('should make choice and navigate', () {
        // Arrange
        storyProvider.setActiveStory(testStory);

        // Act
        final success = storyProvider.makeChoice('choice1');

        // Assert
        expect(success, true);
        expect(storyProvider.currentScene?.id, 'scene2');
      });

      test('should handle invalid choice', () {
        // Arrange
        storyProvider.setActiveStory(testStory);

        // Act
        final success = storyProvider.makeChoice('invalid-choice');

        // Assert
        expect(success, false);
        expect(storyProvider.currentScene?.id, 'scene1'); // Should stay on current scene
      });

      test('should get story by ID', () {
        // Arrange
        storyProvider.setActiveStory(testStory);

        // Act
        final foundStory = storyProvider.getStoryById('test-story');

        // Assert
        expect(foundStory, equals(testStory));
      });

      test('should return null for non-existent story', () {
        // Act
        final foundStory = storyProvider.getStoryById('non-existent');

        // Assert
        expect(foundStory, isNull);
      });
    });

    group('Scene Navigation', () {
      late Story testStory;

      setUp(() {
        testStory = TestHelpers.createMockStory(
          scenes: [
            TestHelpers.createMockScene(
              id: 'scene1',
              choices: [
                Choice(id: 'choice1', text: 'Go to scene 2', nextSceneId: 'scene2'),
              ],
            ),
            TestHelpers.createMockScene(
              id: 'scene2',
              choices: [
                Choice(id: 'choice2', text: 'Go to scene 3', nextSceneId: 'scene3'),
              ],
            ),
            TestHelpers.createMockScene(
              id: 'scene3',
              choices: [], // Ending scene
            ),
          ],
        );
        storyProvider.setActiveStory(testStory);
      });

      test('should check if can go to next scene', () {
        // Should be true when there are choices
        expect(storyProvider.canGoToNextScene(), true);

        // Navigate to ending scene
        storyProvider.makeChoice('choice1');
        storyProvider.makeChoice('choice2');

        // Should be false at ending scene
        expect(storyProvider.canGoToNextScene(), false);
      });

      test('should check if can go to previous scene', () {
        // Should be false at first scene
        expect(storyProvider.canGoToPreviousScene(), false);

        // Navigate forward
        storyProvider.makeChoice('choice1');

        // Should be true after navigation
        expect(storyProvider.canGoToPreviousScene(), true);
      });

      test('should go to previous scene', () {
        // Arrange - navigate forward first
        storyProvider.makeChoice('choice1');
        expect(storyProvider.currentScene?.id, 'scene2');

        // Act
        final success = storyProvider.goToPreviousScene();

        // Assert
        expect(success, true);
        expect(storyProvider.currentScene?.id, 'scene1');
      });

      test('should not go to previous scene when at first scene', () {
        // Act
        final success = storyProvider.goToPreviousScene();

        // Assert
        expect(success, false);
        expect(storyProvider.currentScene?.id, 'scene1');
      });

      test('should check if story is completed', () {
        // Should be false at beginning
        expect(storyProvider.isStoryCompleted(), false);

        // Navigate to ending scene
        storyProvider.makeChoice('choice1');
        storyProvider.makeChoice('choice2');

        // Should be true at ending scene
        expect(storyProvider.isStoryCompleted(), true);
      });

      test('should get scene progress percentage', () {
        // At first scene
        expect(storyProvider.getSceneProgressPercentage(), closeTo(0.33, 0.01));

        // Navigate forward
        storyProvider.makeChoice('choice1');
        expect(storyProvider.getSceneProgressPercentage(), closeTo(0.67, 0.01));

        // Navigate to end
        storyProvider.makeChoice('choice2');
        expect(storyProvider.getSceneProgressPercentage(), 1.0);
      });
    });

    group('Choice Management', () {
      late Story testStory;

      setUp(() {
        testStory = TestHelpers.createMockStory(
          scenes: [
            TestHelpers.createMockScene(
              id: 'scene1',
              choices: [
                Choice(id: 'choice1', text: 'Option 1', nextSceneId: 'scene2'),
                Choice(id: 'choice2', text: 'Option 2', nextSceneId: 'scene3'),
              ],
            ),
            TestHelpers.createMockScene(id: 'scene2'),
            TestHelpers.createMockScene(id: 'scene3'),
          ],
        );
        storyProvider.setActiveStory(testStory);
      });

      test('should get available choices', () {
        // Act
        final choices = storyProvider.getAvailableChoices();

        // Assert
        expect(choices.length, 2);
        expect(choices[0].text, 'Option 1');
        expect(choices[1].text, 'Option 2');
      });

      test('should return empty list when no choices available', () {
        // Arrange - navigate to scene without choices
        storyProvider.makeChoice('choice1');

        // Act
        final choices = storyProvider.getAvailableChoices();

        // Assert
        expect(choices, isEmpty);
      });

      test('should track choice history', () {
        // Act
        storyProvider.makeChoice('choice1');

        // Assert
        final history = storyProvider.getChoiceHistory();
        expect(history.length, 1);
        expect(history.first, 'choice1');
      });

      test('should clear choice history when starting new story', () {
        // Arrange - make some choices
        storyProvider.makeChoice('choice1');
        expect(storyProvider.getChoiceHistory(), isNotEmpty);

        // Act - start new story
        final newStory = TestHelpers.createMockStory(id: 'new-story');
        storyProvider.setActiveStory(newStory);

        // Assert
        expect(storyProvider.getChoiceHistory(), isEmpty);
      });
    });

    group('Error Handling', () {
      test('should handle null story gracefully', () {
        // Act & Assert - should not throw
        expect(() => storyProvider.navigateToScene('scene1'), returnsNormally);
        expect(() => storyProvider.makeChoice('choice1'), returnsNormally);
        expect(() => storyProvider.getAvailableChoices(), returnsNormally);
        expect(() => storyProvider.isStoryCompleted(), returnsNormally);
      });

      test('should handle empty scenes list', () {
        // Arrange
        final emptyStory = TestHelpers.createMockStory(scenes: []);

        // Act & Assert - should not throw
        expect(() => storyProvider.setActiveStory(emptyStory), returnsNormally);
        expect(storyProvider.currentScene, isNull);
      });

      test('should clear error message', () {
        // Arrange - set an error
        storyProvider.setError('Test error');
        expect(storyProvider.errorMessage, 'Test error');

        // Act
        storyProvider.clearError();

        // Assert
        expect(storyProvider.errorMessage, isNull);
      });
    });

    group('State Management', () {
      test('should reset state when clearing active story', () {
        // Arrange
        final testStory = TestHelpers.createMockStory();
        storyProvider.setActiveStory(testStory);
        storyProvider.makeChoice('choice1');

        // Act
        storyProvider.clearActiveStory();

        // Assert
        expect(storyProvider.activeStory, isNull);
        expect(storyProvider.currentScene, isNull);
        expect(storyProvider.getChoiceHistory(), isEmpty);
      });

      test('should save and restore state', () {
        // Arrange
        final testStory = TestHelpers.createMockStory();
        storyProvider.setActiveStory(testStory);
        storyProvider.makeChoice('choice1');

        // Act
        final state = storyProvider.saveState();
        storyProvider.clearActiveStory();
        storyProvider.restoreState(state);

        // Assert
        expect(storyProvider.activeStory?.id, testStory.id);
        expect(storyProvider.getChoiceHistory(), isNotEmpty);
      });
    });
  });
}
