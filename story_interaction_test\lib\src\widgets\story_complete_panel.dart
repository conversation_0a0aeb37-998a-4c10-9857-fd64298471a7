import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/story_provider.dart';
import '../services/tts_service.dart';

/// Panel displayed when a story is completed
class StoryCompletePanel extends StatefulWidget {
  const StoryCompletePanel({super.key});

  @override
  State<StoryCompletePanel> createState() => _StoryCompletePanelState();
}

class _StoryCompletePanelState extends State<StoryCompletePanel>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  
  final TTSService _ttsService = TTSService();
  bool _hasNarratedEnding = false;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _animationController.forward();
    
    // Narrate "The End" message
    Future.delayed(const Duration(milliseconds: 800), () {
      _narrateEnding();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<StoryProvider>(
      builder: (context, storyProvider, child) {
        final currentScene = storyProvider.currentScene;
        
        return Stack(
          children: [
            // Backdrop blur
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
              child: Container(
                color: Colors.black.withAlpha(153),
              ),
            ),
            
            // Completion panel
            Center(
              child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: Opacity(
                      opacity: _opacityAnimation.value,
                      child: _buildCompletionDialog(currentScene?.moralLesson),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCompletionDialog(String? moralLesson) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 40),
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.purple[100]!,
            Colors.blue[100]!,
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(77),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Celebration icon
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.yellow[300],
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.celebration,
              size: 48,
              color: Colors.orange,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // "The End" title
          const Text(
            'The End!',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.purple,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 16),
          
          // Moral lesson (if available)
          if (moralLesson != null && moralLesson.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(179),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                moralLesson,
                style: const TextStyle(
                  fontSize: 16,
                  fontStyle: FontStyle.italic,
                  color: Colors.black87,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 24),
          ] else ...[
            const SizedBox(height: 8),
          ],
          
          // Action buttons
          Column(
            children: [
              _buildActionButton(
                icon: Icons.replay,
                label: 'Try That Choice Again?',
                onPressed: _tryLastChoiceAgain,
                color: Colors.orange,
              ),
              
              const SizedBox(height: 12),
              
              _buildActionButton(
                icon: Icons.refresh,
                label: 'Read This Story Again?',
                onPressed: _readStoryAgain,
                color: Colors.blue,
              ),
              
              const SizedBox(height: 12),
              
              _buildActionButton(
                icon: Icons.library_books,
                label: 'Pick a New Story?',
                onPressed: _pickNewStory,
                color: Colors.green,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 4,
        ),
      ),
    );
  }

  Future<void> _narrateEnding() async {
    if (_hasNarratedEnding || !mounted) return;
    
    try {
      await _ttsService.speak('The End! What a wonderful story!');
      _hasNarratedEnding = true;
    } catch (e) {
      debugPrint('Error narrating ending: $e');
    }
  }

  void _tryLastChoiceAgain() {
    final storyProvider = Provider.of<StoryProvider>(context, listen: false);
    _ttsService.stop();
    storyProvider.goBackToLastChoice();
  }

  void _readStoryAgain() {
    final storyProvider = Provider.of<StoryProvider>(context, listen: false);
    _ttsService.stop();
    storyProvider.restartStory();
  }

  void _pickNewStory() {
    _ttsService.stop();
    Navigator.of(context).popUntil((route) => route.isFirst);
  }
}
