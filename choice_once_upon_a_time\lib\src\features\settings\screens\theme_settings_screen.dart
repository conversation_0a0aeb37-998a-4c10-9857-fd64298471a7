import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../../../shared_kernel/theme/app_theme.dart';

/// Screen for theme settings
class ThemeSettingsScreen extends StatelessWidget {
  /// Constructor
  const ThemeSettingsScreen({super.key});

  /// Route name for navigation
  static const routeName = '/settings/theme';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Theme Settings'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Theme mode selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'App Theme',
                        style: AppTheme.subheadingStyle.copyWith(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      // Light theme option
                      _buildThemeOption(
                        context: context,
                        title: 'Light Theme',
                        subtitle: 'Bright and cheerful colors',
                        icon: Icons.light_mode,
                        themeMode: ThemeMode.light,
                        currentMode: settingsProvider.themeMode,
                        onTap: () => settingsProvider.setThemeMode(ThemeMode.light),
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Dark theme option
                      _buildThemeOption(
                        context: context,
                        title: 'Dark Theme',
                        subtitle: 'Easy on the eyes for bedtime stories',
                        icon: Icons.dark_mode,
                        themeMode: ThemeMode.dark,
                        currentMode: settingsProvider.themeMode,
                        onTap: () => settingsProvider.setThemeMode(ThemeMode.dark),
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // System theme option
                      _buildThemeOption(
                        context: context,
                        title: 'System Theme',
                        subtitle: 'Follows your device settings',
                        icon: Icons.settings_system_daydream,
                        themeMode: ThemeMode.system,
                        currentMode: settingsProvider.themeMode,
                        onTap: () => settingsProvider.setThemeMode(ThemeMode.system),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Theme preview
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Preview',
                        style: AppTheme.subheadingStyle.copyWith(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      // Preview content
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Theme.of(context).colorScheme.outline,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.auto_stories,
                                  color: AppTheme.primaryColor,
                                  size: 24,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Story Title',
                                  style: AppTheme.headingStyle.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'Once upon a time, in a magical land far away, there lived a brave little adventurer who loved to explore...',
                              style: AppTheme.bodyStyle,
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                ElevatedButton(
                                  onPressed: () {},
                                  child: const Text('Continue'),
                                ),
                                const SizedBox(width: 12),
                                OutlinedButton(
                                  onPressed: () {},
                                  child: const Text('Go Back'),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Additional theme info
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: AppTheme.primaryColor,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Theme Information',
                            style: AppTheme.subheadingStyle.copyWith(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '• Light Theme: Perfect for daytime reading with bright, vibrant colors\n'
                        '• Dark Theme: Ideal for bedtime stories with gentle, soothing colors\n'
                        '• System Theme: Automatically switches based on your device settings',
                        style: AppTheme.bodyStyle.copyWith(
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Build a theme option tile
  Widget _buildThemeOption({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required ThemeMode themeMode,
    required ThemeMode currentMode,
    required VoidCallback onTap,
  }) {
    final isSelected = themeMode == currentMode;
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected 
              ? AppTheme.primaryColor 
              : Theme.of(context).colorScheme.outline,
            width: isSelected ? 2 : 1,
          ),
          color: isSelected 
            ? AppTheme.primaryColor.withOpacity(0.1)
            : Colors.transparent,
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isSelected 
                  ? AppTheme.primaryColor 
                  : AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: isSelected ? Colors.white : AppTheme.primaryColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTheme.bodyStyle.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? AppTheme.primaryColor : null,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppTheme.bodyStyle.copyWith(
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppTheme.primaryColor,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }
}
