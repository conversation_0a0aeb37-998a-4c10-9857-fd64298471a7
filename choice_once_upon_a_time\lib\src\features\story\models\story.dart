import 'dart:convert';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'scene.dart';

/// Represents a complete story with all its scenes and metadata
class Story extends Equatable {
  /// Unique identifier for the story
  final String id;

  /// Title of the story
  final String title;

  /// Description of the story
  final String description;

  /// Path to the cover image
  final String? coverImagePath;

  /// List of scenes in the story
  final List<Scene> scenes;

  /// Whether this story requires premium access
  final bool isLocked;

  /// Age range for this story (e.g., "3-6", "7-10")
  final String? ageRange;

  /// Estimated reading time in minutes
  final int estimatedMinutes;

  /// Tags/categories for the story
  final List<String> tags;

  /// Author of the story
  final String? author;

  /// When this story was created
  final DateTime createdAt;

  /// When this story was last updated
  final DateTime lastUpdated;

  /// Version of the story
  final String version;

  /// Optional metadata for the story
  final Map<String, dynamic>? metadata;

  /// Creates a new Story
  const Story({
    required this.id,
    required this.title,
    required this.description,
    this.coverImagePath,
    this.scenes = const [],
    this.isLocked = false,
    this.ageRange,
    this.estimatedMinutes = 10,
    this.tags = const [],
    this.author,
    required this.createdAt,
    required this.lastUpdated,
    this.version = '1.0.0',
    this.metadata,
  });

  /// Creates a Story from JSON
  factory Story.fromJson(Map<String, dynamic> json) {
    return Story(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      coverImagePath: json['coverImagePath'] as String?,
      scenes: (json['scenes'] as List<dynamic>?)
              ?.map((scene) => Scene.fromJson(scene as Map<String, dynamic>))
              .toList() ??
          [],
      isLocked: json['isLocked'] as bool? ?? false,
      ageRange: json['ageRange'] as String?,
      estimatedMinutes: json['estimatedMinutes'] as int? ?? 10,
      tags: List<String>.from(json['tags'] as List? ?? []),
      author: json['author'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      version: json['version'] as String? ?? '1.0.0',
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Converts the Story to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      if (coverImagePath != null) 'coverImagePath': coverImagePath,
      'scenes': scenes.map((scene) => scene.toJson()).toList(),
      'isLocked': isLocked,
      if (ageRange != null) 'ageRange': ageRange,
      'estimatedMinutes': estimatedMinutes,
      'tags': tags,
      if (author != null) 'author': author,
      'createdAt': createdAt.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
      'version': version,
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// Creates a copy of this Story with the given fields replaced
  Story copyWith({
    String? id,
    String? title,
    String? description,
    String? coverImagePath,
    List<Scene>? scenes,
    bool? isLocked,
    String? ageRange,
    int? estimatedMinutes,
    List<String>? tags,
    String? author,
    DateTime? createdAt,
    DateTime? lastUpdated,
    String? version,
    Map<String, dynamic>? metadata,
  }) {
    return Story(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      coverImagePath: coverImagePath ?? this.coverImagePath,
      scenes: scenes ?? this.scenes,
      isLocked: isLocked ?? this.isLocked,
      ageRange: ageRange ?? this.ageRange,
      estimatedMinutes: estimatedMinutes ?? this.estimatedMinutes,
      tags: tags ?? this.tags,
      author: author ?? this.author,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      version: version ?? this.version,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get the first scene of the story
  Scene get firstScene {
    if (scenes.isEmpty) {
      throw StateError('Story has no scenes');
    }
    return scenes.first;
  }

  /// Get the last scene of the story
  Scene? get lastScene => scenes.isNotEmpty ? scenes.last : null;

  /// Get scene by ID
  Scene? getSceneById(String sceneId) {
    try {
      return scenes.firstWhere((scene) => scene.id == sceneId);
    } catch (e) {
      return null;
    }
  }

  /// Get the next scene after the given scene ID
  Scene? getNextScene(String currentSceneId) {
    final currentIndex = scenes.indexWhere((scene) => scene.id == currentSceneId);
    if (currentIndex == -1 || currentIndex >= scenes.length - 1) {
      return null;
    }
    return scenes[currentIndex + 1];
  }

  /// Get the next scene by array index (for auto-advance)
  Scene? getNextSceneByIndex(String currentSceneId) {
    return getNextScene(currentSceneId);
  }

  /// Load a story from a JSON file
  static Future<Story> fromAsset(String assetPath) async {
    try {
      final String jsonString = await rootBundle.loadString(assetPath);
      final Map<String, dynamic> jsonMap = json.decode(jsonString);
      return Story.fromJson(jsonMap);
    } catch (e) {
      debugPrint('Error loading story from asset: $e');
      rethrow;
    }
  }

  /// Get the previous scene before the given scene ID
  Scene? getPreviousScene(String currentSceneId) {
    final currentIndex = scenes.indexWhere((scene) => scene.id == currentSceneId);
    if (currentIndex <= 0) {
      return null;
    }
    return scenes[currentIndex - 1];
  }

  /// Check if a scene exists in this story
  bool hasScene(String sceneId) {
    return scenes.any((scene) => scene.id == sceneId);
  }

  /// Get all ending scenes
  List<Scene> get endingScenes {
    return scenes.where((scene) => scene.isEnding).toList();
  }

  /// Check if this story has multiple endings
  bool get hasMultipleEndings => endingScenes.length > 1;

  /// Get total number of scenes
  int get sceneCount => scenes.length;

  /// Get total number of choices across all scenes
  int get totalChoices {
    return scenes.fold(0, (total, scene) => total + scene.choices.length);
  }

  /// Check if this story is suitable for a given age
  bool isSuitableForAge(int age) {
    if (ageRange == null) return true;

    final parts = ageRange!.split('-');
    if (parts.length != 2) return true;

    final minAge = int.tryParse(parts[0]);
    final maxAge = int.tryParse(parts[1]);

    if (minAge == null || maxAge == null) return true;

    return age >= minAge && age <= maxAge;
  }

  /// Check if this story contains a specific tag
  bool hasTag(String tag) {
    return tags.contains(tag.toLowerCase());
  }

  /// Get formatted age range
  String get formattedAgeRange {
    return ageRange ?? 'All ages';
  }

  /// Get formatted reading time
  String get formattedReadingTime {
    if (estimatedMinutes < 60) {
      return '$estimatedMinutes min';
    } else {
      final hours = estimatedMinutes ~/ 60;
      final minutes = estimatedMinutes % 60;
      if (minutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${minutes}m';
      }
    }
  }

  /// Get story difficulty based on scene count and choices
  String get difficulty {
    final totalElements = sceneCount + totalChoices;
    if (totalElements < 10) return 'Easy';
    if (totalElements < 20) return 'Medium';
    return 'Hard';
  }

  /// Check if this story is new (created within last 7 days)
  bool get isNew {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inDays <= 7;
  }

  /// Check if this story was recently updated (within last 30 days)
  bool get isRecentlyUpdated {
    final now = DateTime.now();
    final difference = now.difference(lastUpdated);
    return difference.inDays <= 30;
  }

  /// Validate that the story structure is correct
  bool get isValid {
    // Must have at least one scene
    if (scenes.isEmpty) return false;

    // All scene IDs must be unique
    final sceneIds = scenes.map((scene) => scene.id).toSet();
    if (sceneIds.length != scenes.length) return false;

    // All choice nextSceneIds must reference valid scenes
    for (final scene in scenes) {
      for (final choice in scene.choices) {
        if (!hasScene(choice.nextSceneId)) return false;
      }

      // Check defaultNextSceneId if present
      if (scene.defaultNextSceneId != null && !hasScene(scene.defaultNextSceneId!)) {
        return false;
      }
    }

    return true;
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        coverImagePath,
        scenes,
        isLocked,
        ageRange,
        estimatedMinutes,
        tags,
        author,
        createdAt,
        lastUpdated,
        version,
        metadata,
      ];

  @override
  String toString() {
    return 'Story(id: $id, title: $title, scenes: ${scenes.length}, isLocked: $isLocked)';
  }
}
