import 'dart:convert';
import 'package:flutter/services.dart';

/// Represents a story choice
class StoryChoice {
  final String text;
  final String nextSceneId;

  const StoryChoice({
    required this.text,
    required this.nextSceneId,
  });

  factory StoryChoice.fromJson(Map<String, dynamic> json) {
    return StoryChoice(
      text: json['choiceText'] as String,
      nextSceneId: json['nextSceneId'] as String,
    );
  }
}

/// Represents a story scene
class StoryScene {
  final String id;
  final String narrationText;
  final String imagePath;
  final bool isChoicePoint;
  final String? choicePrompt;
  final List<StoryChoice> choices;
  final bool isEndingScene;
  final String? moralLesson;
  final String? defaultNextSceneId;

  const StoryScene({
    required this.id,
    required this.narrationText,
    required this.imagePath,
    required this.isChoicePoint,
    this.choicePrompt,
    required this.choices,
    required this.isEndingScene,
    this.moralLesson,
    this.defaultNextSceneId,
  });

  factory StoryScene.fromJson(Map<String, dynamic> json) {
    final choicesJson = json['choices'] as List<dynamic>? ?? [];
    final choices = choicesJson
        .map((choice) => StoryChoice.fromJson(choice as Map<String, dynamic>))
        .toList();

    return StoryScene(
      id: json['sceneId'] as String,
      narrationText: json['narrationText'] as String,
      imagePath: json['imageAssetPath'] as String,
      isChoicePoint: json['isChoicePoint'] as bool? ?? false,
      choicePrompt: json['choicePrompt'] as String?,
      choices: choices,
      isEndingScene: json['isEndingScene'] as bool? ?? false,
      moralLesson: json['moralLessonReinforced'] as String?,
      defaultNextSceneId: _findDefaultNextSceneId(json, choices),
    );
  }

  static String? _findDefaultNextSceneId(Map<String, dynamic> json, List<StoryChoice> choices) {
    // If there's only one choice, use it as default
    if (choices.length == 1) {
      return choices.first.nextSceneId;
    }
    
    // Otherwise, look for a defaultNextSceneId field or return null
    return json['defaultNextSceneId'] as String?;
  }
}

/// Represents a complete story
class Story {
  final String id;
  final String title;
  final String targetAge;
  final String moralTheme;
  final List<StoryScene> scenes;
  final StoryScene firstScene;

  const Story({
    required this.id,
    required this.title,
    required this.targetAge,
    required this.moralTheme,
    required this.scenes,
    required this.firstScene,
  });

  factory Story.fromJson(Map<String, dynamic> json) {
    final storyNodesJson = json['storyNodes'] as List<dynamic>;
    final scenes = storyNodesJson
        .map((node) => StoryScene.fromJson(node as Map<String, dynamic>))
        .toList();

    return Story(
      id: json['storyId'] as String,
      title: json['storyTitle'] as String,
      targetAge: json['targetAge'] as String? ?? 'All ages',
      moralTheme: json['moralTheme'] as String? ?? 'General',
      scenes: scenes,
      firstScene: scenes.first,
    );
  }

  /// Load a story from an asset file
  static Future<Story> fromAsset(String assetPath) async {
    final jsonString = await rootBundle.loadString(assetPath);
    final jsonData = json.decode(jsonString) as Map<String, dynamic>;
    return Story.fromJson(jsonData);
  }

  /// Get a scene by its ID
  StoryScene? getSceneById(String sceneId) {
    try {
      return scenes.firstWhere((scene) => scene.id == sceneId);
    } catch (e) {
      return null;
    }
  }
}
