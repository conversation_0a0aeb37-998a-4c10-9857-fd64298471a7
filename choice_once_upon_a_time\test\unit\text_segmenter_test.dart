import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/src/features/story/services/text_segmenter.dart';

void main() {
  group('TextSegmenter', () {
    group('splitIntoSentences', () {
      test('should split text into sentences by period', () {
        const text = 'This is the first sentence. This is the second sentence.';
        final result = TextSegmenter.splitIntoSentences(text);
        
        expect(result.length, 2);
        expect(result[0], 'This is the first sentence.');
        expect(result[1], 'This is the second sentence.');
      });
      
      test('should split text into sentences by exclamation mark', () {
        const text = 'This is exciting! This is also exciting!';
        final result = TextSegmenter.splitIntoSentences(text);
        
        expect(result.length, 2);
        expect(result[0], 'This is exciting!');
        expect(result[1], 'This is also exciting!');
      });
      
      test('should split text into sentences by question mark', () {
        const text = 'Is this a question? Yes, it is.';
        final result = TextSegmenter.splitIntoSentences(text);
        
        expect(result.length, 2);
        expect(result[0], 'Is this a question?');
        expect(result[1], 'Yes, it is.');
      });
      
      test('should handle mixed sentence terminators', () {
        const text = 'Hello! How are you? I am fine.';
        final result = TextSegmenter.splitIntoSentences(text);
        
        expect(result.length, 3);
        expect(result[0], 'Hello!');
        expect(result[1], 'How are you?');
        expect(result[2], 'I am fine.');
      });
      
      test('should return the original text if no sentence terminators are found', () {
        const text = 'This text has no sentence terminators';
        final result = TextSegmenter.splitIntoSentences(text);
        
        expect(result.length, 1);
        expect(result[0], text);
      });
      
      test('should return an empty list for empty text', () {
        const text = '';
        final result = TextSegmenter.splitIntoSentences(text);
        
        expect(result.length, 0);
      });
    });
    
    group('splitIntoSegments', () {
      test('should split long text into segments of approximately equal length', () {
        const text = 'This is a very long sentence that should be split into multiple segments because it exceeds the target length. '
            'This is another sentence that adds to the length of the text. '
            'And here is yet another sentence to make the text even longer.';
        
        final result = TextSegmenter.splitIntoSegments(text, targetLength: 50);
        
        // Verify that we have multiple segments
        expect(result.length, greaterThan(1));
        
        // Verify that each segment is approximately the target length
        for (final segment in result) {
          expect(segment.length, lessThanOrEqualTo(100)); // Allow some flexibility
        }
        
        // Verify that all text is preserved
        final combinedText = result.join(' ');
        expect(combinedText.replaceAll('  ', ' '), text);
      });
      
      test('should handle short text that fits in a single segment', () {
        const text = 'This is a short text.';
        final result = TextSegmenter.splitIntoSegments(text, targetLength: 50);
        
        expect(result.length, 1);
        expect(result[0], text);
      });
      
      test('should return an empty list for empty text', () {
        const text = '';
        final result = TextSegmenter.splitIntoSegments(text);
        
        expect(result.length, 0);
      });
      
      test('should split a very long sentence without natural breaks', () {
        const text = 'ThisIsAVeryLongSentenceWithoutAnyNaturalBreaksOrPunctuationThatShouldBeSplitIntoMultipleSegmentsBecauseItExceedsTheTargetLength';
        final result = TextSegmenter.splitIntoSegments(text, targetLength: 20);
        
        // Verify that we have multiple segments
        expect(result.length, greaterThan(1));
        
        // Verify that all text is preserved
        final combinedText = result.join('');
        expect(combinedText, text);
      });
    });
  });
}
