import 'package:cloud_firestore/cloud_firestore.dart';

/// Model representing story progress for a specific story
class StoryProgress {
  /// Story ID
  final String storyId;

  /// Current scene ID
  final String currentSceneId;

  /// Completed scene IDs
  final List<String> completedScenes;

  /// Choices made throughout the story
  final Map<String, String> choicesMade;

  /// When this progress was last updated
  final DateTime lastUpdated;

  /// Whether the story is completed
  final bool isCompleted;

  /// Total time spent on this story (in minutes)
  final int totalTimeMinutes;

  const StoryProgress({
    required this.storyId,
    required this.currentSceneId,
    required this.completedScenes,
    required this.choicesMade,
    required this.lastUpdated,
    this.isCompleted = false,
    this.totalTimeMinutes = 0,
  });

  factory StoryProgress.fromMap(Map<String, dynamic> map) {
    return StoryProgress(
      storyId: map['storyId'] ?? '',
      currentSceneId: map['currentSceneId'] ?? '',
      completedScenes: List<String>.from(map['completedScenes'] ?? []),
      choicesMade: Map<String, String>.from(map['choicesMade'] ?? {}),
      lastUpdated: map['lastUpdated'] is Timestamp
          ? (map['lastUpdated'] as Timestamp).toDate()
          : DateTime.parse(map['lastUpdated'] ?? DateTime.now().toIso8601String()),
      isCompleted: map['isCompleted'] ?? false,
      totalTimeMinutes: map['totalTimeMinutes'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'storyId': storyId,
      'currentSceneId': currentSceneId,
      'completedScenes': completedScenes,
      'choicesMade': choicesMade,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
      'isCompleted': isCompleted,
      'totalTimeMinutes': totalTimeMinutes,
    };
  }

  StoryProgress copyWith({
    String? storyId,
    String? currentSceneId,
    List<String>? completedScenes,
    Map<String, String>? choicesMade,
    DateTime? lastUpdated,
    bool? isCompleted,
    int? totalTimeMinutes,
  }) {
    return StoryProgress(
      storyId: storyId ?? this.storyId,
      currentSceneId: currentSceneId ?? this.currentSceneId,
      completedScenes: completedScenes ?? this.completedScenes,
      choicesMade: choicesMade ?? this.choicesMade,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isCompleted: isCompleted ?? this.isCompleted,
      totalTimeMinutes: totalTimeMinutes ?? this.totalTimeMinutes,
    );
  }
}

/// Model representing a child's profile in the app
class ChildProfile {
  /// Unique identifier for the profile
  final String id;

  /// Child's name
  final String name;

  /// Parent ID who owns this child profile
  final String parentId;

  /// When the profile was created
  final DateTime createdAt;

  /// When the profile was last active
  final DateTime lastActive;

  /// Story progress for each story
  final Map<String, StoryProgress> storyProgress;

  /// Child's preferences and settings
  final Map<String, dynamic> preferences;

  /// Avatar/character selection
  final String? avatarId;

  /// Age of the child (optional)
  final int? age;

  /// Constructor
  const ChildProfile({
    required this.id,
    required this.name,
    required this.parentId,
    required this.createdAt,
    required this.lastActive,
    this.storyProgress = const {},
    this.preferences = const {},
    this.avatarId,
    this.age,
  });

  /// Create a ChildProfile from Firestore document
  factory ChildProfile.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // Parse story progress
    final progressData = data['storyProgress'] as Map<String, dynamic>? ?? {};
    final storyProgress = <String, StoryProgress>{};
    for (final entry in progressData.entries) {
      storyProgress[entry.key] = StoryProgress.fromMap(entry.value as Map<String, dynamic>);
    }

    return ChildProfile(
      id: doc.id,
      name: data['name'] ?? '',
      parentId: data['parentId'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastActive: (data['lastActive'] as Timestamp?)?.toDate() ?? DateTime.now(),
      storyProgress: storyProgress,
      preferences: Map<String, dynamic>.from(data['preferences'] ?? {}),
      avatarId: data['avatarId'],
      age: data['age'],
    );
  }

  /// Create a profile from a map (e.g., from Firestore)
  factory ChildProfile.fromMap(Map<String, dynamic> map) {
    // Parse story progress
    final progressData = map['storyProgress'] as Map<String, dynamic>? ?? {};
    final storyProgress = <String, StoryProgress>{};
    for (final entry in progressData.entries) {
      storyProgress[entry.key] = StoryProgress.fromMap(entry.value as Map<String, dynamic>);
    }

    return ChildProfile(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      parentId: map['parentId'] ?? '',
      createdAt: map['createdAt'] is Timestamp
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      lastActive: map['lastActive'] is Timestamp
          ? (map['lastActive'] as Timestamp).toDate()
          : DateTime.parse(map['lastActive'] ?? DateTime.now().toIso8601String()),
      storyProgress: storyProgress,
      preferences: Map<String, dynamic>.from(map['preferences'] ?? {}),
      avatarId: map['avatarId'],
      age: map['age'],
    );
  }

  /// Convert to Firestore document data
  Map<String, dynamic> toFirestore() {
    // Convert story progress to map
    final progressData = <String, dynamic>{};
    for (final entry in storyProgress.entries) {
      progressData[entry.key] = entry.value.toMap();
    }

    return {
      'name': name,
      'parentId': parentId,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastActive': Timestamp.fromDate(lastActive),
      'storyProgress': progressData,
      'preferences': preferences,
      'avatarId': avatarId,
      'age': age,
    };
  }

  /// Convert profile to a map (e.g., for Firestore)
  Map<String, dynamic> toMap() {
    // Convert story progress to map
    final progressData = <String, dynamic>{};
    for (final entry in storyProgress.entries) {
      progressData[entry.key] = entry.value.toMap();
    }

    return {
      'id': id,
      'name': name,
      'parentId': parentId,
      'createdAt': createdAt.toIso8601String(),
      'lastActive': lastActive.toIso8601String(),
      'storyProgress': progressData,
      'preferences': preferences,
      'avatarId': avatarId,
      'age': age,
    };
  }

  /// Create a copy of this profile with optional new values
  ChildProfile copyWith({
    String? id,
    String? name,
    String? parentId,
    DateTime? createdAt,
    DateTime? lastActive,
    Map<String, StoryProgress>? storyProgress,
    Map<String, dynamic>? preferences,
    String? avatarId,
    int? age,
  }) {
    return ChildProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      parentId: parentId ?? this.parentId,
      createdAt: createdAt ?? this.createdAt,
      lastActive: lastActive ?? this.lastActive,
      storyProgress: storyProgress ?? this.storyProgress,
      preferences: preferences ?? this.preferences,
      avatarId: avatarId ?? this.avatarId,
      age: age ?? this.age,
    );
  }

  /// Get the last story ID (for backward compatibility)
  String? get lastStoryId {
    if (storyProgress.isEmpty) return null;

    // Find the most recently updated story
    StoryProgress? mostRecent;
    for (final progress in storyProgress.values) {
      if (mostRecent == null || progress.lastUpdated.isAfter(mostRecent.lastUpdated)) {
        mostRecent = progress;
      }
    }

    return mostRecent?.storyId;
  }

  /// Get the last scene ID (for backward compatibility)
  String? get lastSceneId {
    final lastStory = lastStoryId;
    if (lastStory == null) return null;
    return storyProgress[lastStory]?.currentSceneId;
  }

  /// Update story progress
  ChildProfile updateStoryProgress(String storyId, String sceneId, {
    List<String>? completedScenes,
    Map<String, String>? choicesMade,
    bool? isCompleted,
    int? additionalTimeMinutes,
  }) {
    final currentProgress = storyProgress[storyId];
    final newProgress = StoryProgress(
      storyId: storyId,
      currentSceneId: sceneId,
      completedScenes: completedScenes ?? currentProgress?.completedScenes ?? [],
      choicesMade: choicesMade ?? currentProgress?.choicesMade ?? {},
      lastUpdated: DateTime.now(),
      isCompleted: isCompleted ?? currentProgress?.isCompleted ?? false,
      totalTimeMinutes: (currentProgress?.totalTimeMinutes ?? 0) + (additionalTimeMinutes ?? 0),
    );

    final updatedProgress = Map<String, StoryProgress>.from(storyProgress);
    updatedProgress[storyId] = newProgress;

    return copyWith(
      storyProgress: updatedProgress,
      lastActive: DateTime.now(),
    );
  }

  /// Get preference value with default
  T getPreference<T>(String key, T defaultValue) {
    return preferences[key] as T? ?? defaultValue;
  }

  /// Set preference value
  ChildProfile setPreference(String key, dynamic value) {
    final newPreferences = Map<String, dynamic>.from(preferences);
    newPreferences[key] = value;
    return copyWith(
      preferences: newPreferences,
      lastActive: DateTime.now(),
    );
  }

  /// Check if a story is completed
  bool isStoryCompleted(String storyId) {
    return storyProgress[storyId]?.isCompleted ?? false;
  }

  /// Get total time spent across all stories (in minutes)
  int get totalTimeMinutes {
    return storyProgress.values.fold(0, (total, progress) => total + progress.totalTimeMinutes);
  }

  /// Get number of completed stories
  int get completedStoriesCount {
    return storyProgress.values.where((progress) => progress.isCompleted).length;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChildProfile &&
        other.id == id &&
        other.name == name &&
        other.parentId == parentId;
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ parentId.hashCode;
  }

  @override
  String toString() {
    return 'ChildProfile(id: $id, name: $name, parentId: $parentId, '
           'storiesCount: ${storyProgress.length}, completedCount: $completedStoriesCount)';
  }
}
