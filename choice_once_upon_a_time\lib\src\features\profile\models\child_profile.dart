/// Model representing a child's profile in the app
class ChildProfile {
  /// Unique identifier for the profile
  final String id;
  
  /// Child's name
  final String name;
  
  /// ID of the last story the child was reading (null if none)
  String? lastStoryId;
  
  /// ID of the last scene in the story (null if none)
  String? lastSceneId;
  
  /// Constructor
  ChildProfile({
    required this.id,
    required this.name,
    this.lastStoryId,
    this.lastSceneId,
  });
  
  /// Create a profile from a map (e.g., from Firestore)
  factory ChildProfile.fromMap(Map<String, dynamic> map) {
    return ChildProfile(
      id: map['id'] as String,
      name: map['name'] as String,
      lastStoryId: map['lastStoryId'] as String?,
      lastSceneId: map['lastSceneId'] as String?,
    );
  }
  
  /// Convert profile to a map (e.g., for Firestore)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'lastStoryId': lastStoryId,
      'lastSceneId': lastSceneId,
    };
  }
  
  /// Create a copy of this profile with optional new values
  ChildProfile copyWith({
    String? name,
    String? lastStoryId,
    String? lastSceneId,
  }) {
    return ChildProfile(
      id: id,
      name: name ?? this.name,
      lastStoryId: lastStoryId ?? this.lastStoryId,
      lastSceneId: lastSceneId ?? this.lastSceneId,
    );
  }
}
